{"$schema": "https://json.schemastore.org/eslintrc", "root": true, "extends": ["next/core-web-vitals"], "plugins": ["prettier", "react-hooks"], "rules": {"prettier/prettier": ["error", {"useTabs": false, "tabWidth": 2, "singleQuote": true, "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "semi": false, "endOfLine": "lf", "jsxSingleQuote": false, "printWidth": 150}], "import/no-restricted-paths": [2, {"zones": [{"target": "./src/core", "from": "./src/components"}, {"target": "./src/core", "from": "./src/lib"}, {"target": "./src/core", "from": "./src/pages"}, {"target": "./src/lib", "from": "./src/pages"}, {"target": "./src/components", "from": "./src/pages"}]}], "@next/next/no-img-element": 0, "@next/next/no-html-link-for-pages": "off", "@next/next/no-page-custom-font": "off", "arrow-parens": ["error", "as-needed"], "no-trailing-spaces": "error", "no-multiple-empty-lines": ["error", {"max": 1, "maxEOF": 0, "maxBOF": 0}], "react-hooks/rules-of-hooks": "error", "react/hook-use-state": "error"}, "overrides": [{"files": ["**/*.ts", "**/*.tsx"], "extends": ["plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": ["./tsconfig.json"], "ecmaVersion": "latest", "sourceType": "module"}, "rules": {"react-hooks/exhaustive-deps": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-unsafe-function-type": "off", "@typescript-eslint/consistent-type-imports": ["error", {"prefer": "type-imports", "disallowTypeAnnotations": false}], "@typescript-eslint/no-unused-expressions": ["error", {"allowShortCircuit": true, "allowTernary": true, "allowTaggedTemplates": true}]}}]}