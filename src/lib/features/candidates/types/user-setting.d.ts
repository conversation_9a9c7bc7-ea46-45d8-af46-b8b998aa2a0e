export interface InputFieldsSettingParamType {
  display: boolean
  index: number
  locked: boolean
  sort?: string
  display_locked: boolean
  filter?: boolean
  name: {
    en: string
    ja: string
  }
}
export interface InputFieldsSettingType {
  display: boolean
  index: number
  locked: boolean
  sort?: string
  role_ids?: number[]
  field_name: string
  field_kind?: string
  field_level?: string
  custom_field_id?: number
  visibility?: boolean
  display_locked: boolean
  filter?: boolean
  name: {
    en: string
    ja: string
  }
}

export interface IUserSettingResponse {
  id: number
  kind: string
  kindDescription: string
  group: string
  groupDescription: string
  profileColumnsIsDefault: boolean
  placementColumnsIsDefault: boolean
  values: InputFieldsSettingType[]
  profileDisplay: InputFieldsSettingType[]
}

export interface InputUpdateUserSetting {
  id: number
  group: string
  resetDefault?: boolean
  values: InputFieldsSettingType[]
  profileDisplay: InputFieldsSettingType[]
}
