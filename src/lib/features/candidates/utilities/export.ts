import type { TFunction } from 'i18next'
import { jsPDF } from 'jspdf'
import type J<PERSON><PERSON><PERSON> from 'jszip'

import type { IResume } from '~/lib/features/settings/profiles/edit/types'
import { roundedImage, toDataURL } from '~/lib/features/settings/profiles/edit/utilities'
import { DATE_FORMAT_OPTIONS, FONT_SIZE_ENUMS, PROFILE_TEMPLATE_PREVIEW_STYLE } from '~/lib/features/settings/profiles/edit/utilities/enum'

import addTemplatePage from '~/components/Resumes/PreviewTemplates/Templates/utilities/addTemplatePage'
import { renderTemplateDefault } from '~/components/Resumes/PreviewTemplates/Templates/view/templateDefault'
import { renderTemplateHarvard } from '~/components/Resumes/PreviewTemplates/Templates/view/templateHarvard'
import { renderTemplateJapanese } from '~/components/Resumes/PreviewTemplates/Templates/view/templateJapanese'
import { renderTemplateLinkedin } from '~/components/Resumes/PreviewTemplates/Templates/view/templateLinkedin'

const fontPaths: Record<
  string,
  {
    regular: string
    medium: string
    regularItalic: string
    mediumItalic: string
  }
> = {
  ja: {
    regular: '/fonts/NotoSansJP-Regular-NoVN.ttf',
    medium: '/fonts/NotoSansJP-Medium-NoVN.ttf',
    regularItalic: '/fonts/NotoSansJP-Regular-NoVN.ttf',
    mediumItalic: '/fonts/NotoSansJP-Medium-NoVN.ttf'
  },
  default: {
    regular: '/fonts/NotoSansJP-Regular.ttf',
    medium: '/fonts/NotoSansJP-Medium.ttf',
    regularItalic: '/fonts/NotoSans-Italic.ttf',
    mediumItalic: '/fonts/NotoSans-MediumItalic.ttf'
  }
}

export const initPDF = async (language: string) => {
  const pdf = new jsPDF({
    orientation: 'p',
    unit: 'pt',
    format: [595, 842],
    compress: true
  })

  const fonts = fontPaths[language] || fontPaths.default

  await Promise.all([
    pdf.addFont(fonts?.regular as unknown as URL, 'NotoSansJP', 'normal'),
    pdf.addFont(fonts?.medium as unknown as URL, 'NotoSansJP', 'medium'),
    pdf.addFont(fonts?.regularItalic as unknown as URL, 'NotoSansJP', 'normalItalic'),
    pdf.addFont(fonts?.mediumItalic as unknown as URL, 'NotoSansJP', 'mediumItalic')
  ])

  return pdf
}

export const onDownloadPdf = async (resumeData: IResume, fileBlobState: Blob, includeCandidateName?: boolean) => {
  const id = Number(resumeData?.permittedFields?.publicId?.value)
  const content = fileBlobState
  const encodedUri = window.URL.createObjectURL(content)
  const link = document.createElement('a')
  const fullName = (resumeData?.permittedFields?.fullName?.value || '').replace(/[`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi, '')

  const fileSuffix = includeCandidateName ? [fullName, id] : [id]
  const fileNameDownload = resumeData?.cvTemplateSections?.isDefault
    ? ['Resume', ...fileSuffix]
    : [resumeData.cvTemplateSections?.templateName || 'TemplateName', ...fileSuffix]
  link.setAttribute('href', encodedUri)
  link.setAttribute('download', fileNameDownload.map(item => item).join('_'))
  link.click()
}

export const generatePdf = (pdf?: jsPDF, templateData?: any, t?: TFunction) => {
  if (pdf) {
    const {
      templateStyle,
      user,
      locale,
      resumeData,
      columns,
      keys,
      percentScale,
      customFieldViewData,
      listDegrees,
      profileLevel,
      companyName,
      currentFontSize,
      dateFormatContent,
      pdfPosition,
      currentFont
    } = templateData
    pdf.setFont(currentFont, 'normal')
    pdf.setTextColor('18191E')
    addTemplatePage({
      isAdd: false,
      pdf,
      watermark: '',
      fontSize: currentFontSize
    })

    const position = pdfPosition || 0

    switch (templateStyle) {
      case PROFILE_TEMPLATE_PREVIEW_STYLE.harvard:
        renderTemplateHarvard({
          user,
          pdf,
          pdfPosition: position,
          locale,
          columns,
          resumeData,
          keys,
          percentScale,
          customFieldViewData,
          listDegrees,
          profileLevel,
          companyName,
          currentFontSize: currentFontSize || FONT_SIZE_ENUMS.small,
          dateFormatContent: dateFormatContent || DATE_FORMAT_OPTIONS[0],
          currentFont
        })
        break

      case PROFILE_TEMPLATE_PREVIEW_STYLE.japanese:
        renderTemplateJapanese({
          user,
          pdf,
          pdfPosition: position,
          locale,
          columns,
          resumeData,
          keys,
          customFieldViewData,
          listDegrees,
          profileLevel,
          companyName,
          currentFontSize: currentFontSize || FONT_SIZE_ENUMS.small,
          dateFormatContent: dateFormatContent || DATE_FORMAT_OPTIONS[0],
          currentFont,
          t
        })
        break

      case PROFILE_TEMPLATE_PREVIEW_STYLE.linkedin:
        renderTemplateLinkedin({
          user,
          pdf,
          pdfPosition: position,
          locale,
          columns,
          resumeData,
          keys,
          percentScale,
          customFieldViewData,
          listDegrees,
          profileLevel,
          companyName,
          currentFontSize: currentFontSize || FONT_SIZE_ENUMS.small,
          dateFormatContent: dateFormatContent || DATE_FORMAT_OPTIONS[0],
          currentFont
        })
        break

      default:
        renderTemplateDefault({
          user,
          pdf,
          pdfPosition: position,
          locale,
          columns,
          resumeData,
          keys,
          percentScale,
          customFieldViewData,
          listDegrees,
          profileLevel,
          companyName,
          currentFontSize: currentFontSize || FONT_SIZE_ENUMS.small,
          dateFormatContent: dateFormatContent || DATE_FORMAT_OPTIONS[0],
          currentFont
        })
        break
    }

    return pdf
  }
  return null
}

export const resetPDF = (pdf: jsPDF) => {
  const numberPages = pdf.getNumberOfPages()
  for (let pageNumber = numberPages; pageNumber >= 1; pageNumber--) {
    pdf.deletePage(pageNumber)
  }
  addTemplatePage({
    isAdd: true,
    pdf,
    watermark: ''
  })
}

export const downloadZipFile = async (fileZip: JSZip, fileName: string) => {
  const zipData = await fileZip.generateAsync({
    type: 'blob',
    streamFiles: true
  })
  // Create a download link for the zip file
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(zipData)
  link.download = fileName
  link.click()
}

export const getBase64FromImageURL = async (endpoint: { url: string; logoBase64?: boolean; avatarBase64?: boolean }) => {
  const imageBase64 = {
    avatarBase64: '',
    avatarRoundedBase64: '',
    logoBase64: ''
  }
  const response = await toDataURL(endpoint.url)
  if (endpoint.logoBase64) {
    imageBase64.logoBase64 = response as string
    return imageBase64
  }

  imageBase64.avatarBase64 = response as string
  const drawAvatarRounded = await roundedImage(response as string, 200, 200)
  imageBase64.avatarRoundedBase64 = drawAvatarRounded
  return imageBase64
}

export async function getsBase64FromImageURL(
  endpoints: Array<{
    url: string
    logoBase64?: boolean
    avatarBase64?: boolean
  }>
) {
  const imageBase64 = {
    avatarBase64: '',
    avatarRoundedBase64: '',
    logoBase64: ''
  }

  const promises = endpoints.map(getBase64FromImageURL)
  const responses = await Promise.all(promises)

  responses.map(response => {
    if (response.logoBase64) imageBase64.logoBase64 = response.logoBase64
    if (response.avatarBase64) imageBase64.avatarBase64 = response.avatarBase64
    if (response.avatarRoundedBase64) imageBase64.avatarRoundedBase64 = response.avatarRoundedBase64
  })
  return imageBase64
}
