import { gql } from 'urql'

import type { TemplateResponseType } from '../types/editor'

const QueryCareersTemplateShow = gql<
  {
    careerTemplatesShow: TemplateResponseType
  },
  {}
>`
  query {
    careerTemplatesShow {
      id
      name
      references
      setting {
        images {
          file
          key
        }
      }
      sections {
        id
        name
        key
        index
        locked
        hidden
        hiddenChangable
        sectionableType
        fields {
          id
          index
          required
          key
          name
          fieldKind
          values
          images {
            id
            file
          }
          sections {
            id
            name
            key
            index
            locked
            hidden
            hiddenChangable
            sectionableType
            fields {
              id
              index
              required
              key
              name
              fieldKind
              values
              images {
                id
                file
              }
            }
          }
        }
      }
    }
  }
`

export default QueryCareersTemplateShow
