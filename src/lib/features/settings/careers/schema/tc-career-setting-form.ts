import type { TFunction } from 'i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

const schemaCareerTermAndConditionForm = (t: TFunction) => {
  return z.object({
    termsAndConditions: z
      .object({
        enabling: z.boolean(),
        description: z.string().optional()
      })
      .refine(
        TandC => {
          return !TandC.enabling || (TandC.description || '')?.length > 0
        },
        `${t('form:requiredField')}`
      )
      .refine(
        TandC => {
          return !TandC.enabling || removeHTMLTags(TandC.description || '').length <= 10000
        },
        {
          message: `${t('form:field_max_number_required', { number: 10000 })}`
        }
      )
  })
}

export default schemaCareerTermAndConditionForm
