import type { TFunction } from 'next-i18next'
import { z } from 'zod'

import type { FieldType } from '../types/editor'

const schemaCareerTemplateFieldDynamic = ({ t, field }: { t: TFunction; field: FieldType }) => {
  return {
    string: z
      .string()
      .optional()
      .refine(value => (field.required ? (value || '').trim().length > 0 : true), {
        message: `${t('form:requiredField')}`
      })
      .refine(value => !value || (field?.keyName === 'title' || field?.keyName === 'question' ? value?.length <= 80 : value?.length <= 160), {
        message: `${t('form:field_max_number_required', {
          number: field?.keyName === 'title' || field?.keyName === 'question' ? 80 : 160
        })}`
      }),
    text: z
      .string()
      .max(1000, {
        message: `${t('form:field_max_number_required', {
          number: 1000
        })}`
      })
      .optional(),
    mult_lang_text: z.object({
      en: z
        .string()
        .max(1000, {
          message: `${t('form:field_max_number_required', {
            number: 1000
          })}`
        })
        .optional(),
      ja: z
        .string()
        .max(1000, {
          message: `${t('form:field_max_number_required', {
            number: 1000
          })}`
        })
        .optional()
    }),
    file: z
      .array(
        z.object({
          id: z.string().optional(),
          img: z.any(),
          imgBase64: z.string().optional(),
          status: z.string(),
          errorType: z.string().optional()
        })
      )
      .refine(files => {
        return files?.[0]?.status !== 'failed'
      })
      .optional(),
    files: z
      .array(
        z.object({
          id: z.string().optional(),
          img: z.any(),
          imgBase64: z.string().optional(),
          status: z.string(),
          errorType: z.string().optional()
        })
      )
      .optional(),
    icon_dropdown: z.string().optional()
  }[field.fieldKind]
}

export default schemaCareerTemplateFieldDynamic
