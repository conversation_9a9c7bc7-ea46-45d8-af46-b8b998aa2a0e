import type { TFunction } from 'i18next'
import { z } from 'zod'

const schemaCreateDomain = (t: TFunction) => {
  // return z.object({
  //   domain: z.string().trim().optional()
  // })
  return z.object({
    name: z
      .string()
      .trim()
      .min(1, `${t('settings:customDomain:form:domain_required')}`) // Required
      .regex(/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}([\/\w.-]*)*$/, `${t('settings:customDomain:form:domain_invalid')}`)
  })
}

export default schemaCreateDomain
