import type { i18n } from 'i18next'

import type { CareerSettingType, ICareerSettingForm } from '../types'
import { DEPARTMENT_SHOW_ALL, LANGUAGES_VALUE } from '../utilities/enum'

export const mappingCareerFormDefaultValue = (data?: CareerSettingType, i18n?: i18n): ICareerSettingForm => ({
  pageTitle: data?.page_title || '',
  ...(data?.description && typeof data?.description !== 'string'
    ? {
        description: data?.description
      }
    : {
        description: {
          en: '',
          ja: ''
        }
      }),
  departmentVisibility: data?.department_visibility || DEPARTMENT_SHOW_ALL,
  gaMeasurementId: data?.ga_measurement_id || '',
  ...(data?.languages
    ? {
        languages: data.languages
      }
    : {
        languages: {
          [LANGUAGES_VALUE.english]: {
            enable: i18n?.language === LANGUAGES_VALUE.english,
            default: i18n?.language === LANGUAGES_VALUE.english
          },
          [LANGUAGES_VALUE.japanese]: {
            enable: i18n?.language === LANGUAGES_VALUE.japanese,
            default: i18n?.language === LANGUAGES_VALUE.japanese
          }
        }
      })
})
