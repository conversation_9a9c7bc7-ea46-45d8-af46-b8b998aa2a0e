import type { ILogoAndAvatarVariants } from '~/core/@types/global'

export interface ICareerSettingForm {
  pageTitle: string
  description: { [key: string]: string }
  departmentVisibility: string
  gaMeasurementId: string
  languages: {
    [key: string]: { enable: boolean; default: boolean }
  }
}

export interface CareerSettingType {
  canonical_url: string
  description?: {
    [key: string]: string
  }
  logoVariants?: ILogoAndAvatarVariants
  page_title?: string
  department_visibility?: string
  ga_measurement_id?: string
  languages?: {
    [key: string]: {
      enable: boolean
      default: boolean
    }
  }
  enablingCareerSiteSetting?: boolean
  terms_and_conditions: {
    enable: boolean
    description: string
  }
  terms_and_conditions: ITermsAndConditions
}

export interface ITermsAndConditions {
  enabling: boolean
  description?: string
}
