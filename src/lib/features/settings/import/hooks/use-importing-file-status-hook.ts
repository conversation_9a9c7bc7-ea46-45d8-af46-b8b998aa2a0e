'use client'

import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'

import configuration from '~/configuration'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import QueryJobImportShow from '../graphql/query-job-import-show'
import QueryProfileImportShow from '../graphql/query-profile-import-show'
import QueryTenantCourseImportShow from '../graphql/query-tenant-course-import-show'
import type { ImportFileStatus, ISourceImportType } from '../types'
import { ENUMS_IMPORT_TYPE } from '../utilities/enum'

const useImportingFileStatusHook = ({ importType }: { importType?: ISourceImportType }) => {
  const router = useRouter()
  const { searchParams } = useRouterContext()
  const import_id = searchParams?.get('import_id')
  const object_kind = searchParams?.get('object_kind')
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const clientGraphQL = useGraphQLRequest({ language: user?.language })
  const [importFileStatus, setImportFileStatus] = useState<undefined | ImportFileStatus>()

  const [step, setStep] = useState<undefined | number>() //maximum step is 3; count from index 0

  const fetchImportedFileData = useCallback(async (param: { id: number }) => {
    return clientGraphQL
      .query(QueryJobImportShow, param)
      .toPromise()
      .then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.import,
            setToast
          })

          return undefined
        }

        return result.data?.jobImportsShow
      })
  }, [])

  const fetchImportedProfileFileData = useCallback(async (param: { id: number }) => {
    return clientGraphQL
      .query(QueryProfileImportShow, param)
      .toPromise()
      .then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.import,
            setToast
          })

          return undefined
        }

        return result.data?.profileImportsShow
      })
  }, [])

  const fetchImportedTenantCourseFileData = useCallback(async (param: { id: number }) => {
    return clientGraphQL
      .query(QueryTenantCourseImportShow, param)
      .toPromise()
      .then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.import,
            setToast
          })

          return undefined
        }

        return result.data?.tenantCourseImportsShow
      })
  }, [])

  const nextStep = useCallback(() => {
    if (typeof step === 'number') {
      const nextStepCount = step + 1

      setStep(nextStepCount)
      if (nextStepCount === 2) {
        setImportFileStatus('in_progress')
      }
    }
  }, [step])

  const previousStep = useCallback(() => {
    if (typeof step === 'number') {
      const previousStepCount = step - 1
      if (step > 0 && step <= 2) {
        setStep(previousStepCount)
        if (previousStepCount !== 2) setImportFileStatus(undefined)
      }
    }
  }, [step])

  const backToFirstStep = useCallback(() => {
    setImportFileStatus(undefined)
    setStep(0)
    router.push(`${configuration.path.settings.import}?tab=import&object_kind=${object_kind}`)
  }, [])

  useEffect(() => {
    if (import_id) {
      if (importType?.value === ENUMS_IMPORT_TYPE.jobs) {
        fetchImportedFileData({ id: Number(import_id) }).then(importedFile => {
          setImportFileStatus(importedFile?.status)
        })
      } else if (importType?.value === ENUMS_IMPORT_TYPE.candidate) {
        // if (import_type === 'candidate') {
        fetchImportedProfileFileData({
          id: Number(import_id)
        }).then(importedFile => {
          setImportFileStatus(importedFile?.status)
        })
        // }
      } else {
        fetchImportedTenantCourseFileData({
          id: Number(import_id)
        }).then(importedFile => {
          setImportFileStatus(importedFile?.status)
        })
      }
    } else {
      setImportFileStatus(undefined)
      setStep(0)
    }
  }, [import_id])

  return {
    currentStep: step,
    importFileStatus,
    nextStep,
    previousStep,
    backToFirstStep
  }
}

export default useImportingFileStatusHook
