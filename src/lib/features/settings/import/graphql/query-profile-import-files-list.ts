import { gql } from 'urql'

import type { ImportFileType } from '../types'

const QueryProfileImportFilesList = gql<
  {
    profileImportsList: {
      collection: Array<ImportFileType>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    page?: number
    limit?: number
  }
>`
  query ($limit: Int, $page: Int) {
    profileImportsList(limit: $limit, page: $page) {
      collection {
        id
        name
        file
        uploadedBy {
          fullName
          avatarVariants
          defaultColour
        }
        importedCount
        rowsCount
        status
        statusDescription
        createdAt
        objectKindDescription
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryProfileImportFilesList
