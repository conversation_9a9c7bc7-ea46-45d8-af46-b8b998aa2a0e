import { gql } from 'urql'

import type { CourseFieldType } from '../types'

const QueryTenantCourseFields = gql<
  {
    profileFieldsList: {
      collection: Array<CourseFieldType>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    page?: number
    limit?: number
    search?: string
  }
>`
  query ($limit: Int, $page: Int, $search: String, $mappedFields: [JSON!]) {
    tenantCourseFieldsList(limit: $limit, page: $page, search: $search, mappedFields: $mappedFields) {
      collection {
        field
        name
        required
        uuid
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryTenantCourseFields
