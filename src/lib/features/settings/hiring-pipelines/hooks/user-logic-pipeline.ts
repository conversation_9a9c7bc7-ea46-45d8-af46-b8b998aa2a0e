import { useTranslation } from 'react-i18next'

import useStaticData from 'src/hooks/data/use-static-data'
import { openAlert } from '~/core/ui/AlertDialog'

import type { IStageType } from '~/lib/features/jobs/types'
import { JOB_STAGE_GROUP } from '~/lib/features/jobs/utilities/enum'

import type { IPipelineItem, IPipelineTemplate, IStageItem } from '../types'
import { stageGroupInfoLocal } from '../utilities/common'

export function useLogicPipelineTemplate() {
  const { t, i18n } = useTranslation()
  const stageTypes = useStaticData({
    keyName: 'agency_stageTypes',
    locale: i18n.language
  })

  const mappingSubmitDataEdit = (id: number, name: string, pipelineStages: IPipelineItem[]) => {
    return {
      id,
      name,
      pipelineStages: [
        ...pipelineStages?.flatMap(i =>
          i.stages.map((s, index) => ({
            // index: i.value === 'active_process' ? index : undefined,
            id: s.id?.includes('item') ? undefined : s.id,
            stageLabel: s.stage,
            clientShared: s?.clientShared,
            stageTypeId: s.stageType?.value
          }))
        ),
        ...pipelineStages?.flatMap(i =>
          (i?.stagesDeleted || []).map((s, index) => ({
            id: s.id?.includes('item') ? undefined : s.id,
            _destroy: true,
            stageLabel: s.stage,
            clientShared: s?.clientShared,
            stageTypeId: s.stageType?.value
          }))
        )
      ].map((p, index) => ({ ...p, index }))
    }
  }

  const mappingSubmitDataAddNew = (name: string, pipelineStages: IPipelineItem[]) => {
    return {
      name,
      pipelineStages: pipelineStages
        ?.flatMap((i, index) =>
          i.stages.map(s => ({
            // index: i.value === 'active_process' ? index : undefined,
            stageLabel: s.stage,
            clientShared: s?.clientShared,
            stageTypeId: s.stageType?.value
          }))
        )
        .map((p, index) => ({ ...p, index }))
    }
  }

  const transformData4Form = (stageGroup: any, item: IPipelineTemplate) => {
    const pipelineStages: Array<IPipelineItem> = []
    if (item) {
      stageGroup.forEach(({ value, supportingObj }: { value: string; supportingObj: { name: string } }) => {
        const group = {
          disabled: !!stageGroupInfoLocal[value]?.disabled,
          index: Number(stageGroupInfoLocal[value]?.position),
          name: supportingObj.name,
          value: value,
          toolTipLabel: `${t(`job:pipeline:${value}`)}`,
          stages: (item?.pipelineStages || [])
            .filter((stage: IPipelineItem) => stage.stageGroup === value && !stage._destroy)
            .map((stage: IPipelineItem, index: number) => ({
              id: item?.name !== '' ? stage.id : `item-${index}`,
              stage: stage.stageLabel,
              stageGroup: value,
              clientShared: stage?.clientShared,
              stageType: stage.stageType?.id
                ? {
                    value: stageTypes.filter((item: IStageType) => String(item.id) === String(stage?.stageType?.id))?.[0]?.id,
                    supportingObj: {
                      name: stageTypes.filter((item: { id: string }) => String(item.id) === String(stage?.stageType?.id))?.[0]?.label
                    }
                  }
                : undefined,
              locked: !stage?.updateable
            })),
          stagesDeleted: (item?.pipelineStages || [])
            .filter((stage: IPipelineItem) => stage.stageGroup === value && stage._destroy)
            .map((stage: IPipelineItem) => ({
              id: stage.id
            }))
        }
        pipelineStages.push(group)
      })
    }

    return {
      id: item?.id,
      name: item?.name,
      default: item?.default,
      pipelineStages: pipelineStages.sort((a: { index: number }, b: { index: number }) => a.index - b.index)
    }
  }

  const findPipelineIndexByValue = (pipelines: IPipelineItem[], value: string): number => {
    return pipelines.findIndex(pipeline => pipeline.value === value)
  }

  const onUpdateInfoStage = (pipelines: IPipelineItem[], value: IStageItem, onChange: (pipelines: IPipelineItem[]) => void, areaType?: string) => {
    const pipelineIndex = findPipelineIndexByValue(pipelines, areaType || JOB_STAGE_GROUP.activeProcess)
    if (pipelineIndex !== -1 && pipelines[pipelineIndex]) {
      pipelines[pipelineIndex].stages = pipelines[pipelineIndex].stages.map(pipeline =>
        pipeline.id === value.id
          ? {
              ...pipeline,
              stage: value.stage,
              clientShared: value?.clientShared,
              stageType: value.stageType
            }
          : pipeline
      )
      onChange(pipelines)
    }
  }

  const onDeleteStage = (pipelines: IPipelineItem[], value: IStageItem, onChange: (pipelines: IPipelineItem[]) => void, areaType?: string) => {
    const pipelineIndex = findPipelineIndexByValue(pipelines, areaType || JOB_STAGE_GROUP.activeProcess)
    if (pipelineIndex !== -1) {
      const findDeleteItemIndex = pipelines[pipelineIndex]?.stages.findIndex(stage => stage.id === value.id && !value.id?.includes('item'))
      if (findDeleteItemIndex !== -1 && pipelines[pipelineIndex]) {
        pipelines[pipelineIndex].stagesDeleted = [
          ...(pipelines[pipelineIndex]?.stagesDeleted || []),
          pipelines[pipelineIndex]?.stages?.[Number(findDeleteItemIndex)] as IStageItem
        ]
      }
      if (pipelines[pipelineIndex]) {
        pipelines[pipelineIndex].stages = pipelines[pipelineIndex]?.stages.filter(pipeline => pipeline.id !== value.id)
      }
      onChange(pipelines)
    }
  }

  const onReOrderStage = (
    pipelines: IPipelineItem[],
    stages: IStageItem[],
    onChange: (pipelines: IPipelineItem[]) => void,
    areaType?: string
  ): void => {
    const pipelineIndex = findPipelineIndexByValue(pipelines, areaType || JOB_STAGE_GROUP.activeProcess)

    if (pipelineIndex !== -1 && pipelines[pipelineIndex]) {
      pipelines[pipelineIndex].stages = stages
      onChange(pipelines)
    }
  }

  const onAddStage = (pipelines: IPipelineItem[], stages: IStageItem[], onChange: (pipelines: IPipelineItem[]) => void, areaType?: string): void => {
    const pipelineIndex = findPipelineIndexByValue(pipelines, areaType || JOB_STAGE_GROUP.activeProcess)
    if (pipelineIndex !== -1 && pipelines[pipelineIndex]) {
      pipelines[pipelineIndex].stages = [...(pipelines[pipelineIndex]?.stages || []), ...stages]

      const groupMap = new Map<string, IStageItem[]>()
      pipelines[pipelineIndex].stages.forEach(item => {
        if (item.stageType?.value) {
          const groupKey = item.stageType?.value
          if (!groupMap.has(groupKey)) {
            groupMap.set(groupKey, [])
          }
          groupMap.get(groupKey)!.push(item)
        }
      })
      const sortedData: IStageItem[] = []
      groupMap.forEach(group => {
        group.sort((a, b) => {
          if (a?.stageType?.value && b?.stageType?.value) {
            return parseInt(a.stageType.value) - parseInt(b.stageType.value)
          }
          return 0
        })
        sortedData.push(...group)
      })
      pipelines[pipelineIndex].stages = [...sortedData]
      onChange(pipelines)
    }
  }

  const onShowAlertClose4HiringPipelineFormModal = (funcCallBack: () => void) => {
    return openAlert({
      isPreventAutoFocusDialog: false,
      className: 'w-[480px]',
      title: `${t('common:modal:discard_unsaved_changes_title')}`,
      description: t('common:modal:discard_unsaved_changes_description'),
      actions: [
        {
          label: `${t('button:keepEditing')}`,
          type: 'secondary',
          size: 'sm'
        },
        {
          label: `${t('button:discard')}`,
          type: 'destructive',
          size: 'sm',
          onClick: async e => {
            funcCallBack()
          }
        }
      ]
    })
  }

  const onShowAlertConfirmDelete = (
    relateApplicantsData:
      | {
          qualifiedCount: number
          disQualifiedCount: number
        }
      | undefined,
    value: IStageItem,
    pipelines: IPipelineItem[],
    onChange: (pipelines: IPipelineItem[]) => void,
    areaType?: string
  ) => {
    if (relateApplicantsData) {
      openAlert({
        className: 'w-[480px]',
        title: `${t('common:modal:delete_stage_title')}`,
        description: t('common:modal:delete_stage_description_01', {
          qualifiedCount: relateApplicantsData?.qualifiedCount || 0,
          disQualifiedCount: relateApplicantsData?.disQualifiedCount || 0
        }),
        actions: [
          {},
          {
            label: `${t('button:close')}`,
            type: 'primary',
            size: 'sm',
            onClick: async e => {}
          }
        ]
      })
    } else {
      openAlert({
        className: 'w-[480px]',
        title: `${t('common:modal:delete_stage_title')}`,
        description: t('common:modal:delete_stage_description', {
          title: value.stage
        }),
        actions: [
          {
            label: `${t('button:cancel')}`,
            type: 'secondary',
            size: 'sm'
          },
          {
            isCallAPI: true,
            label: `${t('button:delete')}`,
            type: 'destructive',
            size: 'sm',
            onClick: async e => {
              await onDeleteStage(pipelines, value, onChange, areaType)
            }
          }
        ]
      })
    }
  }

  return {
    onShowAlertClose4HiringPipelineFormModal,
    transformData4Form,
    mappingSubmitDataEdit,
    mappingSubmitDataAddNew,
    onUpdateInfoStage,
    onDeleteStage,
    onReOrderStage,
    onAddStage,
    onShowAlertConfirmDelete
  }
}
