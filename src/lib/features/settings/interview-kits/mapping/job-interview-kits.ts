import type { ISelectOption } from '~/core/ui/Select'

import type { IStageType, IStageTypes } from '~/lib/features/jobs/types'
import { JOB_COLOR_STAGE_NAME } from '~/lib/features/jobs/utilities/enum'

import type { IJobInterviewKit } from '../types'

export const mappingSubmitDataJobInterviewKits = ({
  ikitTemplateIds,
  jobStageIds
}: {
  ikitTemplateIds: ISelectOption[]
  jobStageIds: ISelectOption
}) => {
  return {
    ikitTemplateIds: (ikitTemplateIds || []).map((item: ISelectOption) => Number(item.value)),
    jobStageIds: jobStageIds?.value ? [Number(jobStageIds.value)] : undefined
  }
}

export const mappingEditDataJobInterviewKits = (data?: IJobInterviewKit, stageTypes?: IStageTypes) => {
  if (!data) return undefined

  return {
    id: data.id,
    name: data.name,
    guideline: data.guideline || '',
    jobStageIds: data.jobStages?.length
      ? {
          value: data.jobStages?.[0]?.id,
          dot: JOB_COLOR_STAGE_NAME(
            String((stageTypes || []).filter((s: IStageType) => String(s.id) === String(data.jobStages?.[0]?.stageTypeId))?.[0]?.colorClassName)
          ),
          supportingObj: { name: data.jobStages?.[0]?.stageLabel }
        }
      : undefined,
    ikitSessions: (data.jobIkitSessions || []).map(item => {
      return {
        id: item.id,
        name: item.name,
        position: item.position,
        metrics: (item.jobIkitMetrics || [])
          .filter(item => item.name)
          .map(s => {
            return {
              id: s.id,
              name: s.name
            }
          }),
        questions: (item.jobIkitQuestions || [])
          .filter(item => item.content)
          .map(s => {
            return {
              id: s.id,
              content: s.content
            }
          })
      }
    })
  }
}
