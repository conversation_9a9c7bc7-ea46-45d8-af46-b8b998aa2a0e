import type { IInterviewKitTemplate, IInterviewKitTemplateForm } from '../types'

export const mappingSubmitData = (data: IInterviewKitTemplateForm) => {
  return {
    id: data?.id ? Number(data.id) : undefined,
    name: data.name,
    guideline: data.guideline,
    ikitSessions: (data.ikitSessions || []).map(item => {
      return {
        id: item.id,
        name: item.name,
        position: item.position,
        metrics: (item.metrics || [])
          .filter(item => item.name)
          .map(s => {
            return {
              id: s.id,
              name: s.name
            }
          }),
        questions: (item.questions || [])
          .filter(item => item.content)
          .map(s => {
            return {
              id: s.id,
              content: s.content
            }
          })
      }
    })
  }
}

export const mappingEditDataInterviewKitsTemplate = (data?: IInterviewKitTemplate) => {
  if (!data) return undefined

  return {
    id: data.id,
    name: data.name,
    guideline: data.guideline || '',
    ikitSessions: (data.ikitTemplateSessions || []).map(item => {
      return {
        id: item.id,
        name: item.name,
        position: item.position,
        metrics: (item.ikitTemplateMetrics || [])
          .filter(item => item.name)
          .map(s => {
            return {
              id: s.id,
              name: s.name
            }
          }),
        questions: (item.ikitTemplateQuestions || [])
          .filter(item => item.content)
          .map(s => {
            return {
              id: s.id,
              content: s.content
            }
          })
      }
    })
  }
}
