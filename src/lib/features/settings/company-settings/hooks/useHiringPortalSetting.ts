import { useCallback, useEffect, useMemo, useState } from 'react'

import { AGENCY_TENANT } from '~/core/constants/enum'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { accessHiringPortalFeature } from '~/core/utilities/feature-permission'
import { adminAndMemberCanAction } from '~/core/utilities/permission'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import QueryTenantHiringPortalSettingShow from '../graphql/query-tenant-hiring-portal-setting'
import type { IHiringPortalSettingType } from '../types'

const useHiringPortalSetting = ({ isFetchSetting }: { isFetchSetting?: boolean } = {}) => {
  const { clientGraphQL } = useContextGraphQL()
  const { currentRole, featureSetting } = useBoundStore()
  const { isFeatureEnabled } = useSubscriptionPlan()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const [data, setData] = useState<IHiringPortalSettingType>()

  const enableHiringPortal = useMemo(() => {
    if (!adminAndMemberCanAction(currentRole?.code)) {
      return false
    }
    if (isCompanyKind) {
      return true
    }

    return !!isFeatureEnabled(PLAN_FEATURE_KEYS.company) && accessHiringPortalFeature(featureSetting)
  }, [currentRole?.code, featureSetting, isCompanyKind, isFeatureEnabled])

  const fetchSetting = useCallback(() => {
    return clientGraphQL
      .query(QueryTenantHiringPortalSettingShow)
      .then((result: { error: { graphQLErrors: Array<object> }; data: { tenantHiringPortalSettingShow: IHiringPortalSettingType } }) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error
          })
        }
        setData(result.data?.tenantHiringPortalSettingShow || [])
        return
      })
  }, [clientGraphQL])

  useEffect(() => {
    isFetchSetting && fetchSetting()
  }, [isFetchSetting])

  return {
    enableHiringPortal,
    hiringPortalSetting: data,
    fetchHiringPortalSetting: fetchSetting
  }
}
export default useHiringPortalSetting
