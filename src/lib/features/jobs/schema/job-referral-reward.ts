import type { TFunction } from 'i18next'
import { z } from 'zod'

import { REFERRAL_REWARD_MONEY_VALUE, REFERRAL_REWARD_OTHER_VALUE } from '../utilities/enum'

const schemaReferralRewardForm = (t: TFunction) => {
  return z
    .object({
      referralRewardType: z.string().optional(),
      rewardAmount: z.coerce
        .number() // Force it to be a number
        .int() // Make sure it's an integer
        .gte(1, { message: `${t('form:numberMustBeGreater', { number: 1 })}` })
        .optional(),
      rewardCurrency: z.string().optional(),
      rewardGift: z
        .string()
        .max(20, {
          message: `${t('form:field_max_number_required', { number: 20 })}`
        })
        .nullable()
        .optional()
    })
    .refine(
      data => {
        if (data.referralRewardType === REFERRAL_REWARD_MONEY_VALUE) {
          return (data.rewardAmount?.toString().trim() || '').length > 0
        } else return true
      },
      {
        message: `${t('form:requiredField')}`,
        path: ['rewardAmount']
      }
    )
    .refine(
      data => {
        return (data.rewardAmount?.toString().trim() || '').length <= 8
      },
      {
        message: `${t('form:field_max_digits_required', { digits: 8 })}`,
        path: ['rewardAmount']
      }
    )
    .refine(
      data => {
        if (data.referralRewardType === REFERRAL_REWARD_OTHER_VALUE) {
          return (data.rewardGift?.trim() || '').length > 0
        } else return true
      },
      {
        message: `${t('form:requiredField')}`,
        path: ['rewardGift']
      }
    )
}

export default schemaReferralRewardForm
