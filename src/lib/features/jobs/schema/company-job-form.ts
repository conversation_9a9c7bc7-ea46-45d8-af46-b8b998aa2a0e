import type { TFunction } from 'next-i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

import { schemaCustomField } from '../../settings/profile-fields/schema/custom-field-schema'

const schemaCompanyJobForm = (t: TFunction) => {
  return z
    .object({
      title: z
        .string()
        .trim()
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .max(80, {
          message: `${t('form:field_max_number_required', { number: 80 })}`
        }),
      companyId: z
        .object({
          value: z.string(),
          supportingObj: z.object({
            name: z.string()
          })
        })
        .nullish()
        .refine(obj => !!obj?.value, {
          message: `${t('form:requiredField')}`
        }),
      status: z.string().optional(),
      remoteStatus: z.string().optional(),
      ownerId: z.array(z.object({ value: z.string() }).nullable()).optional(),
      description: z
        .string()
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .refine(
          description => {
            return removeHTMLTags(description || '').length <= 10000
          },
          {
            message: `${t('form:field_max_number_required', { number: 10000 })}`
          }
        ),
      pitch: z
        .string()
        .refine(pitch => removeHTMLTags(pitch || '').length <= 500, {
          message: `${t('form:field_max_number_required', { number: 500 })}`
        })
        .optional(),
      salaryFrom: z
        .string()
        // .max(10, {
        //   message: `${t('form:field_max_digits_required', { digits: 10 })}`
        // })
        .optional(),
      salaryTo: z
        .string()
        // .max(10, {
        //   message: `${t('form:field_max_digits_required', { digits: 10 })}`
        // })
        .optional(),
      currency: z.string().optional(),
      employmentType: z.string().optional(),
      typeOfSalary: z.string().optional(),
      hiringManagerIds: z.array(z.object({ value: z.string() }).nullable()).optional(),
      industryIds: z.array(z.object({ value: z.string() })).optional(),
      locationIds: z.array(z.object({ value: z.string() })).min(1, {
        message: `${t('form:requiredField')}`
      }),
      jobCategoryId: z.any(),
      education: z.string().optional(),
      jobLevel: z.string().optional(),
      skills: z
        .array(
          z.object({
            value: z.string()
          })
        )
        .optional(),
      languages: z
        .array(
          z.object({
            value: z.string(),
            supportingObj: z.object({
              name: z.string()
            })
          })
        )
        .nullish()
        .optional(),
      jobTalentPoolIds: z
        .array(
          z.object({
            value: z.string()
          })
        )
        .optional(),
      headcount: z
        .string()
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .max(3, {
          message: `${t('form:max_number_field', { number: 999 })}`
        })
        .optional(),
      hiringProcess: z
        .object(
          {
            id: z.string().optional(),
            name: z.string().optional(),
            pipelineStages: z.array(
              z.object({
                id: z.string().optional(),
                stageLabel: z.string().optional(),
                clientShared: z.boolean().optional(),
                _destroy: z.boolean().optional(),
                stageType: z
                  .object({
                    id: z.string().or(z.number())
                  })
                  .optional()
              })
            )
          },
          {
            required_error: `${t('form:requiredField')}`
          }
        )
        .nullish()
        .refine(obj => !!obj, {
          message: `${t('form:requiredField')}`
        }),
      referenceId: z.nullable(z.string()).optional(),
      taggedIds: z
        .array(
          z.object({
            value: z.string(),
            supportingObj: z.object({
              name: z.string()
            })
          })
        )
        .max(5, {
          message: `${t('form:maximum_tags_job', {
            num: 5
          })}`
        })
        .nullish()
        .optional()
    })
    .and(schemaCustomField(t))
    .refine(
      data => {
        if (!data.salaryFrom || !data.salaryTo) return true

        return Number(data.salaryFrom) <= Number(data.salaryTo)
      },
      data => ({
        message: `${t('form:this_number_should_be_lesser_number_field_to')}`,
        path: ['salaryFrom']
      })
    )
    .refine(
      data => {
        if (!data.salaryFrom || !data.salaryTo) return true

        return Number(data.salaryFrom) <= Number(data.salaryTo)
      },
      data => ({
        message: `${t('form:this_number_should_be_greater_number_field_from')}`,
        path: ['salaryTo']
      })
    )
}

export default schemaCompanyJobForm
