import type { IUserInformation } from '~/core/@types/global'
import { formatAddressLocation, trimFirstContentBreakLine } from '~/core/utilities/common'

import { idPipelineCustom } from '~/components/Jobs/JobHiringView'

import { formatInitialValueCustomField, formatSubmitCustomFieldData } from '../../settings/profile-fields/mapping/custom-field-mapping'
import type { IJobDetailForm, IJobForm, IJobStage } from '../types'
import { JOB_STATUS_ENUM } from '../utilities/enum'
import { mappingLanguagesData } from './add-job-mapping'

export const mappingDuplicateCompanyJob = (data: IJobDetailForm, user: IUserInformation) => {
  const jobData = mappingEditCompanyJob(data)
  delete jobData.id
  return {
    ...jobData,
    title: data.title + ' (2)',
    ownerId: [
      {
        value: user.id?.toString(),
        supportingObj: {
          name: String(user?.fullName || user.email)
        }
      }
    ],
    hiringProcess: undefined,
    status: JOB_STATUS_ENUM.publish
  }
}

export const mappingEditCompanyJob = (data: IJobDetailForm) => {
  const pipelineStages = (data?.jobStages || [])
    .sort((a, b) => (a.index || 0) - (b.index || 0))
    .map(stage => ({
      ...stage,
      clientShared: stage.clientShared,
      stageType: { id: stage.stageTypeId }
    }))
  return {
    id: data.id,
    title: data?.title,
    headcount: String(data.headcount || ''),
    status: data.status,
    applicants: data.applicants,
    remoteStatus: data?.permittedFields?.['remoteStatus']?.value || '',
    companyId: data?.company
      ? {
          value: data?.company?.id,
          supportingObj: {
            name: data.company?.permittedFields?.name?.value || ''
          }
        }
      : undefined,
    ownerId: data.owner
      ? [
          {
            value: data.owner.id,
            supportingObj: {
              name: String(data.owner?.fullName || data.owner.email)
            }
          }
        ]
      : [],
    hiringManagerIds:
      data.hiringManagers && data.hiringManagers.length > 0
        ? [
            {
              value: String(data.hiringManagers[0]?.id),
              supportingObj: {
                name: String(data.hiringManagers[0]?.fullName || data.hiringManagers[0]?.email)
              }
            }
          ]
        : [],

    description: trimFirstContentBreakLine(data.description),
    pitch: trimFirstContentBreakLine(data.pitch || ''),
    salaryFrom: String(data.permittedFields?.salaryFrom?.value || ''),
    salaryTo: String(data.permittedFields?.salaryTo?.value || ''),
    currency: data.permittedFields?.currency?.value || '',
    employmentType: data.permittedFields?.employmentType?.value || '',
    education: data.education || '',
    jobLevel: data.permittedFields?.jobLevel?.value || '',
    typeOfSalary: data.permittedFields?.typeOfSalary?.value || '',
    jobCategoryId: data.jobCategory
      ? {
          value: data.jobCategory?.id.toString(),
          supportingObj: {
            name: String(data.jobCategory?.name)
          }
        }
      : undefined,
    skills: (data.skills || []).map(skill => ({
      value: skill,
      supportingObj: {
        name: skill
      }
    })),
    industryIds: (data.industries || []).map(item => ({
      value: item.id,
      supportingObj: {
        name: item.name
      }
    })),
    locationIds: (data?.jobLocations || []).map(item => ({
      value: String(item.companyLocationId),
      supportingObj: {
        name: formatAddressLocation({
          location: {
            address: item.address,
            city: item.city,
            state: item.state,
            country: item.country
          }
        })
      }
    })),
    hiringProcess: {
      value: idPipelineCustom,
      supportingObj: {
        name: `Custom for this job`,
        shortDescription: pipelineStages.map((stage: IJobStage) => stage.stageLabel).join(', ')
      },
      pipelineStages
    },
    languages: (data.languages || []).map(lang => ({
      value: String(lang.language),
      supportingObj: {
        name: String(lang.languageDescription)
      }
    })),
    jobTalentPoolIds: (data?.permittedFields?.talentPools?.value || []).map(talentPool => ({
      value: String(talentPool.id),
      supportingObj: {
        name: talentPool.name
      }
    })),
    referenceId: data?.referenceId,
    taggedIds: data?.tags?.map(tag => ({
      value: String(tag.id),
      supportingObj: {
        name: tag.name
      }
    })),
    customFields: formatInitialValueCustomField(data.customFields || [])
  }
}

export const mappingPublishEditCompanyJobGraphQL = (data: IJobForm) => {
  return {
    id: data.id,
    title: data.title,
    headcount: Number(data.headcount),
    remoteStatus: data.remoteStatus || null,
    ownerId: data.ownerId?.length ? Number(data.ownerId[0]?.value) : null,
    hiringManagerIds: data.hiringManagerIds?.length ? [Number(data.hiringManagerIds[0]?.value)] : null,
    description: trimFirstContentBreakLine(data.description),
    pitch: data.pitch ? trimFirstContentBreakLine(data.pitch) : null,
    salaryFrom: data.salaryFrom ? Number(data.salaryFrom) : null,
    salaryTo: data.salaryTo ? Number(data.salaryTo) : null,
    currency: data.currency || null,
    employmentType: data.employmentType || null,
    typeOfSalary: data.typeOfSalary || null,
    industryIds: data.industryIds?.length ? Number(data.industryIds[0]?.value) : null,
    locationIds: (data.locationIds || []).map(item => Number(item.value)).filter(i => i !== null && i !== undefined),
    jobLevel: data.jobLevel || null,
    education: data.education || null,
    jobCategoryId: data.jobCategoryId?.value ? Number(data.jobCategoryId?.value) : null,
    skills: (data.skills || []).length > 0 ? (data.skills || []).map(skill => skill.value) : null,
    languages: mappingLanguagesData(data.languages || []),
    jobTalentPoolIds: (data.jobTalentPoolIds || []).length > 0 ? (data.jobTalentPoolIds || []).map(item => Number(item.value)) : null,
    jobStages: data?.hiringProcess?.pipelineStages?.map(
      (
        stage: {
          id: string
          stageLabel: string
          clientShared?: boolean
          stageType: { id: string }
          _destroy: boolean
        },
        index: number
      ) => {
        const value: {
          id?: string
          _destroy?: boolean
          clientShared?: boolean
          stageLabel?: string
          stageTypeId?: string
          index?: number
        } = { id: stage.id }
        if (stage.id.includes('item')) {
          delete value.id
        }
        if (stage._destroy) {
          value._destroy = true
        } else {
          value.stageLabel = stage.stageLabel
          value.clientShared = stage.clientShared
          value.stageTypeId = stage.stageType.id
          value.index = index
        }
        return value
      }
    ),
    ...(data.customFields ? { customFields: formatSubmitCustomFieldData(data.customFields) } : {}),
    referenceId: data?.referenceId ? data?.referenceId : null,
    taggedIds: (data.taggedIds || []).map(item => Number(item.value))
  }
}
