import type { IUserInformation } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'
import { trimFirstContentBreakLine } from '~/core/utilities/common'

import { idPipelineCustom } from '~/components/Jobs/JobHiringView'

import { formatInitialValueCustomField, formatSubmitCustomFieldData } from '../../settings/profile-fields/mapping/custom-field-mapping'
import type { IJobDetailForm, IJobForm, IJobStage } from '../types'
import { JOB_STATUS_ENUM } from '../utilities/enum'
import { mappingLanguagesData } from './add-job-mapping'

export const mappingDuplicateJob = (data: IJobDetailForm, user: IUserInformation) => {
  const jobData = mappingEditJob(data)
  delete jobData.id
  return {
    ...jobData,
    title: data.title + ' (2)',
    ownerId: [
      {
        value: user.id?.toString(),
        supportingObj: {
          name: String(user?.fullName || user.email)
        }
      }
    ],
    hiringProcess: undefined,
    status: JOB_STATUS_ENUM.publish
  }
}

export const mappingEditJob = (data: IJobDetailForm) => {
  const pipelineStages = (data?.jobStages || [])
    .sort((a, b) => (a.index || 0) - (b.index || 0))
    .map(stage => ({
      ...stage,
      stageType: { id: stage.stageTypeId }
    }))

  return {
    id: data.id,
    title: data?.title,
    headcount: String(data.headcount || ''),
    status: data.status,
    applicants: data.applicants,
    remoteStatus: data?.permittedFields?.['remoteStatus']?.value || '',
    departmentId: data.department
      ? [
          {
            value: data.department.id,
            supportingObj: {
              name: data.department.name
            }
          }
        ]
      : [],
    ownerId: data.owner
      ? [
          {
            value: data.owner.id,
            supportingObj: {
              name: String(data.owner?.fullName || data.owner.email)
            }
          }
        ]
      : [],
    hiringManagerIds:
      data.hiringManagers && data.hiringManagers.length > 0
        ? [
            {
              value: String(data.hiringManagers[0]?.id),
              supportingObj: {
                name: String(data.hiringManagers[0]?.fullName || data.hiringManagers[0]?.email)
              }
            }
          ]
        : [],
    description: trimFirstContentBreakLine(data.description),
    pitch: trimFirstContentBreakLine(data.pitch || ''),
    salaryFrom: String(data.permittedFields?.salaryFrom?.value || ''),
    salaryTo: String(data.permittedFields?.salaryTo?.value || ''),
    currency: data.permittedFields?.currency?.value || '',
    employmentType: data.permittedFields?.employmentType?.value || '',
    education: data.education || '',
    jobLevel: data.permittedFields?.jobLevel?.value || '',
    typeOfSalary: data.permittedFields?.typeOfSalary?.value || '',
    jobCategoryId: data.jobCategory
      ? {
          value: data.jobCategory?.id.toString(),
          supportingObj: {
            name: String(data.jobCategory?.name)
          }
        }
      : undefined,
    skills: (data.skills || []).map(skill => ({
      value: skill,
      supportingObj: {
        name: skill
      }
    })),
    industryIds: (data.industries || []).map(item => ({
      value: item.id,
      supportingObj: {
        name: item.name
      }
    })),
    locationIds: data?.company?.id
      ? ((data.jobLocations || []).map(item => ({
          value: String(item.companyLocationId),
          supportingObj: {
            name: [item.state, item.country]
              .filter(item => item)
              .map(item => item)
              .join(', ')
          }
        })) as ISelectOption[])
      : ((data.jobLocations || []).map(item => ({
          value: String(item.locationId),
          supportingObj: {
            name: item.name
          }
        })) as ISelectOption[]),
    hiringProcess: {
      value: idPipelineCustom,
      supportingObj: {
        name: `Custom for this job`,
        shortDescription: pipelineStages.map((stage: IJobStage) => stage.stageLabel).join(', ')
      },
      pipelineStages
    },
    companyId: data?.company?.id
      ? {
          value: String(data?.company?.id),
          supportingObj: {
            name: String(data?.company?.permittedFields?.name?.value)
          }
        }
      : undefined,
    customFields: formatInitialValueCustomField(data.customFields || []),
    languages: (data.languages || []).map(lang => ({
      value: String(lang.language),
      supportingObj: {
        name: String(lang.languageDescription)
      }
    })),
    jobTalentPoolIds: (data?.permittedFields?.talentPools?.value || []).map(talentPool => ({
      value: String(talentPool.id),
      supportingObj: {
        name: talentPool.name
      }
    })),
    referenceId: data?.referenceId,
    taggedIds: data?.tags?.map(tag => ({
      value: String(tag.id),
      supportingObj: {
        name: tag.name
      }
    }))
  }
}

export const mappingPublishEditJobGraphQL = (data: IJobForm) => {
  return {
    id: data.id,
    title: data?.title,
    headcount: Number(data.headcount),
    remoteStatus: data.remoteStatus || null,
    departmentId: data.departmentId?.length ? Number(data.departmentId[0]?.value) : null,
    ownerId: data.ownerId?.length ? Number(data.ownerId[0]?.value) : null,
    hiringManagerIds: data.hiringManagerIds?.length ? [Number(data.hiringManagerIds[0]?.value)] : null,
    description: trimFirstContentBreakLine(data.description),
    pitch: data.pitch ? trimFirstContentBreakLine(data.pitch) : null,
    salaryFrom: data.salaryFrom ? Number(data.salaryFrom) : null,
    salaryTo: data.salaryTo ? Number(data.salaryTo) : null,
    currency: data.currency || null,
    employmentType: data.employmentType || null,
    typeOfSalary: data.typeOfSalary || null,
    industryIds: data.industryIds?.length ? Number(data.industryIds[0]?.value) : null,
    locationIds: (data.locationIds || []).map(item => Number(item.value)).filter(i => i !== null && i !== undefined),
    jobLevel: data.jobLevel || null,
    education: data.education || null,
    jobCategoryId: data.jobCategoryId?.value ? Number(data.jobCategoryId?.value) : null,
    skills: (data.skills || []).length > 0 ? (data.skills || []).map(skill => skill.value) : null,
    languages: mappingLanguagesData(data.languages || []),
    jobTalentPoolIds: (data.jobTalentPoolIds || []).length > 0 ? (data.jobTalentPoolIds || []).map(item => Number(item.value)) : null,
    jobStages: data?.hiringProcess?.pipelineStages?.map(
      (
        stage: {
          id: string
          stageLabel: string
          clientShared?: boolean
          stageType: { id: string }
          _destroy: boolean
        },
        index: number
      ) => {
        const value: {
          id?: string
          _destroy?: boolean
          clientShared?: boolean
          stageLabel?: string
          stageTypeId?: string
          index?: number
        } = { id: stage.id }
        if (stage.id.includes('item')) {
          delete value.id
        }
        if (stage._destroy) {
          value._destroy = true
        } else {
          value.stageLabel = stage.stageLabel
          value.clientShared = stage.clientShared
          value.stageTypeId = stage.stageType.id
          value.index = index
        }
        return value
      }
    ),
    customFields: formatSubmitCustomFieldData(data.customFields || {}),
    referenceId: data?.referenceId ? data.referenceId : null,
    taggedIds: (data.taggedIds || []).map(item => Number(item.value)),
    ...(data?.companyId?.value ? { companyId: Number(data.companyId.value) } : {})
  }
}
