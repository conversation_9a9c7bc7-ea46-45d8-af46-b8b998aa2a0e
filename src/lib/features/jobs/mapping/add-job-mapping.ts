import type { IUserInformation } from '~/core/@types/global'
import { REMOTE_STATUS } from '~/core/constants/enum'
import type { ISelectOption } from '~/core/ui/Select'
import { trimFirstContentBreakLine } from '~/core/utilities/common'

import type { IRequisitionDetail } from '../../requisitions/types/requisition-detail'
import { formatSubmitCustomFieldData } from '../../settings/profile-fields/mapping/custom-field-mapping'
import type { IJobForm } from '../types'
import { JOB_STATUS_ENUM } from '../utilities/enum'

export const mappingAddJob = (
  user: IUserInformation,
  extraData?: {
    [key: string]: unknown
  }
) => {
  return {
    title: '',
    locationIds: [],
    description: '',
    departmentId: [],
    headcount: '',
    ownerId: [
      {
        value: String(user.id),
        supportingObj: { name: String(user.fullName || user.email) }
      }
    ],
    remoteStatus: REMOTE_STATUS.NO_REMOTE,
    typeOfSalary: 'monthly',
    currency: user?.currentTenant?.currency,
    status: JOB_STATUS_ENUM.publish,
    ...extraData
  }
}

export const mappingRequisitionDetail = (data: IRequisitionDetail, user: IUserInformation) => {
  return {
    title: data?.name,
    remoteStatus: REMOTE_STATUS.NO_REMOTE,
    description: trimFirstContentBreakLine(data.description),
    departmentId: data?.department
      ? [
          {
            value: data?.department?.id,
            supportingObj: {
              name: data.department.name
            }
          }
        ]
      : [],
    locationIds: (data.locations || []).map(item => ({
      value: String(item.id),
      supportingObj: {
        name: item.name
      }
    })),
    salaryFrom: String(data.salaryFrom || ''),
    salaryTo: String(data.salaryTo || ''),
    currency: data.currency || '',
    typeOfSalary: data.typeOfSalary || '',
    ownerId: [
      {
        value: String(user.id),
        supportingObj: {
          name: String(user?.fullName || user.email)
        }
      }
    ],
    hiringManagerIds:
      data.managers && data.managers.length > 0
        ? [
            {
              value: data.managers[0]?.id,
              supportingObj: {
                name: String(data.managers[0]?.fullName)
              }
            }
          ]
        : [],
    headcount: String(data.headcount || '')
  }
}

export const mappingLanguagesData = (languages: ISelectOption[]) =>
  languages.map((lang, index) => ({
    index,
    language: lang.value,
    languageDescription: lang?.supportingObj?.name,
    proficiency: '',
    proficiencyDescription: ''
  }))

export const mappingPublishAddJobGraphQL = (data: IJobForm) => {
  return {
    title: data.title,
    headcount: Number(data.headcount),
    status: data.status || JOB_STATUS_ENUM.publish,
    remoteStatus: data.remoteStatus || undefined,
    departmentId: data.departmentId?.length ? Number(data.departmentId[0]?.value) : undefined,
    ownerId: data.ownerId?.length ? Number(data.ownerId[0]?.value) : undefined,
    description: trimFirstContentBreakLine(data.description),
    pitch: data.pitch ? trimFirstContentBreakLine(data.pitch) : null,
    salaryFrom: Number(data.salaryFrom) || undefined,
    salaryTo: Number(data.salaryTo) || undefined,
    currency: data.currency || undefined,
    employmentType: data.employmentType || undefined,
    typeOfSalary: data.typeOfSalary || undefined,
    industryIds: data.industryIds?.length ? Number(data.industryIds[0]?.value) : undefined,
    locationIds: (data.locationIds || []).map(item => Number(item.value)).filter(i => i !== null && i !== undefined),
    jobLevel: data.jobLevel || undefined,
    education: data.education || undefined,
    jobCategoryId: data.jobCategoryId?.value ? Number(data.jobCategoryId?.value) : undefined,
    skills: (data.skills || []).length > 0 ? (data.skills || []).map(item => item.value) : undefined,
    languages: mappingLanguagesData(data.languages || []),
    jobTalentPoolIds: (data.jobTalentPoolIds || []).length > 0 ? (data.jobTalentPoolIds || []).map(item => Number(item.value)) : undefined,
    pipelineTemplateId: Number(data?.hiringProcess?.id),
    jobStages: data?.hiringProcess?.pipelineStages?.map(
      (
        stage: {
          stageLabel: string
          stageType: { id: string }
          clientShared?: boolean
        },
        index: number
      ) => ({
        index,
        clientShared: stage?.clientShared,
        stageLabel: stage.stageLabel,
        stageTypeId: stage.stageType.id
      })
    ),
    hiringProcessName: data?.hiringProcess?.name,
    ...(data?.requisitionId ? { requisitionId: data?.requisitionId } : {}),
    ...(data.customFields ? { customFields: formatSubmitCustomFieldData(data.customFields) } : {}),
    ...(data?.companyId?.value ? { companyId: Number(data.companyId.value) } : {}),
    referenceId: data?.referenceId ? data.referenceId : null,
    taggedIds: (data.taggedIds || []).map(item => Number(item.value))
  }
}

export const mappingTitleListToSelect = (options: Array<{ id: string; name: string }>) => {
  return options.map<ISelectOption>(item => ({
    value: String(item.name),
    label: item.name,
    id: item.id,
    supportingObj: {
      name: item.name || ''
    }
  }))
}
