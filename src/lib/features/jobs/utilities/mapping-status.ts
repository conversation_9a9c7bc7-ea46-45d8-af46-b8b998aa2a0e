import type { ISelectOption } from '~/core/@types/global'

import { JOB_STATUS_ENUM } from './enum'

export const mappingStatusCanShow = (jobStatus: Array<ISelectOption>, status: string) => {
  if (!status || jobStatus?.length === 0) return []
  let results = [] as Array<ISelectOption>

  if (status === JOB_STATUS_ENUM.draft) {
    results = (jobStatus || []).filter(item => [JOB_STATUS_ENUM.publish, JOB_STATUS_ENUM.internal].includes(item.value as JOB_STATUS_ENUM))
  }

  if (status === JOB_STATUS_ENUM.publish) {
    results = (jobStatus || []).filter(item => [JOB_STATUS_ENUM.archived, JOB_STATUS_ENUM.internal].includes(item.value as JOB_STATUS_ENUM))
  }

  if (status === JOB_STATUS_ENUM.archived) {
    results = (jobStatus || []).filter(item => [JOB_STATUS_ENUM.publish, JOB_STATUS_ENUM.internal].includes(item.value as JOB_STATUS_ENUM))
  }

  if (status === JOB_STATUS_ENUM.internal) {
    results = (jobStatus || []).filter(item => [JOB_STATUS_ENUM.publish, JOB_STATUS_ENUM.archived].includes(item.value as JOB_STATUS_ENUM))
  }

  return results
}
