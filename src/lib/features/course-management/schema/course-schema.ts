import type { TFunction } from 'i18next'
import { t } from 'i18next'
import { z } from 'zod'

import { removeHTMLTags } from '~/core/utilities/common'

const MAX_FILE_SIZE = 2 * 1024 * 1024
const ALLOWED_IMAGE_TYPES = ['image/png', 'image/jpeg', 'image/jpg']

export const schemaCourseForm = (t: TFunction) => {
  return z
    .object({
      title: z
        .string({
          required_error: `${t('form:requiredField')}`
        })
        .trim()
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .max(200, {
          message: `${t('form:field_max_number_required', { number: 200 })}`
        }),
      description: z
        .string({
          required_error: `${t('form:requiredField')}`
        })
        .min(1, {
          message: `${t('form:requiredField')}`
        })
        .refine(
          description => {
            return removeHTMLTags(description || '').length <= 10000
          },
          {
            message: `${t('form:field_max_number_required', { number: 10000 })}`
          }
        ),
      skills: z
        .array(
          z.object({
            value: z.string()
          })
        )
        .optional(),
      link: z
        .string({ required_error: `${t('form:requiredField')}` })
        .trim()
        .url({ message: `${t('form:invalidLink')}` }),
      referenceId: z.string().trim().nullish().optional(),
      status: z.string({ required_error: `${t('form:requiredField')}` }).trim(),
      courseType: z.string({ required_error: `${t('form:requiredField')}` }).trim(),
      provider: z
        .string({ required_error: `${t('form:requiredField')}` })
        .trim()
        .nullable()
        .optional(),
      courseLevel: z.string({ required_error: `${t('form:requiredField')}` }).trim(),
      language: z.string({ required_error: `${t('form:requiredField')}` }).trim(),
      price: z
        .string({ required_error: `${t('form:requiredField')}` })
        .trim()
        .nullable()
        .optional(),
      duration: durationSchema.optional(),
      // thumbnail: z
      //   .custom<File>((val) => val instanceof File, {
      //     message: `${t('form:requiredField')}`
      //   })
      //   .refine((file) => file.size <= MAX_FILE_SIZE, {
      //     message: `${t('form:maximum_size_is_2MB')}`
      //   })
      thumbnail: z.any().optional(),
      thumbnailVariants: z
        .object({
          url: z.string().nullable().optional()
        })
        .optional()
    })
    .superRefine((data, ctx) => {
      const { thumbnail, thumbnailVariants } = data
      // if (!thumbnailVariants?.url && !(thumbnail instanceof File)) {
      //   ctx.addIssue({
      //     path: ['thumbnail'],
      //     code: 'custom',
      //     message: `${t('form:requiredField')}`
      //   })
      // }

      if (thumbnail instanceof File && thumbnail.size > MAX_FILE_SIZE) {
        ctx.addIssue({
          path: ['thumbnail'],
          code: 'custom',
          message: `${t('form:maximum_size_is_2MB')}`
        })
      }

      if (thumbnail instanceof File && !ALLOWED_IMAGE_TYPES.includes(thumbnail.type)) {
        ctx.addIssue({
          path: ['thumbnail'],
          code: 'custom',
          message: `${t('form:supportedFormatThumbnail')}`
        })
      }
    })
}
export const durationSchema = z.object({
  hour: z
    .number()
    .int({
      message: `${t('form:numbeIntMustBeGreater', { number: 0 })}`
    })
    .min(0, {
      message: `${t('form:numbeIntMustBeGreater', { number: 0 })}`
    })
    .optional(),
  minute: z
    .number()
    .int({
      message: `${t('form:numbeIntMustBeGreater', { number: 0 })}`
    })
    .min(0, {
      message: `${t('form:numbeIntMustBeGreater', { number: 0 })}`
    })
    .optional(),
  second: z
    .number()
    .int({
      message: `${t('form:numbeIntMustBeGreater', { number: 0 })}`
    })
    .min(0, {
      message: `${t('form:numbeIntMustBeGreater', { number: 0 })}`
    })
    .optional()
})
