import { trimFirstContentBreakLine } from '~/core/utilities/common'

import type { ISelectOption } from '../../../../core/ui/Select'
import { uuidV4 } from '../../../../core/ui/utils'
import type { CourseManagementForm, CourseManagementType, ICourseManagementFilter } from '../types'

export const mappingCourseAddGraphQL = (data: CourseManagementForm) => {
  return {
    title: data.title,
    referenceId: data.referenceId || null, // optional
    status: data.status,
    courseType: data.courseType,
    provider: data.provider,
    courseLevel: data.courseLevel,
    language: data.language,
    price: data.price,
    duration: data.duration,
    link: data.link,
    skills: (data.skills || []).length > 0 ? (data.skills || []).map((item: ISelectOption) => String(item.value)) : undefined,

    thumbnail: data.thumbnail ? new File([new Blob([data.thumbnail || ''])], `course-${uuidV4()}.jpeg`) : null,
    description: trimFirstContentBreakLine(data.description)
  }
}
export const mappingCourseEditGraphQL = (data: CourseManagementForm) => {
  const thumbnail = data.thumbnail ? new File([new Blob([data.thumbnail])], `course-${uuidV4()}.jpeg`) : null
  return {
    id: Number(data.id),
    title: data.title,
    referenceId: data.referenceId || null, // optional
    status: data.status,
    courseType: data.courseType,
    provider: data.provider,
    courseLevel: data.courseLevel,
    language: data.language,
    price: data.price,
    duration: data.duration,
    link: data.link,
    skills: (data.skills || []).length > 0 ? (data.skills || []).map((item: ISelectOption) => String(item.value)) : null,
    thumbnail: data.thumbnail !== undefined ? thumbnail : data.thumbnail,
    description: trimFirstContentBreakLine(data.description)
  }
}

export const mappingCourseListingGraphQL = (filterCourse: ICourseManagementFilter) => {
  return {
    page: filterCourse.page,
    search: filterCourse.search || '',
    limit: filterCourse.limit,
    sorting: filterCourse.sorting,
    statuses: (filterCourse.statuses || []).length > 0 ? (filterCourse.statuses || []).map((item: ISelectOption) => String(item.value)) : undefined,
    levels: (filterCourse.levels || []).length > 0 ? (filterCourse.levels || []).map((item: ISelectOption) => String(item.value)) : undefined,

    providers:
      (filterCourse.providers || []).length > 0 ? (filterCourse.providers || []).map((item: ISelectOption) => String(item.value)) : undefined,
    pricing: filterCourse.pricing ? String(filterCourse.pricing.value) : undefined
  }
}

export const mappingCourseForm = (data: CourseManagementType) => {
  return {
    id: data.id ? String(data.id) : undefined,
    title: data?.title,
    referenceId: data?.referenceId || null, // optional
    status: data?.status,
    courseType: data?.courseType,
    provider: data?.provider,
    courseLevel: data?.courseLevel,
    language: data?.language,
    price: data?.price,
    duration:
      typeof data.duration === 'object' && data.duration !== null
        ? {
            hour: data.duration.hour ?? undefined,
            minute: data.duration.minute ?? undefined,
            second: data.duration.second ?? undefined
          }
        : undefined,
    link: data?.link,
    skills:
      (data?.skills || []).length > 0
        ? (data.skills || []).map((skill: string) => ({
            value: skill,
            supportingObj: { name: skill }
          }))
        : [],

    thumbnail: data?.thumbnail,
    description: data?.description,
    thumbnailVariants: data?.thumbnailVariants
  }
}

export const mappingCoursesBulkActionGraphQL = (filterCourse: ICourseManagementFilter) => {
  return {
    search: filterCourse.search || '',
    statuses: (filterCourse.statuses || []).length > 0 ? (filterCourse.statuses || []).map((item: ISelectOption) => String(item.value)) : undefined,
    levels: (filterCourse.levels || []).length > 0 ? (filterCourse.levels || []).map((item: ISelectOption) => String(item.value)) : undefined,

    providers:
      (filterCourse.providers || []).length > 0 ? (filterCourse.providers || []).map((item: ISelectOption) => String(item.value)) : undefined,
    pricing: filterCourse.pricing ? String(filterCourse.pricing.value) : undefined
  }
}
