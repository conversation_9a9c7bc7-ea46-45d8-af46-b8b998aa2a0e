import type { ILogoAndAvatarVariants, IParamsTableInfinity } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'

export interface ICareerForm extends IParamsTableInfinity {
  search?: string
  countryStateId?: ISelectOption
  departmentIds?: ISelectOption[]
  remoteStatusOption?: ISelectOption
  remoteStatus?: ISelectOption | string
  key?: number
  jobLevel?: ISelectOption | string
  referredBy?: string
}

export interface ICareerSearchParam {
  search?: string
  country?: string
  state?: string
  departmentIds?: ISelectOption[]
  remoteStatus?: string | Array | ISelectOption
  remoteStatusOption?: ISelectOption
  key?: number
  page?: number
  countryStateId?: ISelectOption
  jobLevel?: string | ISelectOption
}

export interface ICareer {
  id?: string
  title: string
  remoteStatus: string
  remoteStatusDescription: string
  slug: string
  jobLocations: Array<{
    id: number
    locationId: number
    address: string
    country: string
    city: string
    state: string
  }>
  salaryFrom: number
  salaryTo: number
  currency: string
  currencyDescription: string
  typeOfSalaryDescription: string
  employmentType: string
  employmentTypeDescription: string
  createdAt: string
  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: string
    }
  }
}

export interface ShowMoreTextProps {
  text: string
  limitLines?: number
  className?: string
}

export interface LocationItem {
  name: string
  address: string
  country: string
  state: string
  countryStateId?: number
}

export interface DepartmentItem {
  id: string
  name: string
  subordinates: { id: string; name: string }[]
  parent: { id: string; name: string }
}

export interface CareersListInfinityProps {
  search: {
    filter?: ICareerForm | ICareerSearchParam
  }
  emptyConfig?: {
    classNameEmpty?: string
    title?: string
    description?: string
    buttonTitle?: string
    buttonTitleOnClick?: () => void
    titleSearch?: string
    descriptionSearch?: string
    buttonTitleSearch?: string
    buttonTitleSearchOnClick?: () => void
    buttonClassNameText?: string
  }
  listConfig: {
    renderButton?: ReactNode
    renderItem: (data: ICareer) => ReactNode
    endOfList?: string
  }
  dataQuery?: dataQueryTableInfinityProps
}

export interface TenantType {
  name: string
  companyKind: string
  logoVariants?: ILogoAndAvatarVariants
  description: string
  openJobsCount: number
  careerSiteSettings: CareerPageSettingType
}

export interface CareerPageSettingType {
  canonical_url?: string
  tenant_slug?: string
  description?: {
    [key: string]: string
  }
  languages?: {
    [key: string]: {
      default: boolean
      enable: boolean
    }
  }
  page_title?: string
  department_visibility?: string
  ga_measurement_id?: string
  enablingCareerPageBuilder?: boolean
}

export interface CareerDepartmentParams {
  page?: number
  limit?: number
  tenantSlug?: string
  search?: string
}

export interface CareerLocationsParams {
  page?: number
  limit?: number
  tenantSlug?: string
}

export interface PublicInterviewDetailType {
  applicantId: number
  currentToken: string
  profileId: number
  eventTypeDescription?: string
  remoteStatusDescription?: string
  eventType?: string
  fromDatetime?: string
  toDatetime?: string
  timezone?: string
  meetingUrl?: string
  previewLink?: string
  callableAttendAction?: boolean
  careerSiteSettings: {
    canonical_url: string
  }
  tenant: {
    name: string
    slug: string
  }
  location?: {
    id: number
    address: string
    country: string
    state: string
    city: string
  }
  profile?: {
    fullName?: string
    avatarVariants?: ILogoAndAvatarVariants
    email?: string | Array<string>
  }
  organizer?: {
    id: number
    avatarVariants?: ILogoAndAvatarVariants
    fullName: string
    defaultColour?: string
    email?: string
  }
  attendees?: Array<{
    id?: number
    avatarVariants?: ILogoAndAvatarVariants
    fullName?: string
    email?: Array<string>
    defaultColour?: string
    roles?: Array<{
      id: number
      name: string
    }>
  }>
  job?: {
    title: string
    salaryFrom: number
    salaryTo: number
    currency: string
    typeOfSalaryDescription: string
    employmentTypeDescription: string
    remoteStatusDescription: string
    jobLocations: {
      name: string
      state: string
      address: string
      country: string
      city: string
    }[]
    permittedFields?: {
      [key: string]: {
        role_changeable?: boolean
        visibility_changeable?: boolean
        roles?: Array<string>
        value?: string
      }
    }
  }
}

export interface IPublicSelfSchedulesShow {
  eventTypeDescription: string
  timezoneName: string
  timezone: string
  job: {
    title: string
  }
  tenant: {
    logoVariants?: ILogoAndAvatarVariants
    name: string
  }
  token?: string
  interviewTimeSlots: IInterviewSlotTime[]
}

export interface IInterviewSlotTime {
  id: string
  fromDatetime: string
  toDatetime: string
  timezone: string
  timezoneName: string
}

export interface ISlotTime {
  id?: string
  label?: string
  timezone?: string
  fromDatetime?: string
  toDatetime?: string
}
