import type { ILogoAndAvatarVariants, ISelectOption, IUserInformation } from '~/core/@types/global'

import type { CommentType } from '~/features/placements/components/PlacementNotesDialog'

import { IAgencyCompanies } from '../../agency/companies/types'
import type { CompanyItemListingType } from '../../agency/companies/types/company-detail'
import type { ICandidateApplicant } from '../../candidates/types'
import { IPermittedFields } from '../../candidates/types'

export interface IUserPlacementSettingResponse {
  id: number
  kind: string
  kindDescription: string
  group: string
  groupDescription: string
  profileColumnsIsDefault: boolean
  values: InputFieldsSettingType[]
  placementDisplay: InputFieldsSettingType[]
}

export type IQueryPlacementParam = Partial<{
  page: number
  limit: number
  search: string
  companyId: number
  status: Array<string>
  hiredDate: string
  startDate: string
  status: string
  profitSplitUserIds: number
}>

export interface IPlacementFilters {
  fieldType: string
  fieldKind?: string
  object?: string
  direction?: string
  value: unknown
  id?: number
}

export type IFilterPlacement = Partial<{
  sorting?: {
    onboard_date?: 'desc' | 'asc'
  }
  isFilterTouched: boolean
  page: number
  operator: string
  search: string
  companyId?: ISelectOption
  public_id?: string
  profile_full_name?: string
  applicant_statuses?: ISelectOption
  profitSplitUserIds?: ISelectOption[]
  job_id?: ISelectOption
  company_id?: ISelectOption
  status?: ISelectOption
  hired_by_id?: ISelectOption
  hired_date?: {
    from?: string
    to?: string
  }
  onboard_date?: {
    from: string
    to: string
  }
  end_of_probation_date?: {
    from: string
    to: string
  }
  salary?: string
  fee?: string
  typeFee?: ISelectOption
  revenue?: string
  profit_splits?: ISelectOption[]
  created_by_id?: ISelectOption
  tabsMembersTeams?: string
  comments?: string
  fieldsFilter?: {
    id?: string
    field?: string
    direction?: string
    directions?: Array<string>
  }[]
  departmentIds?: ISelectOption[]
}>

export type IAgencyStatus = 'Not Invoiced' | 'Paid Partially' | 'Paid Fully' | 'Cancelled'

export type IAgencyStatusValue = 'not_invoiced' | 'paid_partially' | 'paid_fully' | 'cancelled'
export type IAgencyTypeOfSalary = 'annually' | 'monthly'
export type IPlacementType = Partial<{
  id: number
  status: string
  hiredDate: string
  onboardDate: string
  endOfProbationDate: string
  salary: string
  typeOfSalary: string
  currencyOfSalary: string
  fee: number
  typeOfFee: string
  revenue: string
  currencyOfRevenue: string
  createdAt: string
  updatedAt: string
  profitSplits: {
    id: number
    profitPercentage: number
    user: IUserInformation
    createdAt: string
    updatedAt: string
  }[]
  company: CompanyItemListingType
  applicant: ICandidateApplicant
  hiredBy: IUserInformation
  job: {
    id: number
    title: string
  }
  permittedFields: IPlacementPermittedFields
  comments?: Array<CommentType>
  createdBy?: IUserInformation
  customFields?: CustomFieldResponseItem[]
  ownedPlacement: boolean
  editablePlacement?: boolean
}>

export interface IPlacementPermittedFields {
  publicId: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  jobStage: {
    value?: {
      id: number
      createdAt: string
      stageLabel: string
      updatedAt: string
    }
    roles?: string[]
    visibility_changeable?: boolean
  }
  job: {
    value?: {
      id: number
      title: string
    }
    roles?: string[]
    visibility_changeable?: boolean
  }
  company: {
    value?: CompanyItemListingType
    roles?: string[]
    visibility_changeable?: boolean
  }
  applicant: {
    value?: ICandidateApplicant
    roles?: string[]
    visibility_changeable?: boolean
  }
  status: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  hiredDate: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  onboardDate: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  endOfProbationDate: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  salary: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  typeOfSalary: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  currencyOfSalary: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  fee: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  typeOfFee: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  currencyOfFee: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  revenue: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  currencyOfRevenue: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
  }
  profitSplits: {
    value?: {
      id: number
      profitPercentage: number
      profit_percentage: number
      user: IUserInformation
      createdAt: string
      updatedAt: string
    }[]
    roles?: string[]
    visibility_changeable?: boolean
  }
  hiredBy: {
    value?: IUserInformation
    roles?: string[]
    visibility_changeable?: boolean
  }
  createdBy: {
    value?: IUserInformation
    roles?: string[]
    visibility_changeable?: boolean
  }
  comments: {
    value?: Array<CommentType>
    roles?: string[]
    visibility_changeable?: boolean
  }
}

export interface OwnerType {
  id: number
  email: string
  fullName: string
  avatarVariants?: ILogoAndAvatarVariants
  defaultColour: string
  roles: Array<{
    id: number
    name: string
  }>
}

export type IPlacementCustomField = {
  custom_setting_id: number
  field_name: string
  field: string
  index: number
  locked: boolean
  display_locked: boolean
  field_level: 'default' | 'custom' | 'system'
  visibility: boolean
  field_kind: string
  custom_field_id: string
  required: boolean
  value: string
  select_options: Array<{
    key: string
    index: number
    value: string
    description: string
  }>
}

export type IPlacementCustomFieldParam = {
  index: number
  id: string
  fieldKind: string
  customSettingId: number
  value: string
  selectedOptionKeys: string[]
}
