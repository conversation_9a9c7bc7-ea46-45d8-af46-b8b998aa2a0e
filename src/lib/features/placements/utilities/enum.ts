import type { LucideIconName } from '~/core/ui/IconWrapper'
import type { ISelectOption } from '~/core/ui/Select'

export type IPlacementStatusType = {
  label: string
  id: string
  colorClassName: string
}

export const PLACEMENT_COLOR_STATUS = (status: string) => {
  const lowercaseStatus = status.trim().toLowerCase().replaceAll(' ', '_')
  let obj = { key: 'cancelled', color: 'red' }

  switch (lowercaseStatus) {
    case 'not_invoiced':
      obj = { key: 'not_invoiced', color: 'gray' }
      break
    case 'paid_partially':
      obj = { key: 'paid_partially', color: 'yellow' }
      break
    case 'paid_fully':
      obj = { key: 'paid_fully', color: 'green' }
      break
    case 'invoiced':
      obj = { key: 'invoiced', color: 'purple' }
      break
    default:
      break
  }

  return obj
}
export const PLACEMENT_FEE_TYPE = (value: string) => {
  const lowercaseType = value.trim().toLowerCase().replaceAll(' ', '_')
  if (lowercaseType === 'percentage') return { key: 'percentage' }
  if (lowercaseType === 'months') return { key: 'months' }
  return { key: 'default' }
}

export const mappingPlacementCustomFieldIcon = (fieldKind: string) => {
  if (!fieldKind) return 'Type'

  return (
    {
      string: 'Type',
      number: 'Hash',
      text: 'AlignLeft',
      boolean: 'CheckSquare',
      array: 'ListChecks',
      date: 'Calendar',
      multiple: 'LayoutList'
    } as { [key: string]: LucideIconName }
  )[fieldKind]
}

export const PLACEMENT_YES_NO: ISelectOption[] = [{ value: 'yes' }, { value: 'no' }]

export const FILTER_PLACEMENT_FIELDS_VALUE = {
  public_id: 'public_id',
  profile_full_name: 'profile_full_name',
  applicant_statuses: 'applicant_statuses',
  job_id: 'job_id',
  company_id: 'company_id',
  status: 'status',
  hired_by_id: 'hired_by_id',
  hired_date: 'hired_date',
  onboard_date: 'onboard_date',
  end_of_probation_date: 'end_of_probation_date',
  salary: 'salary',
  fee: 'fee',
  typeFee: 'typeFee',
  tabsMembersTeams: 'tabsMembersTeams',
  revenue: 'revenue',
  profit_splits: 'profit_splits',
  created_by_id: 'created_by_id',
  comments: 'comments',
  operator: 'operator'
}

export const FILTER_PLACEMENT_FIELDS_CONDITION = {
  equal: 'equal',
  greaterThan: 'greater_than',
  lessThan: 'less_than',
  contains: 'contains',
  is: 'is',
  isNot: 'is_not',
  isAnyOf: 'is_any_of',
  isNoneOf: 'is_none_of',
  isEmpty: 'is_empty',
  isNotEmpty: 'is_not_empty',
  range: 'range',
  anyOfAll: 'any_of_all',
  anyOf: 'any_of',
  all: 'all',
  and: 'and',
  or: 'or'
}
