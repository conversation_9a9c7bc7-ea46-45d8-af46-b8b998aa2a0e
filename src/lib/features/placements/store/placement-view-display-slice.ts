import { create } from 'zustand'

import type { FieldSettingType } from '~/components/DisplayConfig'

import { IProfileViewDisplay } from '../../candidates/types'

type IPlacementDisplay = {
  id?: string | number
  group?: string
  createdById?: number
  name?: string
  state?: string
  stateDescription?: string
  placementDisplay?: FieldSettingType[]
  placementColumnsIsDefault?: boolean
}

interface PlacementViewDisplaySlice {
  placementViewDisplay?: IPlacementDisplay
  setPlacementViewDisplay: (profileViewDisplay: IPlacementDisplay | undefined) => void
}

const usePlacementViewDisplayStore = create<PlacementViewDisplaySlice>(set => ({
  placementViewDisplay: undefined,
  setPlacementViewDisplay: (placementViewDisplay: IPlacementDisplay | undefined) => set({ placementViewDisplay })
}))

export default usePlacementViewDisplayStore
