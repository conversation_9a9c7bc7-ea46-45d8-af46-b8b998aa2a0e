import type { TFunction } from 'next-i18next'
import { useTranslation } from 'next-i18next'
import { useCallback, useMemo } from 'react'
import type { AnyVariables, OperationResult } from 'urql'

import configuration from '~/configuration'
import type { IToast } from '~/core/@types/global'
import useMutationGraphQL from '~/core/middleware/use-mutation-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import type { FieldSettingType } from '~/components/DisplayConfig'

import type { InputFieldsSettingType } from '../../candidates/types/user-setting'
import { FIELDS_USER_SETTING_DISPLAY } from '../../candidates/utilities/enum'
import MutationUpdateUserPlacementSetting from '../graphql/mutation-user-update-placement-setting'
import QueryUserPlacementSettingIsDefault from '../graphql/query-user-placement-default-seting'
import QueryUserPlacementColumnsSetting from '../graphql/query-user-placement-setting'
import usePlacementViewDisplayStore from '../store/placement-view-display-slice'
import { mappingPlacementCustomFieldIcon } from '../utilities/enum'

const hiddenDisplayFields = ['applicantDisqualified', 'jobArchived']

export const mappingDisplayColumnsSetting = (data: InputFieldsSettingType[], t?: TFunction) => {
  const fields = data
  const arr: FieldSettingType[] = []
  fields.forEach(fieldItem => {
    const isDefaultField = !fieldItem?.field_level || fieldItem?.field_level === 'default'

    arr[fieldItem?.index] = {
      id: String(fieldItem?.index),
      value: fieldItem.field_name,
      accessorKey: isDefaultField ? fieldItem.field_name : `column-custom-field-${fieldItem?.custom_field_id}`,
      locked: fieldItem?.locked,
      name: fieldItem?.name,
      hidden: hiddenDisplayFields.includes(fieldItem.field_name),
      visibleValue: fieldItem?.display,
      isDisableVisible: fieldItem?.display_locked,
      label: isDefaultField
        ? t
          ? `${t(`placements:management:displayConfig:${FIELDS_USER_SETTING_DISPLAY?.[fieldItem.field_name]?.key}`)}`
          : ''
        : fieldItem?.field_name,
      iconsMenu:
        fieldItem?.field_level === 'default'
          ? FIELDS_USER_SETTING_DISPLAY?.[fieldItem.field_name]?.iconMenus
          : mappingPlacementCustomFieldIcon(fieldItem.field_kind || ''),
      filter: !!fieldItem?.filter,
      fieldLevel: fieldItem?.field_level,
      fieldKind: fieldItem?.field_kind,
      customSettingId: fieldItem?.custom_field_id,
      visibility: fieldItem?.visibility,
      roleIds: fieldItem?.role_ids,
      isDefaultField
    }
  })

  return Object.values(arr)
}

export const useUserPlacementColumnsSetting = ({ setToast }: { setToast: (state: IToast) => void }) => {
  const { t } = useTranslation()
  const { placementViewDisplay, setPlacementViewDisplay } = usePlacementViewDisplayStore()

  const {
    trigger: fetchUserPlacementColumnsSetting,
    isLoading: isFetching,
    data: userSetting
  } = useQueryGraphQL({
    query: QueryUserPlacementColumnsSetting,
    variables: {},
    shouldPause: false
  })
  const {
    trigger: fetchIsUserSettingDefault,
    isLoading: isFetchingDefaultFlag,
    data: userIsSettingDefault
  } = useQueryGraphQL({
    query: QueryUserPlacementSettingIsDefault,
    variables: {},
    shouldPause: false
  })
  const {
    trigger: updateUserSetting,
    isLoading: isUpdating,
    data: updateResponseData
  } = useMutationGraphQL({
    query: MutationUpdateUserPlacementSetting
  })

  const resetAsDefault = useCallback<() => Promise<void>>(() => {
    const params = placementViewDisplay?.id
      ? { id: Number(placementViewDisplay.id), group: 'placement_view' }
      : {
          ...(userSetting?.userSettingsPlacementViewColumns?.id ? { id: Number(userSetting?.userSettingsPlacementViewColumns?.id) } : {}),
          group: userSetting?.userSettingsPlacementViewColumns?.group
          // values: formateData,
        }

    return updateUserSetting({
      ...params,
      placementDisplay: userSetting?.userSettingsPlacementViewColumns?.placementDisplay.map(f => ({
        ...f,
        custom_field_id: f.custom_field_id ? f.custom_field_id : null,
        field_level: f.field_level ? f.field_level : null
      })),
      resetDefault: true
    }).then(
      (
        response: OperationResult<
          {
            userSettingsPlacementColumnsUpdate: {
              userSetting: {
                id: number
                placementColumnsIsDefault: boolean
                placementDisplay: FieldSettingType[]
              }
            }
          },
          AnyVariables
        >
      ) => {
        if (response.error) {
          catchErrorFromGraphQL({
            error: response.error,
            page: configuration.path.candidates.list,
            setToast
          })
        }

        const result = response?.data?.userSettingsPlacementColumnsUpdate
        if (result?.userSetting?.id) {
          if (placementViewDisplay?.id) {
            setPlacementViewDisplay({
              ...placementViewDisplay,
              placementDisplay: result?.userSetting?.placementDisplay,
              placementColumnsIsDefault: result?.userSetting?.placementColumnsIsDefault
            })
          } else {
            fetchUserPlacementColumnsSetting()
            fetchIsUserSettingDefault()
          }
        }
      }
    )
  }, [
    fetchUserPlacementColumnsSetting,
    fetchIsUserSettingDefault,
    setToast,
    updateUserSetting,
    userSetting?.userSettingsPlacementViewColumns?.group,
    userSetting?.userSettingsPlacementViewColumns?.id,
    userSetting?.userSettingsPlacementViewColumns?.placementDisplay,
    placementViewDisplay
  ])

  const updateFieldsSetting = useCallback<(data: FieldSettingType[]) => Promise<void>>(
    data => {
      const filterData = data.filter(item => item)
      const formateData = filterData.map((field, index) => ({
        field_name: field?.value,
        display: !!field?.visibleValue,
        name: field?.name,
        index: index + 1,
        filter: field?.filter,
        locked: !!field?.locked,
        display_locked: !!field?.isDisableVisible,
        visibility: field?.visibility,
        field_kind: field?.fieldKind,
        field_level: field?.fieldLevel,
        custom_field_id: field?.customSettingId || null,
        role_ids: field?.roleIds,
        sort: ''
      }))

      const params = placementViewDisplay?.id
        ? { id: Number(placementViewDisplay.id), group: 'placement_view' }
        : {
            ...(userSetting?.userSettingsPlacementViewColumns?.id
              ? {
                  id: Number(userSetting?.userSettingsPlacementViewColumns?.id)
                }
              : {}),
            group: userSetting?.userSettingsPlacementViewColumns?.group
            // values: formateData,
          }

      return updateUserSetting({
        ...params,
        placementDisplay: formateData
      }).then(
        (
          response: OperationResult<
            {
              userSettingsPlacementColumnsUpdate: {
                userSetting: {
                  id: number
                  placementColumnsIsDefault: boolean
                  placementDisplay: FieldSettingType[]
                }
              }
            },
            AnyVariables
          >
        ) => {
          if (response.error) {
            catchErrorFromGraphQL({
              error: response.error,
              page: configuration.path.candidates.list,
              setToast
            })
          }

          const result = response?.data?.userSettingsPlacementColumnsUpdate

          if (result?.userSetting?.id) {
            if (placementViewDisplay?.id) {
              setPlacementViewDisplay({
                ...placementViewDisplay,
                placementDisplay: result?.userSetting?.placementDisplay,
                placementColumnsIsDefault: result?.userSetting?.placementColumnsIsDefault
              })
            } else {
              fetchUserPlacementColumnsSetting()
              fetchIsUserSettingDefault()
            }
          }
        }
      )
    },
    [
      fetchIsUserSettingDefault,
      setToast,
      updateUserSetting,
      userSetting?.userSettingsPlacementViewColumns?.group,
      userSetting?.userSettingsPlacementViewColumns?.id,
      userSetting?.userSettingsPlacementViewColumns?.placementDisplay,
      placementViewDisplay
    ]
  )

  return {
    userSetting: useMemo(
      () => ({
        ...userSetting?.userSettingsPlacementViewColumns,
        placementDisplay: mappingDisplayColumnsSetting(userSetting?.userSettingsPlacementViewColumns?.placementDisplay || [], t)
      }),
      [userSetting?.userSettingsPlacementViewColumns]
    ),
    isDefaultUserSetting: useMemo(
      () => userIsSettingDefault?.userSettingsPlacementViewColumns?.placementColumnsIsDefault,
      [userIsSettingDefault?.userSettingsPlacementViewColumns?.placementColumnsIsDefault]
    ),
    resetAsDefault,
    updateFieldsSetting,
    isLoading: isFetching || isUpdating
  }
}
