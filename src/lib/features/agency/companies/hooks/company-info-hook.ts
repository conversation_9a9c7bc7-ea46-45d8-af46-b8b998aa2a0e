import { useCallback } from 'react'

import configuration from '~/configuration'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import type { IPromiseSearchOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryPublicIndustries from '~/lib/graphql/query-public-industry'
import useBoundStore from '~/lib/store'

import QueryCompanyShow from '../graphql/query-company-show'

const useCompanyInfoHook = () => {
  const { user } = useBoundStore()
  const clientGraphQL = useGraphQLRequest({ language: user?.language })

  const fetchExistedCompany = useCallback((domain: string) => {
    return (
      clientGraphQL
        .query(QueryCompanyShow, { domain: domain })
        .toPromise()
        // @ts-ignore - doesn't need to fix
        .then(result => {
          if (result.error) {
            return catchErrorFromGraphQL({
              error: result.error
            })
          }

          return result.data?.companiesShow
        })
    )
  }, [])

  const promisePublicIndustriesOptions = useCallback(
    (params = {} as IPromiseSearchOption) =>
      new Promise<any>(resolve => {
        clientGraphQL
          .query(QueryPublicIndustries, params)
          .toPromise()
          .then(result => {
            if (result.error) {
              catchErrorFromGraphQL({
                error: result.error
              })
              resolve({
                metadata: {
                  totalCount: configuration.defaultAsyncLoadingOptions
                },
                collection: []
              })
            }

            const collection = result.data?.publicIndustriesList?.collection || []
            const metadata = result.data?.publicIndustriesList?.metadata || {
              totalCount: 0
            }

            const cloneData = collection.map(item => {
              return {
                value: item.id,
                supportingObj: {
                  name: item.name,
                  status: item.status
                }
              }
            })

            return resolve({ metadata, collection: cloneData })
          })
      }),
    []
  )

  return {
    promisePublicIndustriesOptions,
    fetchExistedCompany
  }
}

export default useCompanyInfoHook
