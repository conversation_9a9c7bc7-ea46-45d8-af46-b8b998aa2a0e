import { useRef } from 'react'
import type { FieldPath, UseFormTrigger } from 'react-hook-form'

import type { CompanyDetailType } from '../types/company-detail'

const useCompanyInfoValidationHook = () => {
  const touchingFieldRef = useRef<FieldPath<CompanyDetailType>>(undefined)
  const submitPartialField = (fieldName: FieldPath<CompanyDetailType>, validate: UseFormTrigger<CompanyDetailType>, submit?: () => Promise<any>) => {
    if (touchingFieldRef) {
      touchingFieldRef.current = fieldName
    }

    return validate(fieldName).then(test => {
      if (test) {
        return submit
          ? submit().then(result => {
              return test && !!result
            })
          : Promise.resolve(false)
      }
      return Promise.resolve(false)
    })
  }

  return {
    touchingFieldRef,
    submitPartialField
  }
}

export default useCompanyInfoValidationHook
