'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useMutation } from 'urql'

import configuration from '~/configuration'

import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import { trimObjectProps } from '~/lib/features/tasks/utilities/common'

import DeleteCompanyTaskMutation from '../graphql/delete-company-task-mutation'
import QueryCompanyTasksList from '../graphql/query-company-tasks-list'
import UpdateCompanyTaskMutation from '../graphql/update-company-task-mutation'
import type { ITasksCompanyManagementFilter } from '../types/company-detail'
import { FILTER_BY_DEFAULT } from '../utilities/company-detail-enum'

const useTasksCompanyManagement = ({ companyId }: { companyId: number }) => {
  const [filterValue, onChangeFilter] = useState<ITasksCompanyManagementFilter | undefined>()
  const taskPaging = useInfinityGraphPage({
    queryDocumentNote: QueryCompanyTasksList,
    getVariable: useCallback(
      (page: number) => {
        const { filterBy } = filterValue || {}
        return trimObjectProps({
          limit: configuration.defaultPageSize,
          page,
          companyId,
          filterBy: filterBy || FILTER_BY_DEFAULT
        })
      },
      [filterValue, companyId]
    ),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.companyTasksList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.companyTasksList?.collection?.length)
    }),
    queryKey: ['tasks-candidate-management-list-assigned']
  })

  useEffect(() => {
    taskPaging.refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterValue])

  const [{ fetching: deletingTask }, deleteTask] = useMutation(DeleteCompanyTaskMutation)
  const [{ fetching: updatingTask }, updateTask] = useMutation(UpdateCompanyTaskMutation)

  return {
    taskPaging,
    filterControl: useMemo(() => ({ value: filterValue, onChange: onChangeFilter }), [filterValue]),
    action: {
      taskDeleteAction: {
        deleteTask: (args: { id: number; companyId?: number }) => deleteTask(args),
        deletingTask
      },
      updateTaskAction: {
        updateTask: (args: {
          id: number
          title?: string
          profileId?: number | null
          companyId?: number | null
          dueDate?: string
          assigneeIds?: Array<number>
        }) => updateTask(args),
        updatingTask,
        updateTaskStatus: (args: { id: number; status?: string; title?: string; assigneeIds?: Array<number> }) => updateTask(args)
      }
    }
  }
}

export default useTasksCompanyManagement
