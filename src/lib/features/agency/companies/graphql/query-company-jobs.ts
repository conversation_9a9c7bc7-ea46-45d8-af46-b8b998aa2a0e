import { gql } from 'urql'

import type { CompanyJobResponseType } from '../types/company-detail'

const QueryCompanyJobs = gql<
  {
    companyJobsList: {
      collection: Array<CompanyJobResponseType>
      metadata: {
        totalCount: number
      }
    }
  },
  { companyId?: number; page?: number; limit?: number }
>`
  query ($companyId: Int!, $page: Int, $limit: Int, $status: [JobStatus!], $tagIds: [Int!], $operator: String) {
    companyJobsList(companyId: $companyId, page: $page, limit: $limit, status: $status, tagIds: $tagIds, operator: $operator) {
      collection {
        id
        title
        status
        statusDescription
        jobLocations {
          state
          country
        }
        jobStages {
          stageTypeId
          stageLabel
        }
        owner {
          fullName
          avatarVariants
          defaultColour
        }
        applicants {
          hiredDate
        }
        statistic {
          inprocessGroupCount
          hiredGroupCount
        }
        firstHiredDate
        hiringProcessName
        openedAt
        tags {
          id
          name
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryCompanyJobs
