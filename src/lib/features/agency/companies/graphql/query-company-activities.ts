import { gql } from 'urql'

import type { ActivityBase } from '~/lib/features/activity/types'

const QueryCompanyActivities = gql<
  {
    companyActivitiesList: {
      collection: ActivityBase[]
      metadata: { totalCount: number }
    }
  },
  { companyId: number; page?: number; limit?: number; ownerChanged?: boolean }
>`
  query ($companyId: Int!, $page: Int, $limit: Int, $ownerChanged: Boolean) {
    companyActivitiesList(companyId: $companyId, page: $page, limit: $limit, ownerChanged: $ownerChanged) {
      collection {
        id
        actionKey
        properties
        payload
        createdAt
        loggedDate
        propertiesRelated
        user {
          id
          fullName
          avatarVariants
          defaultColour
        }
        profile {
          id
          fullName
        }
        job {
          title
        }
        recordOwner {
          fullName
          defaultColour
          avatarVariants
        }
        payloadRelated
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryCompanyActivities
