import { gql } from 'urql'

import type { TaskItemType } from '~/lib/features/tasks/types'

const QueryCompanyTasksList = gql<
  {
    companyTasksList: {
      collection: Array<TaskItemType>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    page: number
    limit: number
    tab: string
    companyId: number
    filterBy: string
  }
>`
  query ($page: Int, $limit: Int, $tab: String, $companyId: Int!, $filterBy: String) {
    companyTasksList(page: $page, limit: $limit, tab: $tab, companyId: $companyId, filterBy: $filterBy) {
      collection {
        id
        title
        dueDate
        status
        comments {
          content
        }
        profile {
          id
          fullName
          avatarVariants
        }
        applicant {
          profile {
            id
            fullName
          }
        }
        applicantId
        assignees {
          id
          fullName
          avatarVariants
          defaultColour
          roles {
            id
            name
          }
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryCompanyTasksList
