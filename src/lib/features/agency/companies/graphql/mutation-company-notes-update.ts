import { gql } from 'urql'

import type { UpdateProfileNoteInput } from '~/lib/features/candidates/types'

export const MutationCompanyNotesUpdate = gql<{}, UpdateProfileNoteInput>`
  mutation ($id: Int!, $content: String!, $sharedUserIds: [Int!], $attachments: [File!]) {
    companyNotesUpdate(input: { id: $id, content: $content, sharedUserIds: $sharedUserIds, attachments: $attachments }) {
      companyNote {
        content
        sharedUsers {
          id
          fullName
          avatarVariants
        }
        attachments {
          id
          blobs
          file
        }
      }
    }
  }
`
