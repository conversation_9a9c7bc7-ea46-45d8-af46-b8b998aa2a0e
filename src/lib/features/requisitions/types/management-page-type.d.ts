import type { ILogoAndAvatarVariants, ISelectOption } from '~/core/@types/global'

export type IQueryRequisitionParam = Partial<{
  page: number
  limit: number
  search: string
  status: Array<string>
  ownerId: number
  departmentIds?: number[]
  sorting: {
    createdAt?: 'desc' | 'asc'
  }
}>

export type IFilterRequisition = Partial<{
  isFilterTouched: boolean
  search: string
  status: Array<ISelectOption>
  ownerId: ISelectOption
  departmentIds: ISelectOption[]
}>

export type IRequisitionStatusValue = 'draft' | 'approved' | 'archived' | 'pending' | 'rejected' | 'open'

export type IRequisitionType = Partial<{
  id: number
  name: string
  statusDescription: string
  status: string
  locations: Array<{
    city: string
    state: string
    country: string
  }>
  department: {
    name: string
  }
  salaryFrom: number
  salaryTo: number
  currency: string
  currencyDescription: string
  typeOfSalaryDescription: string
  owner: {
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }
  requisitionSteps: Array<{
    status: string
    requisitionApprovers: Array<{
      approver: {
        id: number
        fullName: string
      }
      status: IRequisitionStatusValue
    }>
  }>
  jobs: Array<{
    id: number
    status: string
    title: string
    currentUserAccessible?: boolean
  }>
  createdAt: string
  jobCreatable: boolean
  editable: boolean
  archivable: boolean
  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: string
    }
  }
}>

export interface OwnerType {
  id: number
  email: string
  fullName: string
  avatarVariants?: ILogoAndAvatarVariants
  defaultColour: string
  roles: Array<{
    id: number
    name: string
  }>
}
