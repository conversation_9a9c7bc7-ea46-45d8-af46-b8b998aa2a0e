import type { ILogoAndAvatarVariants } from '~/core/@types/global'

import type { ICompanyPermittedFields } from '../../agency/companies/types'
import type { IRequisitionStatusValue } from './management-page-type'

export type IRequisitionDetail = {
  id: number
  name: string
  description: string
  statusDescription: string
  status: string
  reasonDescription: string
  createdAt: string
  createdBy: {
    fullName: string
  }
  locations?: Array<{
    id: number
    name: string
    city: string
    state: string
    country: string
  }>
  department?: {
    id: number
    name: string
  }
  company?: {
    id: number
    permittedFields?: ICompanyPermittedFields
  }
  managers: Array<{
    id: number
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }>
  owner: {
    id: number
    email: string
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }
  salaryFrom?: number
  salaryTo?: number
  currency?: string
  currencyDescription?: string
  typeOfSalary?: string
  typeOfSalaryDescription?: string
  requisitionSteps?: Array<{
    status: string
    index: number
    minimumApproval: number
    requisitionApprovers: Array<{
      status: IRequisitionStatusValue
      rejectReason: string
      approver: {
        id?: number
        email?: string
        fullName: string
        defaultColour: string
        avatarVariants: ILogoAndAvatarVariants
      }
    }>
  }>
  jobCreatable?: boolean
  editable?: boolean
  archivable?: boolean
  jobs?: Array<{
    id: number
    title: string
    status: string
  }>
  jobLocations?: Array<{
    name?: string
    address?: string
    state?: string
    city?: string
    country?: string
  }>
  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: string
    }
  }
  headcount: number | string
}

export type RequisitionActionParam = {
  requisitionId: number
  page: number
  limit: number
}
