import { RefObject } from 'react'

import type { ILogoAndAvatarVariants, ISelectOption } from '~/core/@types/global'

export interface IRequisitionForm {
  id?: string | number
  name?: string
  status?: string
  departmentId?: ISelectOption
  ownerId?: ISelectOption
  description?: string
  salaryFrom?: string
  salaryTo?: string
  currency?: string
  typeOfSalary?: string
  locationIds?: Array<ISelectOption>
  requisitionTemplateId?: ISelectOption & {
    reqTemplateSteps: IReqTemplateSteps[]
  }
  reason?: string
  hiringManagerIds?: ISelectOption
  headcount: number | string
}

export interface IReqTemplateSteps {
  id: string
  index: number
  minimumApproval: number
  approvers: {
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
  }[]
}

export interface IRequisitionDetailForm {
  id?: string
  name?: string
  status?: string
  reason?: string
  department?: {
    id: string
    name: string
  }
  managers: {
    id: string
    email: string
    fullName?: string
    defaultColour?: string
    avatarVariants?: ILogoAndAvatarVariants
  }[]
  editable: boolean
  owner: {
    id: string
    email: string
    fullName?: string
  }
  locations: Array<{
    id: string
    locationId: string
    name: string
  }>
  description: string
  salaryFrom?: number
  salaryTo?: number
  currency?: string
  typeOfSalary?: string
  requisitionSteps?: {
    id?: string
    requisitionApprovers?: {
      status?: string
      rejectReason?: string
      approver: {
        email: string
        fullName?: string
        defaultColour?: string
        avatarVariants?: ILogoAndAvatarVariants
      }
    }[]
  }[]
  requisitionTemplate?: {
    id: string
    name: string
    reqTemplateSteps: IReqTemplateSteps[]
  }
  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: string
    }
  }
  headcount: number | string
}
