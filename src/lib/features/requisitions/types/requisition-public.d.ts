import type {
  FetchNextPageOptions,
  InfiniteData,
  InfiniteQueryObserverResult,
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters
} from '@tanstack/react-query'

import type { ILogoAndAvatarVariants } from '~/core/@types/global'

import type { ActivityBase, RequisitionActionLogType } from '../../activity/types'
import type { IRequisitionStatusValue } from './management-page-type'

export type PublicToastMessage = 'approved_by_current_user' | 'approved_by_other_users' | 'rejected_by_current_user' | 'rejected_by_other_users'

export type PublicRequisitionDetailType = {
  name: string
  description: string
  status: string
  statusDescription: string
  reasonDescription: string
  createdAt: string
  createdBy: {
    fullName: string
  }
  locations: Array<{
    name: string
    city: string
    state: string
  }>
  tenant: any
  department: any
  managers: Array<{
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }>
  owner: {
    email: string
    fullName: string
    avatarVariants: ILogoAndAvatarVariants
    defaultColour: string
  }
  salaryFrom: number
  salaryTo: number
  currencyDescription: string
  typeOfSalaryDescription: string
  requisitionSteps: Array<{
    status: string
    minimumApproval: number
    index: number
    requisitionApprovers: Array<{
      status: IRequisitionStatusValue
      rejectReason: string
      approver: {
        email: string
        fullName: string
        avatarVariants: ILogoAndAvatarVariants
        defaultColour: string
      }
    }>
  }>
  jobs: {
    title: string
  }
  toastMessage: PublicToastMessage
  showActionButtons: boolean
  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: string
    }
  }
}

export type RequisitionPublicHistoryParam = {
  requisitionUuid: string
  userToken: string
  page: number
  limit: number
}

export type ActivityInfinityType = {
  data?: InfiniteData<{
    publicRequisitionActivitiesList: {
      collection: (ActivityBase & RequisitionActionLogType)[]
      metadata: {
        totalCount: number
      }
    }
  }>
  hasNextPage?: boolean
  fetchNextPage?: (options?: FetchNextPageOptions | undefined) => Promise<
    InfiniteQueryObserverResult<
      {
        publicRequisitionActivitiesList: {
          collection: (ActivityBase & RequisitionActionLogType)[]
          metadata: {
            totalCount?: number
          }
        }
      },
      {}
    >
  >
  refetch?: <TPageData>(options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined) => Promise<
    QueryObserverResult<
      InfiniteData<{
        publicRequisitionActivitiesList: {
          collection: (ActivityBase & RequisitionActionLogType)[]
          metadata: {
            totalCount?: number
          }
        }
      }>,
      {}
    >
  >
  isLoading?: boolean
  isFetchedAfterMount?: boolean
  formatFromNow?: boolean
}
