import type { StateCreator } from 'zustand'

export interface RemindSlice {
  disableRemindList: number[]
  addDisableItemRemindedList: (value: number[]) => void
  removeDisableItemFromRemindedList: (value: number[]) => void
}

export const createRemindSlice: StateCreator<RemindSlice, [], [], RemindSlice> = (set: Function, get) => ({
  disableRemindList: [],
  addDisableItemRemindedList: value => set({ disableRemindList: [...get().disableRemindList, ...value] }),
  removeDisableItemFromRemindedList: value =>
    set({
      disableRemindList: get().disableRemindList.filter(id => !value.includes(id))
    })
})
