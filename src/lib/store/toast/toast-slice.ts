import type { StateCreator } from 'zustand'

import type { IToast } from '../../../core/@types/global'

export interface ToastSlice {
  configToast: IToast
  setToast: (state: IToast) => void
  setToastClose: () => void
  clearToast: () => void
}

export const createToastSlice: StateCreator<ToastSlice, [], [], ToastSlice> = (set: Function) => ({
  configToast: {
    open: false,
    type: 'info',
    title: '',
    description: '',
    actions: [],
    extraActions: undefined,
    classNameConfig: undefined
  },
  setToast: (configToast: IToast) => set({ configToast }),
  setToastClose: () =>
    set((state: { configToast: IToast }) => ({
      configToast: {
        ...state.configToast,
        open: false
      }
    })),
  clearToast: () =>
    set(() => ({
      configToast: {
        open: false,
        type: '',
        title: '',
        description: '',
        actions: [],
        extraActions: undefined,
        classNameConfig: undefined
      }
    }))
})
