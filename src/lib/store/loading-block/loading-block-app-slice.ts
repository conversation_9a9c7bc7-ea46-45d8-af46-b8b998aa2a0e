import type { StateCreator } from 'zustand'

export interface LoadingBlockAppSlice {
  showLockApp: boolean
  titleLockApp: string
  setShowLockApp: (titleLockApp: string) => void
  setCloseLockApp: () => void
}

export const createLoadingBlockAppSlice: StateCreator<LoadingBlockAppSlice, [], [], LoadingBlockAppSlice> = (set: Function) => ({
  showLockApp: false,
  titleLockApp: '',
  setShowLockApp: (titleLockApp: string) => set({ showLockApp: true, titleLockApp }),
  setCloseLockApp: () => set({ showLockApp: false, titleLockApp: '' })
})
