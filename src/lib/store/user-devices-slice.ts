import type { StateCreator } from 'zustand'

import type { IFirebaseToken } from 'src/firebase/hooks/useFcmToken'

export interface UserDevicesSlice {
  deviceRegistrations?: IFirebaseToken[]
  setDeviceRegistrations: (deviceRegistrations: IFirebaseToken[]) => void
}

export const createUserDevicesSlice: StateCreator<UserDevicesSlice, [], [], UserDevicesSlice> = (set: Function) => ({
  deviceRegistrations: undefined,
  setDeviceRegistrations: (deviceRegistrations: IFirebaseToken[]) => set({ deviceRegistrations })
})
