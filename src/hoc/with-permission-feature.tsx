import type { ReactElement } from 'react'
import { useMemo } from 'react'

import configuration from '~/configuration'
import type { IRole } from '~/core/@types/global'
import { SpinnerIcon } from '~/core/ui/FillIcons'
import { adminCanAction } from '~/core/utilities/permission'

import useBoundStore from '~/lib/store'
import type { ITenantPermissionSettingType } from '~/lib/store/permission-setting-slice'

const Spinner = () => (
  <div className="flex h-screen flex-1 items-center justify-center">
    <SpinnerIcon />
  </div>
)

interface PermissionConfig {
  checkAccessPermission: (data: {
    currentRole?: IRole
    permissionSetting: ITenantPermissionSettingType[]
    keyModule: Array<string>
    keyModuleObject: Array<string>
    action: string
  }) => boolean
  keyModule: Array<string>
  keyModuleObject: Array<string>
  action: string
}

const withPermissionFeatureProvider = <T extends object>(permission: PermissionConfig, Component: (props: T) => ReactElement | null) => {
  const WithPermissionFeature = (props: T) => {
    const { permissionSetting, user, currentRole } = useBoundStore()

    // Memoize permission check to avoid recalculation
    const hasPermission = useMemo(() => {
      // Admin always has access
      if (adminCanAction(currentRole?.code)) {
        return true
      }

      // No permission settings or user - show loading
      if (!permissionSetting?.length || !user.id) {
        return null
      }

      // Check actual permission
      return permission.checkAccessPermission({
        currentRole,
        permissionSetting,
        keyModule: permission.keyModule,
        keyModuleObject: permission.keyModuleObject,
        action: permission.action
      })
    }, [currentRole, permissionSetting, user.id])

    // Handle loading state
    if (hasPermission === null) {
      return <Spinner />
    }

    // Handle access denied - redirect
    if (!hasPermission) {
      // Use replace instead of href to avoid history entry
      window.location.replace(configuration.path.errorAccessDenied)
      return <Spinner /> // Show spinner during redirect
    }

    // User has permission
    return <Component {...props} />
  }

  // Add display name for better debugging
  WithPermissionFeature.displayName = `withPermission(${Component.name || 'Component'})`

  return WithPermissionFeature
}

export default withPermissionFeatureProvider
