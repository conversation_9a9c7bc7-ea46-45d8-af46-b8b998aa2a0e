'use client'

import i18n from 'i18next'
import type { FC } from 'react'
import { useEffect } from 'react'
import { I18nextProvider, initReactI18next } from 'react-i18next'

import configuration from '~/configuration'

import useBoundStore from '~/lib/store'

/**
 * common resources for en
 */
import en_activity from '../../public/locales/en/activity.json'
import en_assign_jobs from '../../public/locales/en/assign-job.json'
import en_auth from '../../public/locales/en/auth.json'
import en_button from '../../public/locales/en/button.json'
import en_candidates from '../../public/locales/en/candidates.json'
import en_career_hub from '../../public/locales/en/career-hub.json'
import en_careers from '../../public/locales/en/careers.json'
import en_common from '../../public/locales/en/common.json'
import en_dashboard from '../../public/locales/en/dashboard.json'
import en_form from '../../public/locales/en/form.json'
import en_interview from '../../public/locales/en/interview.json'
import en_job from '../../public/locales/en/job.json'
import en_label from '../../public/locales/en/label.json'
import en_modal from '../../public/locales/en/modal.json'
import en_notification from '../../public/locales/en/notification.json'
import en_onboarding from '../../public/locales/en/onboarding.json'
import en_placements from '../../public/locales/en/placements.json'
import en_referrals from '../../public/locales/en/referrals.json'
import en_requisitions from '../../public/locales/en/requisitions.json'
import en_settings from '../../public/locales/en/settings.json'
import en_talent_pool from '../../public/locales/en/talent-pool.json'
import en_task from '../../public/locales/en/task.json'
import en_tooltip from '../../public/locales/en/tooltip.json'
import en_welcome from '../../public/locales/en/welcome.json'
/**
 * common resources for ja
 */
import ja_activity from '../../public/locales/ja/activity.json'
import ja_assign_jobs from '../../public/locales/ja/assign-job.json'
import ja_auth from '../../public/locales/ja/auth.json'
import ja_button from '../../public/locales/ja/button.json'
import ja_candidates from '../../public/locales/ja/candidates.json'
import ja_career_hub from '../../public/locales/ja/career-hub.json'
import ja_careers from '../../public/locales/ja/careers.json'
import ja_common from '../../public/locales/ja/common.json'
import ja_dashboard from '../../public/locales/ja/dashboard.json'
import ja_form from '../../public/locales/ja/form.json'
import ja_interview from '../../public/locales/ja/interview.json'
import ja_job from '../../public/locales/ja/job.json'
import ja_label from '../../public/locales/ja/label.json'
import ja_modal from '../../public/locales/ja/modal.json'
import ja_notification from '../../public/locales/ja/notification.json'
import ja_onboarding from '../../public/locales/ja/onboarding.json'
import ja_placements from '../../public/locales/ja/placements.json'
import ja_referrals from '../../public/locales/ja/referrals.json'
import ja_requisitions from '../../public/locales/ja/requisitions.json'
import ja_settings from '../../public/locales/ja/settings.json'
import ja_talent_pool from '../../public/locales/ja/talent-pool.json'
import ja_task from '../../public/locales/ja/task.json'
import ja_tooltip from '../../public/locales/ja/tooltip.json'
import ja_welcome from '../../public/locales/ja/welcome.json'

declare global {
  interface Window {
    i18nInstanceCount: any
    i18nInstances: any
    i18n: any
  }
}

// Add this at the very top of your withTranslateProvider file
if (typeof window !== 'undefined') {
  window.i18nInstanceCount = window.i18nInstanceCount || 0
  window.i18nInstances = window.i18nInstances || []
}

// Global instance cache
const globalI18nInstance = i18n.createInstance()
let isGlobalInitialized = false

const defaultEnResource = {
  common: en_common,
  auth: en_auth,
  form: en_form,
  button: en_button,
  tooltip: en_tooltip,
  label: en_label,
  notification: en_notification,
  task: en_task,
  activity: en_activity,
  onboarding: en_onboarding,
  welcome: en_welcome,
  candidates: en_candidates,
  careers: en_careers,
  job: en_job,
  interview: en_interview,
  settings: en_settings,
  talent_pool: en_talent_pool,
  placements: en_placements,
  assignJobs: en_assign_jobs,
  referrals: en_referrals,
  dashboard: en_dashboard,
  careerHub: en_career_hub,
  requisitions: en_requisitions,
  modal: en_modal
}

const defaultJaResource = {
  common: ja_common,
  auth: ja_auth,
  form: ja_form,
  button: ja_button,
  tooltip: ja_tooltip,
  label: ja_label,
  notification: ja_notification,
  task: ja_task,
  activity: ja_activity,
  onboarding: ja_onboarding,
  welcome: ja_welcome,
  candidates: ja_candidates,
  interview: ja_interview,
  careers: ja_careers,
  job: ja_job,
  settings: ja_settings,
  talent_pool: ja_talent_pool,
  placements: ja_placements,
  assignJobs: ja_assign_jobs,
  referrals: ja_referrals,
  dashboard: ja_dashboard,
  careerHub: ja_career_hub,
  requisitions: ja_requisitions,
  modal: ja_modal
}

// Define types
interface PageResources {
  en: Record<string, object>
  ja: Record<string, object>
}

const withTranslateProvider = <TProps extends object>(
  Component: FC<TProps>,
  pageResources: PageResources = { en: {}, ja: {} },
  ignoreInitialize?: boolean
): FC<TProps> => {
  // Debug: Track instance creation
  if (typeof window !== 'undefined') {
    window.i18nInstanceCount++
    console.log(`🔍 Creating i18n instance #${window.i18nInstanceCount}`)
    console.log('📍 pageResources:', pageResources)
  }

  // Create a new i18n instance for this HOC
  const i18nInstance = i18n.createInstance()

  // Initialize only once globally
  if (!isGlobalInitialized) {
    console.log('🎯 Initializing SINGLE global i18n instance')

    // Store reference for debugging
    if (typeof window !== 'undefined') {
      window.i18nInstances.push({
        instance: i18nInstance,
        createdAt: new Date().toISOString(),
        pageResources: Object.keys(pageResources.en || {}),
        stackTrace: new Error().stack
      })
    }

    globalI18nInstance.use(initReactI18next).init({
      resources: {
        en: { ...defaultEnResource, ...pageResources.en },
        ja: { ...defaultJaResource, ...pageResources.ja }
      },
      lng: configuration.site.locale,
      fallbackLng: configuration.site.locale,
      interpolation: {
        escapeValue: false
      }
    })

    isGlobalInitialized = true
  } else {
    // Add additional resources to existing instance
    Object.entries(pageResources.en || {}).forEach(([namespace, resources]) => {
      globalI18nInstance.addResourceBundle('en', namespace, resources, true, true)
    })
    Object.entries(pageResources.ja || {}).forEach(([namespace, resources]) => {
      globalI18nInstance.addResourceBundle('ja', namespace, resources, true, true)
    })
  }

  const WithTranslateProvider: FC<TProps> = props => {
    const { user } = useBoundStore()

    useEffect(() => {
      console.log('🏗️  WithTranslateProvider mounted')
      console.log('📊 Current instance count:', window.i18nInstanceCount)

      return () => {
        console.log('🗑️  WithTranslateProvider unmounted')
      }
    }, [])

    // Update language when user language changes
    useEffect(() => {
      console.log('👤 User language changed:', user?.language)
      if (user?.language && globalI18nInstance.language !== user.language) {
        console.log('🔄 Changing language from', globalI18nInstance.language, 'to', user.language)
        globalI18nInstance.changeLanguage(user.language)
      }
    }, [user?.language])

    if (ignoreInitialize) {
      return <Component {...props} />
    }

    return (
      <I18nextProvider i18n={globalI18nInstance}>
        <Component {...props} />
      </I18nextProvider>
    )
  }

  // Add display name for better debugging
  WithTranslateProvider.displayName = `WithTranslateProvider(${Component.displayName || Component.name || 'Component'})`

  return WithTranslateProvider
}

export default withTranslateProvider
