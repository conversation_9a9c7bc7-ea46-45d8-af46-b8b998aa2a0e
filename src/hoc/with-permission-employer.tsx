import { parseCookies } from 'nookies'
import type { ReactElement } from 'react'
import { useMemo } from 'react'

import configuration from '~/configuration'
import { SESSION_COOKIE_CURRENT_TENANT } from '~/core/constants/cookies'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { SpinnerIcon } from '~/core/ui/FillIcons'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

const Spinner = () => (
  <div className="flex h-screen flex-1 items-center justify-center">
    <SpinnerIcon />
  </div>
)

const withPermissionEmployerProvider = <T extends object>(Component: (props: T) => ReactElement<any> | null, planFeatureKey?: string) => {
  const WithPermissionSetting = (props: T) => {
    const cookies = parseCookies()
    const { data, isFeatureFlagOn } = useSubscriptionPlan()
    const isFeatureFlagEnable = planFeatureKey ? isFeatureFlagOn(planFeatureKey) : false

    const currentTenantCookie = cookies[SESSION_COOKIE_CURRENT_TENANT]

    // Memoize permission check to avoid recalculation
    const hasPermission = useMemo(() => {
      const currentTenantObj = currentTenantCookie ? JSON.parse(currentTenantCookie) : {}

      // No permission settings or user - show loading
      if (!currentTenantObj || Object.keys(currentTenantObj || {}).length === 0) {
        return null
      }

      if (currentTenantObj?.companyKind === AGENCY_TENANT) {
        return false
      }

      // Only direct company and feature flag disabled
      if (!!data && planFeatureKey && !isFeatureFlagEnable) {
        return false
      }

      // Check actual permission
      return true
    }, [currentTenantCookie, isFeatureFlagEnable, data])

    // Handle loading state
    if (hasPermission === null) {
      return <Spinner />
    }

    // Handle access denied - redirect
    if (!hasPermission) {
      // Use replace instead of href to avoid history entry
      window.location.replace(configuration.path.errorAccessDenied)
      return <Spinner /> // Show spinner during redirect
    }

    return <Component {...props} />
  }

  // Add display name for better debugging
  WithPermissionSetting.displayName = `WithPermissionSetting(${Component.name || 'Component'})`

  return WithPermissionSetting
}

export default withPermissionEmployerProvider
