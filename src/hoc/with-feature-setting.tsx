import type { ReactElement } from 'react'
import { useMemo } from 'react'

import configuration from '~/configuration'
import { SpinnerIcon } from '~/core/ui/FillIcons'

import useBoundStore from '~/lib/store'
import type { ITenantFeatureSettingType } from '~/lib/store/feature-setting-slice'

const Spinner = () => (
  <div className="flex h-screen flex-1 items-center justify-center">
    <SpinnerIcon />
  </div>
)

interface PermissionConfig {
  checkAccessPermission: (data?: ITenantFeatureSettingType[]) => boolean
  checkUserPermission: (data?: ITenantFeatureSettingType[]) => boolean
}

const withFeatureSettingProvider = <T extends object>(permission: PermissionConfig, Component: (props: T) => ReactElement | null) => {
  const WithFeatureSetting = (props: T) => {
    const { featureSetting, user } = useBoundStore()

    // Memoize permission check to avoid recalculation
    const hasPermission = useMemo(() => {
      // No permission settings or user - show loading
      if (!featureSetting || !user.id) {
        return null
      }

      if (!permission?.checkAccessPermission(featureSetting) || !permission?.checkUserPermission(featureSetting)) {
        return false
      }

      // Check actual permission
      return permission?.checkAccessPermission(featureSetting) && permission?.checkUserPermission(featureSetting)
    }, [featureSetting, user.id])

    // Handle loading state
    if (hasPermission === null) {
      return <Spinner />
    }

    // Handle access denied - redirect
    if (!hasPermission) {
      // Use replace instead of href to avoid history entry
      window.location.replace(!permission?.checkAccessPermission(featureSetting) ? configuration.path.error404 : configuration.path.errorAccessDenied)
      return <Spinner /> // Show spinner during redirect
    }

    // User has permission
    return <Component {...props} />
  }

  // Add display name for better debugging
  WithFeatureSetting.displayName = `WithFeatureSetting(${Component.name || 'Component'})`

  return WithFeatureSetting
}
export default withFeatureSettingProvider
