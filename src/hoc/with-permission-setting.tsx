import type { ReactElement } from 'react'
import { useMemo } from 'react'

import configuration from '~/configuration'
import { SpinnerIcon } from '~/core/ui/FillIcons'
import { adminCanAction } from '~/core/utilities/permission'

import useBoundStore from '~/lib/store'
import type { ITenantPermissionSettingType } from '~/lib/store/permission-setting-slice'

const Spinner = () => (
  <div className="flex h-screen flex-1 items-center justify-center">
    <SpinnerIcon />
  </div>
)

interface PermissionConfig {
  checkAccessPermission: (data: { permissionSetting: ITenantPermissionSettingType[]; keyModule: Array<string> }) => boolean
  keyModule: Array<string>
}

const withPermissionSettingProvider = <T extends object>(permission: PermissionConfig, Component: (props: T) => ReactElement | null) => {
  const WithPermissionSetting = (props: T) => {
    const { permissionSetting, user, currentRole } = useBoundStore()

    // Memoize permission check to avoid recalculation
    const hasPermission = useMemo(() => {
      // Admin always has access
      if (adminCanAction(currentRole?.code)) {
        return true
      }

      // No permission settings or user - show loading
      if (!permissionSetting?.length || !user.id) {
        return null
      }

      // Check actual permission
      return permission.checkAccessPermission({
        permissionSetting,
        keyModule: permission.keyModule
      })
    }, [currentRole?.code, permissionSetting, user.id])

    // Handle loading state
    if (hasPermission === null) {
      return <Spinner />
    }

    // Handle access denied - redirect
    if (!hasPermission) {
      // Use replace instead of href to avoid history entry
      window.location.replace(configuration.path.errorAccessDenied)
      return <Spinner /> // Show spinner during redirect
    }

    // User has permission
    return <Component {...props} />
  }

  // Add display name for better debugging
  WithPermissionSetting.displayName = `withPermissionSetting(${Component.name || 'Component'})`

  return WithPermissionSetting
}

export default withPermissionSettingProvider
