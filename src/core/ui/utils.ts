// @ts-nocheck
import { type ClassValue, clsx } from 'clsx'
import { Children, isValidElement } from 'react'
import { twMerge } from 'tailwind-merge'

import { LOCAL } from '~/core/constants/enum'
import type { IAttachmentsFile } from '~/core/ui/RichEditor'

/**
 * Gets only the valid children of a component,
 * and ignores any nullish or falsy child.
 *
 * @param children the children
 */
export function getValidChildren(children: React.ReactNode) {
  return Children.toArray(children).filter(child => isValidElement(child)) as React.ReactElement<any>[]
}

export function compact<T extends Record<any, any>>(object: T) {
  const clone = Object.assign({}, object)
  for (const key in clone) {
    if (clone[key] === undefined) delete clone[key]
  }
  return clone
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function debounce(fn: Function, delay = 250) {
  let timeout: string | number | NodeJS.Timeout | undefined

  return (...args: unknown[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

export const delayMilliseconds = (ms: number) => new Promise(res => setTimeout(res, ms))

export const getDisabledSelectedCondition = ({ isDisabled = false, isSelected = false }: { isDisabled?: boolean; isSelected?: boolean }) => {
  if (isDisabled == false && isSelected == false) return 'noDisabledSelected'
  if (isDisabled == false && isSelected == true) return 'noDisabledWithSelected'
  if (isDisabled == true && isSelected == false) return 'disabledNoSelect'
  if (isDisabled == true && isSelected == true) return 'disabledWithSelect'

  return 'noDisabledSelected'
}

export const trimFirstContentBreakLine = (content?: string) => {
  if (!content) return ''
  if (content && content === '<p><br></p>') return content.replace('<p><br></p>', '')
  if (content && content === '<p></p>') return content.replace('<p></p>', '')
  return content
}

export const trimHtmlWhitespaceExceptSpanStrong = (html: string) => {
  // Temporarily protect the content inside <span> and <strong> using placeholders
  const placeholders: Array<string> = []
  let protectedHtml = html.replace(/<(span|strong)[^>]*>.*?<\/\1>/gs, match => {
    const placeholder = `__PLACEHOLDER_${placeholders.length}__`
    placeholders.push(match)
    return placeholder
  })

  // Trim whitespace between HTML tags
  protectedHtml = protectedHtml
    .replace(/>\s+</g, '><')
    .replace(/>([^<]+)</g, (match, text) => {
      return '>' + text.trim() + '<'
    })
    .trim()

  // Restore the protected tags back to their original content
  placeholders.forEach((original, index) => {
    protectedHtml = protectedHtml.replace(`__PLACEHOLDER_${index}__`, original)
  })

  return protectedHtml
}

export function removeAccents(str: string): string {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D')
}

/**
 * Generates a RFC4122 version 4 compliant UUID
 * @returns A string representing a UUID v4
 */
export const uuidV4 = (): string => {
  return (String(1e7) + String(-1e3) + String(-4e3) + String(-8e3) + String(-1e11)).replace(/[018]/g, (c: string): string => {
    const char = parseInt(c, 10)
    return (char ^ (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (char / 4)))).toString(16)
  })
}

export const truncateTextWithDot = (text: string, textLength = 25) => {
  return text?.length > textLength ? `${text.slice(0, textLength)}...` : text
}

export const sumFilesSize = (list: Array<IAttachmentsFile & { profileId?: number }>) => {
  let totalSavedFilesSize = 0
  list.forEach(file => {
    let size = 0
    if (file.type !== LOCAL) {
      size = file?.size || 0
    } else {
      size = file?.file?.size || 0
    }
    totalSavedFilesSize = totalSavedFilesSize + size
  })
  return totalSavedFilesSize
}

export function removeChildObjects(array: any) {
  const parentIds = array.map((item: any) => item.id)

  return array.filter((item: any) => !parentIds.includes(item.parentId))
}
