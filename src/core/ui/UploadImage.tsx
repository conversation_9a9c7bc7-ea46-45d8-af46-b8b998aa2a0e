'use client'

import type { <PERSON><PERSON>vent<PERSON>andler, FC, MouseEventHandler, ReactNode } from 'react'
import { createRef, useCallback, useEffect, useState } from 'react'
import type { Point } from 'react-easy-crop'
import Cropper from 'react-easy-crop'
import { useTranslation } from 'react-i18next'

import type { AvatarProps, AvatarSize } from '~/core/ui/Avatar'
import { Avatar } from '~/core/ui/Avatar'
import { Button } from '~/core/ui/Button'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { Dialog } from '~/core/ui/Dialog'
import { AlertCircleFill } from '~/core/ui/FillIcons'
import type { ISelectOption } from '~/core/ui/Select'
import { Slider } from '~/core/ui/Slider'
import { TypographyText } from '~/core/ui/Text'

const conversionFactors: { [unit: string]: number } = {
  KB: 1024,
  MB: 1024 ** 2,
  GB: 1024 ** 3,
  TB: 1024 ** 4
}

const convertFileSizeToBytes = ({ size }: { size: string }) => {
  const sizeRegex = /^(\d+(?:\.\d+)?)\s*(KB|MB|GB|TB)$/i
  const match = size.match(sizeRegex)
  if (match) {
    const sizeInUnits = parseFloat(String(match?.[1]))
    const unit = match?.[2]?.toUpperCase() || 'KB'

    const sizeInBytes = sizeInUnits * Number(conversionFactors[unit])
    return sizeInBytes
  } else {
    throw new Error(`Invalid size format: ${size}`)
  }
}

const convertFileTypeToFileExtension = (fileType: string) =>
  ({
    'image/vnd.microsoft.icon': 'ico',
    'image/x-icon': 'ico',
    'image/svg+xml': 'svg',
    'image/gif': 'gif',
    'image/jpeg': 'jpeg',
    'image/png': 'png',
    'image/jpg': 'jpg'
  })[fileType] || 'jpg'

const blobToFile = (theBlob: Blob, fileName: string, options?: FilePropertyBag): File => {
  return new File([theBlob], fileName, options)
}

const createImage = (url: string) =>
  new Promise<HTMLImageElement>((resolve, reject) => {
    const image = new Image()
    image.addEventListener('load', () => resolve(image))
    image.addEventListener('error', error => reject(error))
    image.setAttribute('crossOrigin', 'anonymous') // needed to avoid cross-origin issues on CodeSandbox
    image.src = url
  })

function getRadianAngle(degreeValue: number) {
  return (degreeValue * Math.PI) / 180
}

function rotateSize({ width, height, rotation }: { width: number; height: number; rotation: number }) {
  const rotRad = getRadianAngle(rotation)

  return {
    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),
    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height)
  }
}

async function getCroppedImg({
  imageSrc,
  pixelCrop,
  rotation = 0,
  flip = { horizontal: false, vertical: false },
  cropImageFileType = 'image/jpeg'
}: {
  imageSrc: string
  pixelCrop: { x: number; y: number; width: number; height: number }
  rotation?: number
  flip?: { horizontal: boolean; vertical: boolean }
  cropImageFileType?: string
}) {
  const image = await createImage(imageSrc)
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    return null
  }

  const rotRad = getRadianAngle(rotation)

  // calculate bounding box of the rotated image
  const { width: bBoxWidth, height: bBoxHeight } = rotateSize({
    width: image.width,
    height: image.height,
    rotation
  })

  // set canvas size to match the bounding box
  canvas.width = bBoxWidth
  canvas.height = bBoxHeight

  // translate canvas context to a central location to allow rotating and flipping around the center
  ctx.translate(bBoxWidth / 2, bBoxHeight / 2)
  ctx.rotate(rotRad)
  ctx.scale(flip.horizontal ? -1 : 1, flip.vertical ? -1 : 1)
  ctx.translate(-image.width / 2, -image.height / 2)

  // draw rotated image
  ctx.drawImage(image, 0, 0)

  // croppedAreaPixels values are bounding box relative
  // extract the cropped image using these values
  const data = ctx.getImageData(pixelCrop.x, pixelCrop.y, pixelCrop.width, pixelCrop.height)

  // set canvas width to final desired crop size - this will clear existing context
  canvas.width = pixelCrop.width
  canvas.height = pixelCrop.height

  // paste generated rotate image at the top left corner
  ctx.putImageData(data, 0, 0)

  // As Base64 string
  // return canvas.toDataURL('image/jpeg')

  const cropImage = await new Promise((resolve, reject) => {
    canvas.toBlob(file => {
      file ? resolve(file) : resolve('')
    }, cropImageFileType)
  })

  // As a blob
  return {
    cropImage,
    cropImageBase64: canvas.toDataURL(cropImageFileType)
  }
}

interface UploadImageDialogProps {
  open?: boolean
  modal?: boolean
  className?: string
  title?: string
  autoConvertBlobToFile?: boolean
  destroy: () => void
  onCropDataCallback?: ({ image, imageBase64 }: { image: object; imageBase64: string }) => void
  image: string
  imageConvertFileType?: string //image/jpeg, image/png, image/jpg
  aspectRatio?: number
  configText?: {
    [string: string]: string
  }
  ratioOptions?: ISelectOption[]
}

const RATIO_OPTIONS: ISelectOption[] = [
  {
    value: '1/1',
    supportingObj: {
      name: '1:1'
    }
  },
  {
    value: '3/2',
    supportingObj: {
      name: '3:2'
    }
  },
  {
    value: '3/4',
    supportingObj: {
      name: '3:4'
    }
  },
  {
    value: '4/3',
    supportingObj: {
      name: '4:3'
    }
  },
  {
    value: '16/9',
    supportingObj: {
      name: '16:9'
    }
  },
  {
    value: '21/9',
    supportingObj: {
      name: '21:9'
    }
  },
  {
    value: '20/4',
    supportingObj: {
      name: '20:4'
    }
  }
]
export const RATIO_OPTIONS_CV: ISelectOption[] = [
  {
    value: '1/1',
    supportingObj: {
      name: '1:1'
    }
  },
  {
    value: '3/4',
    supportingObj: {
      name: '3:4'
    }
  }
]

const UploadImageDialog: FC<UploadImageDialogProps> = ({
  open = false,
  modal = true,
  className = '',
  title = '',
  image,
  imageConvertFileType,
  aspectRatio = 1,
  destroy,
  onCropDataCallback,
  autoConvertBlobToFile = false,
  configText,
  ratioOptions
}) => {
  const { t } = useTranslation()
  const cropperRef = createRef<any>()
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 })
  const [aspectRationOption, setAspectRatioOption] = useState<ISelectOption | undefined>(ratioOptions ? ratioOptions[0] : RATIO_OPTIONS[0])
  const [ready, setReady] = useState(false)
  const [zoom, setZoom] = useState(1)
  const [isCropping, setIsCropping] = useState(false)

  const onCropData = useCallback(() => {
    if (typeof cropperRef.current?.getCropData() !== 'undefined') {
      const { croppedAreaPixels } = cropperRef.current?.getCropData()
      setIsCropping(true)
      getCroppedImg({
        imageSrc: image,
        pixelCrop: croppedAreaPixels,
        cropImageFileType: imageConvertFileType
      })
        .then(result => {
          const { cropImage, cropImageBase64 } = result as {
            cropImage: object
            cropImageBase64: string
          }

          onCropDataCallback &&
            cropImage &&
            onCropDataCallback({
              image:
                !autoConvertBlobToFile || !imageConvertFileType
                  ? cropImage
                  : blobToFile(cropImage as Blob, `fileName.${convertFileTypeToFileExtension(imageConvertFileType)}`, { type: imageConvertFileType }),
              imageBase64: cropImageBase64
            })
        })
        .finally(() => {
          setIsCropping(false)
        })
    }
  }, [cropperRef, image, onCropDataCallback, autoConvertBlobToFile])

  useEffect(() => {
    if (!open) {
      setReady(false)
      return
    } else {
      const timeout = setTimeout(() => setReady(true), 500) // Delay to let layout settle
      return () => clearTimeout(timeout)
    }
  }, [open])

  return (
    <Dialog
      open={open}
      onOpenChange={destroy}
      isPreventAutoFocusDialog={true}
      modal={modal}
      label={title}
      className={`tablet:w-[480px] tablet:max-w-[480px] w-full max-w-[343px] ${className}`}
      headingClassName=""
    >
      <div>
        <div id="crop-container" className="relative mb-5" style={{ height: 500, width: 432 }}>
          {ready && (
            <Cropper
              ref={cropperRef}
              image={image}
              crop={crop}
              zoom={zoom}
              showGrid={false}
              aspect={aspectRationOption ? eval(aspectRationOption.value) : 1}
              onCropChange={setCrop}
              onZoomChange={setZoom}
              objectFit="horizontal-cover"
            />
          )}
        </div>
        <div className="mb-6">
          <Slider value={[zoom]} min={1} max={3} step={0.1} onValueChange={value => setZoom(Number(value[0]))} />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <TypographyText className="text-sm font-medium text-gray-700">{t('label:imageRatio')}</TypographyText>
            <ComboboxSelect
              type="unstyled"
              onChange={value => setAspectRatioOption(value as ISelectOption)}
              size="sm"
              value={aspectRationOption}
              isSearchable={false}
              menuOptionSide="bottom"
              isClearable={false}
              menuOptionAlign="start"
              placeholder={`${t('company:select')}`}
              dropdownMenuClassName="min-w-[100px] max-w-[100px]"
              options={ratioOptions ? ratioOptions : RATIO_OPTIONS}
            />
          </div>
          <div className="flex">
            <Button configurations="default" className="mr-3" label={configText?.cancel || 'Cancel'} onClick={destroy} size="sm" type="secondary" />
            <Button configurations="default" label={configText?.cropImage || 'Crop Image'} onClick={onCropData} size="sm" isLoading={isCropping} />
          </div>
        </div>
      </div>
    </Dialog>
  )
}

const validatorImageFileSize = ({ file, maximumSize = '10MB' }: { file: File; maximumSize?: string }) => {
  //validate file size
  const maximumFileSizeInBytes = convertFileSizeToBytes({ size: maximumSize })
  const fileSize = file?.size
  return maximumFileSizeInBytes > fileSize
}

interface UploadImageProps {
  type?: 'default' | 'avatar-only' | 'function-only'
  dialogCropperTitle?: string
  value?: string
  maximumSize?: string
  onChange?: (value: object) => void
  onChangeBase64?: (value: string) => void
  shape?: 'rounded' | 'circular'
  size?: AvatarSize
  color?: string
  alt?: string
  avatarConfig?: AvatarProps
  defaultAvatar?: boolean
  callbackGotErrorUploadImage?: (error_type: 'invalid_file_size' | 'invalid_file_type') => void
  configText?: {
    [string: string]: string
  }
  isDisabled?: boolean
  aspectRatio?: number
  imageConvertFileType?: string //image/jpeg, image/png, image/jpg
  acceptFileType?: string
  autoConvertBlobToFile?: boolean

  children?: (onUploadButtonClick: () => void, errorKeys: Array<string>, imageSrc: string) => ReactNode
  ratioOptions?: ISelectOption[]
}

const ACCEPT_IMAGE_FILE_TYPES = `image/jpeg, image/png, image/jpg`
const UploadImage: FC<UploadImageProps> = ({
  type = 'default',
  dialogCropperTitle,
  value = '',
  maximumSize = '10MB', // default image size
  onChange,
  onChangeBase64,
  shape = 'rounded',
  size,
  color,
  alt,
  defaultAvatar = true,
  avatarConfig = {},
  callbackGotErrorUploadImage,
  configText,
  isDisabled,
  aspectRatio = 1,
  imageConvertFileType,
  acceptFileType = ACCEPT_IMAGE_FILE_TYPES,
  autoConvertBlobToFile,
  children,
  ratioOptions
}) => {
  const uploadImageRef = createRef<HTMLInputElement>()
  const [openCropDialog, setOpenCropDialog] = useState<boolean>(false)
  const [errors, setErrors] = useState<Array<string>>([])
  const [cropImage, setCropImage] = useState<string | undefined>(value)
  const [valueState, setValueState] = useState<string>(value)
  const [fileType, setFileType] = useState<string | undefined>()
  const onInputImageFileChange = useCallback<ChangeEventHandler<HTMLInputElement>>(
    event => {
      event.preventDefault()
      const files = event.target ? event.target.files : []

      if (files) {
        //validate
        const isValidFileType = acceptFileType.split(', ').includes(String(files?.[0]?.type))
        const isValidFileSize = validatorImageFileSize({
          file: files[0] as File,
          maximumSize
        })

        if (!isValidFileSize) {
          callbackGotErrorUploadImage && callbackGotErrorUploadImage('invalid_file_size')
          return setErrors(['invalid_file_size'])
        } else if (!isValidFileType) {
          callbackGotErrorUploadImage && callbackGotErrorUploadImage('invalid_file_type')
          return setErrors(['invalid_file_type'])
        } else setErrors([])

        setFileType(String(files?.[0]?.type))
        const reader = new FileReader()
        reader.addEventListener(
          'load',
          () => {
            setCropImage(reader.result as string)
          },
          false
        )
        reader.readAsDataURL(files[0] as Blob)

        if (isValidFileType) {
          setOpenCropDialog(true)
        } else {
          setErrors([])
        }

        return () => reader.removeEventListener('load', () => {})
      }
    },
    [maximumSize, callbackGotErrorUploadImage]
  )

  const onClickUpload: MouseEventHandler<HTMLInputElement> = useCallback(event => {
    event.currentTarget.value = ''
  }, [])

  const onCropDataCallback = useCallback(
    ({ image, imageBase64 }: { image?: object; imageBase64?: string }) => {
      image && onChange && onChange(image)
      imageBase64 && onChangeBase64 && onChangeBase64(imageBase64)
      imageBase64 && setValueState && setValueState(imageBase64)
      setOpenCropDialog(false)
    },
    [onChange, onChangeBase64]
  )

  const onUploadButtonClick = useCallback(() => {
    if (uploadImageRef.current) {
      uploadImageRef.current.click()
    }
  }, [uploadImageRef])

  const onCloseCropDialog = useCallback(() => {
    if (uploadImageRef.current) {
      uploadImageRef.current.value = ''
    }
    setOpenCropDialog(false)
  }, [uploadImageRef])

  if (type === 'function-only') {
    return (
      <>
        <input
          disabled={isDisabled}
          ref={uploadImageRef}
          type="file"
          className="hidden"
          accept={acceptFileType}
          onChange={onInputImageFileChange}
          onClick={onClickUpload}
        />
        {cropImage && (
          <UploadImageDialog
            open={openCropDialog}
            autoConvertBlobToFile={autoConvertBlobToFile}
            destroy={onCloseCropDialog}
            title={dialogCropperTitle}
            onCropDataCallback={onCropDataCallback}
            image={cropImage}
            aspectRatio={aspectRatio}
            configText={configText}
            imageConvertFileType={imageConvertFileType || fileType}
            ratioOptions={ratioOptions}
          />
        )}
        {children && children(onUploadButtonClick, errors, valueState)}
      </>
    )
  }

  if (type === 'avatar-only') {
    return (
      <>
        <input
          disabled={isDisabled}
          ref={uploadImageRef}
          type="file"
          className="hidden"
          accept={acceptFileType}
          onChange={onInputImageFileChange}
          onClick={onClickUpload}
        />

        <div className="flex items-center">
          <Avatar typeUpload src={valueState} shape={shape} onClick={onUploadButtonClick} size={size} {...avatarConfig} />
        </div>

        {cropImage && (
          <UploadImageDialog
            open={openCropDialog}
            destroy={onCloseCropDialog}
            title={dialogCropperTitle}
            onCropDataCallback={onCropDataCallback}
            image={cropImage}
            aspectRatio={aspectRatio}
            configText={configText}
            imageConvertFileType={imageConvertFileType || fileType}
            autoConvertBlobToFile={autoConvertBlobToFile}
            ratioOptions={ratioOptions}
          />
        )}
      </>
    )
  }

  return (
    <>
      <input
        disabled={isDisabled}
        ref={uploadImageRef}
        type="file"
        className="hidden"
        accept={acceptFileType}
        onChange={onInputImageFileChange}
        onClick={onClickUpload}
      />
      <div className="flex items-center">
        <div className="mr-4">
          <Avatar
            color={color}
            alt={alt}
            src={valueState}
            shape={shape}
            onClick={onUploadButtonClick}
            defaultAvatar={defaultAvatar}
            {...avatarConfig}
          />
        </div>

        <div className="">
          <div>
            <Button
              configurations="default"
              label={configText?.upload || 'Upload'}
              htmlType="button"
              onClick={onUploadButtonClick}
              size="xs"
              type="secondary"
            />
          </div>
          {errors.length > 0 ? (
            <div className="mt-1.5 flex items-center">
              <div className="mr-1.5">
                <AlertCircleFill />
              </div>

              <p className="text-xs text-red-500">{configText?.supportedFormat || `Only supported png, jpg, jpeg. Maximum size is ${maximumSize}`}</p>
            </div>
          ) : (
            <p className="mt-1.5 text-xs text-gray-600">
              {configText?.supportedFormat || `Only supported png, jpg, jpeg. Maximum size is ${maximumSize}`}
            </p>
          )}
        </div>
      </div>

      {cropImage && (
        <UploadImageDialog
          open={openCropDialog}
          destroy={onCloseCropDialog}
          title={dialogCropperTitle}
          onCropDataCallback={onCropDataCallback}
          image={cropImage}
          aspectRatio={aspectRatio}
          configText={configText}
          imageConvertFileType={imageConvertFileType || fileType}
          autoConvertBlobToFile={autoConvertBlobToFile}
          ratioOptions={ratioOptions}
        />
      )}
    </>
  )
}

export { UploadImage, UploadImageDialog }
export type { UploadImageProps }
