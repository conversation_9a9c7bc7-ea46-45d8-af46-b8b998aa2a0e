import type { KeyboardEventHandler } from 'react'

import configuration from '~/configuration'

import type { ISelectOption } from '../@types/global'

const timeArrayMode: Array<string> = ['AM', 'PM']
const timeArrayHours: Array<number> = [12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
const timeArrayMinutes: Array<number> = [0, 15, 30, 45]
const to2Digit = (num: number) => ('0' + num).slice(-2)

export const selectTimeList = timeArrayMode.reduce((list: Array<ISelectOption>, A: string) => {
  return [
    ...list,
    ...timeArrayHours.reduce((listH: Array<ISelectOption>, hour: number) => {
      return [
        ...listH,
        ...timeArrayMinutes.map((minute: number) => ({
          value: `${to2Digit(hour)}:${to2Digit(minute)} ${A}`,
          label: `${to2Digit(hour)}:${to2Digit(minute)} ${A}`
        }))
      ]
    }, [])
  ]
}, [])

export const removeAttributesFromServer = (source: Array<{ attributes: { id: string; name: string } }>) => {
  return source.map(item => item.attributes)
}

export function capitalizeFirstLetter(stringValue: string) {
  if (!stringValue || stringValue === '') return stringValue
  return stringValue.charAt(0).toUpperCase() + stringValue.slice(1)
}

export const mappingQueryFromGraphQL = (source: Array<{ id: string; name: string }>) => {
  return source.map(item => ({
    value: String(item.id),
    supportingObj: {
      name: capitalizeFirstLetter(String(item.name))
    }
  }))
}

export const getDisabledSelectedCondition = ({ isDisabled = false, isSelected = false }: { isDisabled?: boolean; isSelected?: boolean }) => {
  if (isDisabled == false && isSelected == false) return 'noDisabledSelected'
  if (isDisabled == false && isSelected == true) return 'noDisabledWithSelected'
  if (isDisabled == true && isSelected == false) return 'disabledNoSelect'
  if (isDisabled == true && isSelected == true) return 'disabledWithSelect'

  return 'noDisabledSelected'
}

export function isHTML(content: string): boolean {
  if (content && content.match('</[^>]+>')) {
    return true
  }
  return false
}

export const trimFirstContentBreakLine = (content?: string) => {
  if (!content) return ''
  if (content && content === '<p><br></p>') return content.replace('<p><br></p>', '')
  if (content && content === '<p></p>') return content.replace('<p></p>', '')
  return content
}

export function removeAccents(str: string): string {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D')
}

export function checkCvFileType(fileName?: string, type?: string): boolean {
  if (!fileName) return false
  return !!fileName.toLowerCase().match(`\\.${type}`)
}

export function getMonths() {
  let i = 1
  const endInt = 12
  const temp = []

  while (i <= endInt) {
    temp.push({
      value: i.toString(),
      supportingObj: {
        name: i.toString()
      }
    })
    i++
  }

  return temp
}

export function getYears() {
  let y = new Date().getFullYear()
  const tempYear = []

  while (y >= 1960) {
    tempYear.push({
      value: y.toString(),
      supportingObj: {
        name: y.toString()
      }
    })
    y--
  }

  return tempYear
}

export function getFoundedYears() {
  let y = new Date().getFullYear()
  const tempYear = []

  while (y >= 1800) {
    tempYear.push({
      value: y.toString(),
      supportingObj: {
        name: y.toString()
      }
    })
    y--
  }

  return tempYear
}

export const regexEmailValidation = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i
export const regexMultiEmailValidation = /[\w.-]+@[\w.-]+\.\w+/g
export const regexEmailDomainValidation = /^((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i

export const currentTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone

export const formatAddressLocation = ({
  location
}: {
  location: {
    address?: string
    name?: string
    city?: string
    state?: string
    country?: string
  }
}) => {
  const arr = [location.name || location.address, location.city, location.state, location.country]
  const filterEmpty = arr.filter(item => item)

  return filterEmpty.map(item => item).join(', ')
}

export const fetchAndDownloadFile = ({ file, name }: { file: string; name?: string }) => {
  if (!file) return

  return fetch(file).then(response => {
    response.blob().then(blob => {
      const url = window.URL.createObjectURL(new Blob([blob]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', name || file)
      document.body.appendChild(link)
      link.click()
      link.remove()
    })
  })
}

export const trimAllEmptySpace = (text: string) => {
  if (!text) return ''
  return text.replace(/^\s+|\s+$/gm, '')
}

export const removeHTMLTags = (content: string): string => {
  return content.replace(/(<([^>]+)>)/gi, '')
}

export const truncateTextWithDot = (text: string, textLength = 25) => {
  return text?.length > textLength ? `${text.slice(0, textLength)}...` : text
}

export const convertStringArrayToArray = (message: string) => {
  if (!message) return []
  return typeof message === 'string' && message[0] === '[' ? JSON.parse(message.replace(/\'/g, '')) : []
}

export const getTimeZone = (date: string) => {
  const tz = date.match(/[-\+][0-9][0-9]:[0-9][0-9]/)?.[0]
  return tz?.includes('00:00') ? '' : tz
}

export const sendChromeRuntimeMessage = (paramsCookie: unknown) => {
  const params = new URL(String(window.location)).searchParams
  const returnUrl = params.get('returnUrl')

  if (returnUrl && returnUrl.startsWith('/ext')) {
    const myInterval = setInterval(myTimer, 1000)

    function myTimer() {
      //@ts-ignore - doesn't need to fix
      if (chrome?.runtime?.sendMessage) {
        clearInterval(myInterval)

        //@ts-ignore - doesn't need to fix
        chrome.runtime.sendMessage(extensionId, { callbackDomain: { cookies: paramsCookie, extensionId } }, function () {
          return Promise.resolve()
        })
      }
    }

    const splitParam = returnUrl.split('/')
    const extensionId = splitParam.pop()
  }

  return Promise.resolve()
}

export const reorder = (list = [] as any, startIndex = 0, endIndex = 0) => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)

  return result as any
}

export function nonAccentVietnamese(str: string) {
  if (!str || typeof str !== 'string') {
    return ''
  }
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a')
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e')
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i')
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o')
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u')
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y')
  str = str.replace(/đ/g, 'd')
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A')
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E')
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I')
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O')
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U')
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y')
  str = str.replace(/Đ/g, 'D')
  return str
}

export const removeEndSplash = (link: string) => link?.replace(/\/$/g, '')

export const scrollToElement = (id: string) => {
  const element = document.getElementById(id)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

export const removeTags = (string: string) => {
  return string
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s{2,}/g, ' ')
    .replaceAll('&nbsp;', ' ')
    .replace(/[0-9]([0-9])?./g, '')
    .trim()
}

export const checkIfArrayStringsContainsObjectKeys = (arrayKeys: Array<string>, objectName: any) => {
  for (let i = 0; i < arrayKeys.length; i++) {
    const keyName = arrayKeys[i]
    if (Object.keys(objectName || {}).some(key => key === keyName)) {
      return true
    }
  }
  return false
}

export const createPreventPositiveChange = (onChange: (value: string | number) => void) => ({
  onKeyDown: (event => {
    if (event.key === '-' || event.key === 'e') {
      event.preventDefault()
    }
  }) as KeyboardEventHandler<HTMLInputElement>,
  onChange: (value: string | number) => {
    if (/^\d*$/.test(value.toString())) {
      onChange(value)
    }
  }
})
export const regexInputUrlValidation = /^[a-zA-Z0-9-_]+$/

export const isInCareerHubApp = (path?: string) => (path || '').includes('careerhub') && !(path || '').includes('settings/careerhub')

export const detectSearchEmpty = ({
  filters,
  filterKeys
}: {
  filters: {
    [key: string]: unknown
  }
  filterKeys: string[]
}) => {
  let isSearchEmpty = false

  for (let index = 0; index < filterKeys.length; index++) {
    const filterKey = filterKeys[index]
    if (filters?.[`${filterKey}`]) {
      isSearchEmpty = true
    }
  }

  return isSearchEmpty
}

export const redirectToAccessDenied = () => {
  // Add /dashboard to browser history
  window.history.pushState(null, '', configuration.path.default)
  window.location.href = configuration.path.errorAccessDenied
}

export const removeEmptyParagraphs = (html: string) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')

  const paragraphs = doc.querySelectorAll('p')

  paragraphs.forEach(p => {
    const hasContent = p.textContent?.trim()

    const hasChildren = Array.from(p.children).some(child => (child.textContent?.trim().length || 0) > 0)
    const isEmpty = !hasContent && !hasChildren
    if (isEmpty) {
      p.remove()
    }
  })

  return doc.body.innerHTML
}
export const difference = (arr1: Array<any>, arr2: Array<any>, key: string) => {
  return arr1.filter(item1 => !arr2.some(item2 => item1[key] === item2[key]))
}

export const calculateVisibleItems = (itemHeight: number, numberOfItems: number, numberAdded: number) => {
  return itemHeight * numberOfItems + numberAdded
}

export const convertLinkFromHTML = (content: string) => {
  if (!content) return ''

  function convertURLLink(inputText: string) {
    //URLs starting with http://, https://, or ftp://
    const replacePattern1 = /(\b(https?|ftp):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim
    var replacedText = inputText.replace(
      replacePattern1,
      `<span class="break-all"><a target="_blank" rel="noopener noreferrer nofollow" href="$1">$1</a></span>`
    )

    //URLs starting with www. (without // before it, or it'd re-link the ones done above)
    const replacePattern2 = /(^|[^\/])(www\.[\S]+(\b|$))/gim
    var replacedText = replacedText.replace(
      replacePattern2,
      '$1<a target="_blank" rel="noopener noreferrer nofollow" href="http://$2" target="_blank">$2</a>'
    )

    //Change email addresses to mailto:: links
    const replacePattern3 = /(([a-zA-Z0-9_\-\.]+)@[a-zA-Z_]+?(?:\.[a-zA-Z]{2,6}))+/gim
    var replacedText = replacedText.replace(replacePattern3, '<a href="mailto:$1">$1</a>')

    return replacedText
  }

  const wordsNew = content.substring(0, 3) === '<p>' ? content : `<p>${content}</p>`
  const wordsRemoved = wordsNew.replace(/<a .*?>/g, ' ').replace(/<\/a>/g, ' ')
  const wordsSplit = wordsRemoved.split(' ')
  const wordsFormatLink = wordsSplit.map(w => convertURLLink(w)).join(' ')
  const formatContent = removeEmptyParagraphs(wordsFormatLink)
  return formatContent
}

export const formatDateValue = ({ value }: { value: unknown }) => {
  const formatRangeSelection = value as {
    from?: string
    to?: string
  }
  if (formatRangeSelection?.from && formatRangeSelection?.to) {
    return true
  }

  const formatSelection = value as {
    year?: number
    month?: number
    date?: number
  }

  if (formatSelection?.year && !formatSelection?.month && !formatSelection?.date) {
    return !!formatSelection?.year
  }

  if (!formatSelection?.year && !formatSelection?.month && !formatSelection?.date) {
    if (formatSelection && Object.keys(formatSelection).length === 0) {
      return true
    }

    return false
  }

  return true
}

export const convertDecimalHoursToHM = (decimalHours: number) => {
  const hours = Math.floor(decimalHours)
  const decimalMinutes = (decimalHours - hours) * 60
  const minutes = Math.max(Math.round(decimalMinutes), 0)
  const seconds = Math.max(Math.round((decimalMinutes - minutes) * 60), 0)

  if (hours === 0 && minutes === 0 && seconds === 0) return null
  return {
    hour: hours,
    minute: minutes,
    second: seconds
  }
}

interface QueryParams {
  [key: string]: string | string[] | undefined | null
}

export const buildURL = (pathname: string, queryParams: QueryParams) => {
  const searchParams = new URLSearchParams()

  Object.entries(queryParams).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      searchParams.append(key, value.join(','))
    } else if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value)
    }
  })

  return searchParams.toString() ? `${pathname}?${searchParams.toString()}` : pathname
}

export const convertToSelectOptions = (input: string | string[] | undefined): ISelectOption[] | '' => {
  if (!input) return ''

  return (Array.isArray(input) ? input : [input]).filter(Boolean).map(value => ({
    value,
    supportingObj: {
      name: ''
    }
  }))
}

export const getNameFromEnum = (options: ISelectOption[] | undefined, value: string): string | undefined => {
  const found = options && options?.find(option => option.value === value)
  return found?.supportingObj?.name
}

export const convertObjectWithEnum = (optionsEnum: ISelectOption[], options: ISelectOption[] | undefined) => {
  const res = optionsEnum?.filter(op => options?.some(op1 => op1.value.toString() === op.value.toString()))

  if (res.length === 0) return undefined
  return res
}

export const hasAtLeastOneValue = (value: any, excludes: string[]) => {
  const valuesCheck = { ...value }

  for (const key of excludes) {
    delete valuesCheck[key]
  }

  return Object.values(valuesCheck).some(val => val !== undefined && val !== null && val !== '' && (Array.isArray(val) ? val.length > 0 : true))
}
export const convertValueToHTMLFromSearch = ({ value, searchValue }: { value: string | number; searchValue?: string }) => {
  if (!searchValue) return String(value)

  let convertValue = '-'
  const formatValue = String(value)

  if (searchValue) {
    const regExp = new RegExp(searchValue, 'gi')
    convertValue = formatValue.replace(regExp, '<mark>$&</mark>')
  }

  return convertValue
}

export const insertKeyInOrder = (obj: Record<string, any>, key: string, value: any, afterKey: string) => {
  const newObj: Record<string, any> = {}
  for (const k of Object.keys(obj)) {
    newObj[k] = obj[k]
    if (k === afterKey) {
      newObj[key] = value
    }
  }
  return newObj
}
