import { Children, isValidElement } from 'react'

/**
 * Gets only the valid children of a component,
 * and ignores any nullish or falsy child.
 *
 * @param children the children
 */
export function getValidChildren(children: React.ReactNode) {
  return Children.toArray(children).filter(child => isValidElement(child)) as React.ReactElement<any>[]
}

export function compact<T extends Record<any, any>>(object: T) {
  const clone = Object.assign({}, object)
  for (const key in clone) {
    if (clone[key] === undefined) delete clone[key]
  }
  return clone
}
