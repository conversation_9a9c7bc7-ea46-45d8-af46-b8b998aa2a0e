'use client'

import { format<PERSON><PERSON> } from 'date-fns'
import { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import type { FieldValues } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { AGENCY_TENANT, TAB_CANDIDATES, TAB_COMPANIES } from '~/core/constants/enum'
import type { ISelectOption } from '~/core/ui/Select'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { adminAndMemberCanAction, limitedMemberCanAction } from '~/core/utilities/permission'

import UpdateCompanyTaskMutation from '~/lib/features/agency/companies/graphql/update-company-task-mutation'
import useInterviewsManagement from '~/lib/features/calendar/hooks/use-interview-management'
import useScheduleInterviewCalendarHook from '~/lib/features/calendar/hooks/use-schedule-interview-calendar'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import CreateTaskAgencyMutation from '~/lib/features/tasks/graphql/create-task-agency-mutation'
import CreateTaskMutation from '~/lib/features/tasks/graphql/create-task-mutation'
import useTasksManagement from '~/lib/features/tasks/hooks/use-tasks-management'
import type { TaskEditModalProps } from '~/lib/features/tasks/types'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import InterviewCalendarFilter from '~/components/Calendar/InterviewCalendarFilter'
import InterviewCalendarView from '~/components/Calendar/InterviewCalendarView'
import ScheduleInterviewModal from '~/components/Calendar/ScheduleInterview/ScheduleInterviewModal'
import TaskCalendarFilter from '~/components/Calendar/Tasks/TaskCalendarFilter'
import TaskCalendarFormModal from '~/components/Calendar/Tasks/TaskCalendarFormModal'
import TaskCalendarView from '~/components/Calendar/Tasks/TaskCalendarView'
import LayoutGrid from '~/components/Layout/LayoutGrid'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

export const TAB_CALENDAR_KEYS = [{ value: 'interview' }, { value: 'task' }]

const InterviewsContainer = () => {
  const { user, currentRole, refetchMyList, setRefetchMyList } = useBoundStore()
  const { t } = useTranslation()
  const [tab, setTab] = useState<string>(String(TAB_CALENDAR_KEYS[0]?.value))
  const [openCreateTask, setOpenCreateTask] = useState(false)
  const [editTask, setEditTask] = useState<TaskEditModalProps>({
    open: false
  })
  const { interviewsListControl, filterControl, action } = useInterviewsManagement({ user })
  const { trigger, isLoading } = useSubmitCommon(CreateTaskMutation)
  const { trigger: companyTaskTrigger, isLoading: isCompanyTaskTriggerLoading } = useSubmitCommon(CreateTaskAgencyMutation)

  const { trigger: updateCompanyTaskTrigger, isLoading: isUpdateCompanyTaskTriggerLoading } = useSubmitCommon(UpdateCompanyTaskMutation)

  const { setToast } = useToastStore()
  const currentTab = useBoundStore(state => state.currentTabRelated)
  const setCurrentTab = useBoundStore(state => state.setCurrentTabRelated)
  const { taskPaging, filterControl: taskFilterControl, action: taskAction } = useTasksManagement({ user, isCalendar: true })

  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const { isUnLockFeature, isFeatureEnabled } = useSubscriptionPlan()

  const isShowCompanyFeature = isFeatureEnabled(PLAN_FEATURE_KEYS.company) && isUnLockFeature(PLAN_FEATURE_KEYS.company)
  const { editModal, setEditModal, onCloseEditModal, onReopenEditModal, onScheduleInterview, interview, fetchAssigneesOptions } =
    useScheduleInterviewCalendarHook({
      updateInterview: action.interviewUpdateAction.updateInterview
    })
  const onChangeTab = useCallback((value: string) => {
    setTab(value)
  }, [])
  const onSubmitCreateTask = async (data: FieldValues) => {
    if (isLoading) return
    if (isCompanyTaskTriggerLoading) return
    const formatData = {
      title: data.title,
      ...(data.dueDate ? { dueDate: formatISO(data.dueDate) } : {}),
      assigneeIds: data.assigneeIds.map((assignee: ISelectOption) => Number(assignee.value)),
      ...(currentTab === TAB_COMPANIES && data?.relatedIds
        ? { companyId: Number(data.relatedIds?.value) }
        : { profileId: Number(data.relatedIds?.value) })
    }
    {
      ;(isCompanyKind || isShowCompanyFeature) && currentTab === TAB_COMPANIES
        ? companyTaskTrigger(formatData).then(result => {
            if (result.error) {
              return catchErrorFromGraphQL({
                error: result.error,
                page: pathConfiguration.tasks.list,
                setToast
              })
            }
            const { companyTasksCreate } = result.data
            if (companyTasksCreate?.task?.id) {
              setToast({
                open: true,
                type: 'success',
                title: t('notification:task:taskCreated'),
                classNameConfig: {
                  viewport: 'mb-[48px]'
                }
              })
              taskPaging && taskPaging.refetch()
            }
            setOpenCreateTask(false)
            return
          })
        : trigger(formatData).then(result => {
            if (result.error) {
              return catchErrorFromGraphQL({
                error: result.error,
                page: pathConfiguration.tasks.list,
                setToast
              })
            }
            const { tasksCreate } = result.data
            if (tasksCreate?.task?.id) {
              setToast({
                open: true,
                type: 'success',
                title: t('notification:task:taskCreated'),
                classNameConfig: {
                  viewport: 'mb-[48px]'
                }
              })
              taskPaging && taskPaging.refetch()
            }
            setOpenCreateTask(false)
            return
          })
    }
  }
  const onSubmitEditForm = async (data: FieldValues) => {
    if (taskAction.updateTaskAction.updatingTask) return Promise.resolve()
    const formatData = {
      id: Number(editTask.task?.id),
      title: data.title,
      ...(limitedMemberCanAction(currentRole?.code) && editTask?.task?.profile?.id ? { profileId: Number(editTask?.task?.profile?.id) } : {}),
      ...(data.dueDate ? { dueDate: formatISO(data.dueDate) } : {}),
      assigneeIds: data.assigneeIds.map((assignee: ISelectOption) => Number(assignee.value)),
      ...(currentTab === TAB_COMPANIES && data?.relatedIds
        ? { companyId: Number(data.relatedIds?.value) }
        : { profileId: Number(data.relatedIds?.value) })
    }
    {
      ;(isCompanyKind || isShowCompanyFeature) && currentTab === TAB_COMPANIES
        ? updateCompanyTaskTrigger(formatData).then(result => {
            if (result.error) {
              return catchErrorFromGraphQL({
                error: result.error,
                page: pathConfiguration.tasks.list,
                setToast
              })
            }
            const { companyTasksUpdate } = result.data
            if (companyTasksUpdate?.task?.id) {
              setToast({
                open: true,
                type: 'success',
                title: t('notification:changesSaved'),
                classNameConfig: {
                  viewport: 'mb-[48px]'
                }
              })
              taskPaging && taskPaging.refetch()
            }
            setEditTask({ open: false })
            return
          })
        : taskAction.updateTaskAction.updateTask(formatData).then(result => {
            setEditTask({ open: false })
            if (result.error) {
              catchErrorFromGraphQL({
                error: result.error,
                page: pathConfiguration.tasks.list,
                setToast
              })
            }
            setToast({
              open: true,
              type: 'success',
              title: t('notification:changesSaved'),
              classNameConfig: {
                viewport: 'mb-[48px]'
              }
            })
            taskPaging.refetch()
          })
    }
  }
  useEffect(() => {
    if (refetchMyList) {
      interviewsListControl.refetch()
      setRefetchMyList(false)
    }
  }, [refetchMyList, interviewsListControl])

  return (
    <LayoutGrid background="bg-gray-50">
      <Tabs defaultValue={tab} onValueChange={onChangeTab}>
        <div className="flex h-[56px] items-center justify-between border-b border-b-gray-100 bg-white px-6 pt-[20px]">
          <TabsList size="sm">
            {TAB_CALENDAR_KEYS.map((tab, index) => (
              <TabsTrigger key={`tab-${index}`} size="sm" value={tab.value}>
                <TabsTriggerView
                  size="sm"
                  session={{
                    value: tab.value,
                    label: `${t(`label:tabs:${tab.value}`)}`
                  }}
                />
              </TabsTrigger>
            ))}
          </TabsList>

          {TAB_CALENDAR_KEYS[0]?.value === tab ? (
            adminAndMemberCanAction(currentRole?.code) && (
              <InterviewCalendarFilter fetchAssigneesOptions={fetchAssigneesOptions} filterControl={filterControl} />
            )
          ) : (
            <TaskCalendarFilter
              setOpenCreateTask={setOpenCreateTask}
              fetchAssigneesOptions={fetchAssigneesOptions}
              filterControl={taskFilterControl}
            />
          )}
        </div>
        <TabsContent value={String(TAB_CALENDAR_KEYS[0]?.value)} className="mt-0">
          <InterviewCalendarView
            filterControl={filterControl}
            interviewsListControl={interviewsListControl}
            action={action}
            interviewEdit={interview}
            setEditModalInterview={setEditModal}
          />
        </TabsContent>
        <TabsContent value={String(TAB_CALENDAR_KEYS[1]?.value)} className="mt-0">
          <TaskCalendarView taskPaging={taskPaging} action={taskAction} setEditTask={setEditTask} callBack={taskPaging.refetch} />
        </TabsContent>
      </Tabs>

      {!!interview.data && (
        <ScheduleInterviewModal
          open={editModal.open}
          onClose={editModal?.onCloseEditModal || onCloseEditModal}
          isEditInterview={true}
          reopenInterviewModal={editModal?.onReopenEditModal || onReopenEditModal}
          reload={interview.fetchData}
          interviewInfo={{
            ...interview.data,
            ...(editModal.newDataInterview || {}),
            jobStageId: interview.data?.jobStage?.id ? Number(interview.data?.jobStage?.id) : undefined
          }}
          onFinish={onScheduleInterview}
          candidateProfile={{
            id: Number(interview?.data?.profile?.id),
            email: interview?.data?.profile?.email
          }}
        />
      )}
      {openCreateTask && (
        <TaskCalendarFormModal
          callback={taskPaging?.refetch}
          open={openCreateTask}
          setOpen={() => setOpenCreateTask(false)}
          onSubmit={onSubmitCreateTask}
          isLoading={isLoading}
          defaultValue={{
            assigneeIds: [
              {
                value: String(user.id),
                avatar: user.avatarVariants?.thumb?.url,
                avatarVariants: user.avatarVariants,
                supportingObj: {
                  name: user.fullName || user.email || '',
                  defaultColour: user.defaultColour
                }
              }
            ]
          }}
        />
      )}
      {editTask.open && (
        <TaskCalendarFormModal
          callback={taskPaging?.refetch}
          open={editTask.open}
          setOpen={() => {
            setEditTask({ open: false })
          }}
          onSubmit={onSubmitEditForm}
          isLoading={isLoading}
          defaultValue={{
            id: editTask.task?.id,
            title: editTask.task?.title,
            assigneeIds: editTask.task?.assignees.map(item => ({
              value: String(item.id),
              avatar: item.avatarVariants?.thumb?.url,
              avatarVariants: item.avatarVariants,
              supportingObj: {
                name: item.fullName || item.email || '',
                defaultColour: item.defaultColour
              }
            })),
            dueDate: editTask.task?.dueDate ? new Date(editTask.task.dueDate) : undefined,
            relatedIds: editTask.task?.profile
              ? {
                  value: String(editTask.task?.profile.id),
                  supportingObj: {
                    name: editTask.task?.profile?.fullName || ''
                  }
                }
              : editTask.task?.company
                ? {
                    value: String(editTask.task?.company?.id),
                    supportingObj: {
                      name: editTask.task?.company?.permittedFields?.name?.value || ''
                    }
                  }
                : editTask.task?.applicant?.profile
                  ? {
                      value: String(editTask.task?.applicant?.profile?.id),
                      supportingObj: {
                        name: editTask.task?.applicant?.profile?.fullName || ''
                      }
                    }
                  : undefined,
            tab: editTask.task?.profile ? TAB_CANDIDATES : editTask.task?.company ? TAB_COMPANIES : undefined
          }}
        />
      )}
    </LayoutGrid>
  )
}

export default withQueryClientProvider(InterviewsContainer)
