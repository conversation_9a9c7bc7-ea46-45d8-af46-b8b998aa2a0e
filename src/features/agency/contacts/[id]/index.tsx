'use client'

import { useRouter } from 'next/navigation'
import { createContext, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import configuration from '~/configuration'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Divider } from '~/core/ui/Divider'
import type { IFormAction } from '~/core/ui/Form'
import { Tabs, TabsContent, TabsList, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'
import { cn } from '~/core/ui/utils'
import { catchError<PERSON>romGraphQL } from '~/core/utilities/catch-api-error'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { DEFAULT_PERMISSIONS_LIST } from '~/core/utilities/feature-permission'
import { pushStateBrowser } from '~/core/utilities/is-browser'

import { ACTIVITIES_TAB, JOBS_TAB } from '~/lib/features/agency/companies/utilities/company-detail-enum'
import MutationContactUpdate from '~/lib/features/agency/contacts/graphql/mutation-contact-update'
import QueryContactActivities from '~/lib/features/agency/contacts/graphql/query-contact-activities'
import QueryContactsShow from '~/lib/features/agency/contacts/graphql/query-contact-show'
import useContactInfoValidationHook from '~/lib/features/agency/contacts/hooks/contact-info-validation-hook'
import { formatUpdateContactData, mappingContactDetailData } from '~/lib/features/agency/contacts/mapping/contact-detail-mapping'
import type { IContactDetailType } from '~/lib/features/agency/contacts/types'
import MutationAddCallLog from '~/lib/features/candidates/graphql/mutation-add-call-log'
import { paramsAddCallLogMapping } from '~/lib/features/candidates/mapping/call-log-params-mapping'
import type { CallLogsFormType } from '~/lib/features/candidates/types'
import { ENUMS_CALL_TO_OBJECTS, OPTIONS_DIRECTIONS, OPTIONS_OUTCOME } from '~/lib/features/candidates/utilities/enum'
import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import { formatSubmitCustomFieldData } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import ContactCompanySection from '~/components/Agency/Contacts/[id]/ContactCompanySection'
import ContactDetailActivityTab from '~/components/Agency/Contacts/[id]/ContactDetailActivity'
import ContactDetailHeader from '~/components/Agency/Contacts/[id]/ContactDetailHeader'
import ContactDetailJobTab from '~/components/Agency/Contacts/[id]/ContactDetailJobTab'
import ContactInformation from '~/components/Agency/Contacts/[id]/ContactInformation'
import CallLogsFormModal from '~/components/Candidates/Profile/components/CallLogs/CallLogsFormModal'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import type { ISwitchLayoutView } from '~/components/SwitchLayout/SwitchLayoutView'

export const CompaniesDetailPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const ContactAgencyDetailContainer = ({
  isDrawer = false,
  setSwitchView,
  contactId
}: {
  isDrawer?: boolean
  setSwitchView?: (value: ISwitchLayoutView) => void
  contactId: number
}) => {
  const { t, i18n } = useTranslation()
  const router = useRouter()
  const { clientGraphQL } = useContextGraphQL()
  const { isCompanyKind: isAgencyTenant } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })

  const { setToast } = useToastStore()

  const { trigger: triggerAddCallLog, isLoading: isLoadingAddCallLog } = useSubmitCommon(MutationAddCallLog)
  const [openAddCallLog, setOpenAddCallLog] = useState<boolean>(false)
  const { setRefetchMyList, refetchMyList } = useBoundStore()
  const topSpace = useClassBasedTopSpace({
    34: isDrawer ? 'h-screen' : 'h-[calc(100vh-34px)]',
    default: 'h-screen'
  })
  const [count, setCount] = useState<{
    jobs?: number
  }>({
    jobs: 0
  })

  const contactTabControl = useBrowserTab({
    defaultValue: ACTIVITIES_TAB,
    queryKeyName: 'tabs',
    pushState: !!isDrawer,
    excludeQueryKeysName: ['id']
  })

  const { submitPartialField, touchingFieldRef } = useContactInfoValidationHook()

  const { data: contactData, trigger: fetchContactShow } = useQueryGraphQL({
    query: QueryContactsShow,
    variables: {
      id: Number(contactId)
    },
    shouldPause: true
  })
  const {
    data: activityList,
    hasNextPage,
    fetchNextPage,
    isLoading,
    isFetchedAfterMount,
    refetch: refetchActivityList
  } = useInfinityGraphPage({
    queryDocumentNote: useMemo(() => QueryContactActivities, []),
    getVariable: useCallback(page => ({ contactId: contactId, limit: 10, page }), [contactId]),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.contactActivitiesList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.contactActivitiesList?.collection?.length)
    }),
    queryKey: ['contact-activity-list', String(contactId)],
    enabled: !!contactId
  })

  const permittedFields = contactData?.contactsShow?.permittedFields
  const fullNameContact = [permittedFields?.firstName?.value || '', permittedFields?.lastName?.value || ''].filter(name => !!name).join(' ')

  const defaultValueContactDetail = !!contactData?.contactsShow
    ? mappingContactDetailData({
        data: contactData?.contactsShow
      })
    : {}

  const onUpdateContact = useCallback(
    (data: IContactDetailType, formAction?: IFormAction) => {
      const submittingField = touchingFieldRef.current as keyof typeof data
      const formattedData = formatUpdateContactData({
        contactId,
        submittingField,
        data: {
          [submittingField]: data[submittingField]
        }
      })

      return clientGraphQL
        .mutation(MutationContactUpdate, {
          ...formattedData,
          ...(data.customFields
            ? {
                customFields: formatSubmitCustomFieldData(data.customFields || {})
              }
            : {})
        })
        .toPromise()
        .then((result: IResponseContextResult<{}>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              formAction,
              setToast,
              callbackHandleStatusError422: keys => {
                keys.forEach(session => {
                  if (session.field && formAction?.control._fields[session.field]) {
                    setToast({
                      open: true,
                      type: 'error',
                      title: session.message
                    })
                    formAction.setError(session.field, {
                      type: 'custom',
                      message: String(session.message)
                    })
                  }
                })
              }
            })

            return false
          }

          if (result.data?.contactsUpdate) {
            fetchContactShow()
          }

          return true
        })
    },
    [contactId, touchingFieldRef]
  )

  useEffect(() => {
    if (contactId) {
      fetchContactShow()
    }
  }, [contactId])

  useEffect(() => {
    if (refetchMyList) {
      setRefetchMyList(false)
      refetchActivityList()
    }
  }, [refetchMyList])

  const onAddCallLog = async (param: CallLogsFormType, formAction?: IFormAction): Promise<void> => {
    if (isLoadingAddCallLog) {
      return
    }

    return triggerAddCallLog(paramsAddCallLogMapping(param, ENUMS_CALL_TO_OBJECTS.contact)).then(result => {
      if (result.error) {
        catchErrorFromGraphQL({
          error: result.error,
          formAction,
          setToast
        })
        return
      } else {
        const { callLogsCreate } = result.data
        if (callLogsCreate.callLog) {
          if (Number(param?.callTo?.value) === Number(contactId)) {
            setRefetchMyList(true)
            setOpenAddCallLog(false)
            setToast({
              open: true,
              type: 'success',
              title: `${t('notification:callLogs:create:toastSuccess')}`
            })
          } else {
            setOpenAddCallLog(false)
            setTimeout(() => {
              setToast({
                open: true,
                type: 'success',
                title: `${t('notification:callLogs:create:toastSuccess')}`,
                description: ``,
                duration: 2000,
                actions: [
                  {
                    alt: 'details',
                    label: `${t('button:viewDetail')}`,
                    actionType: 'detail',
                    callback: () => {
                      router.push(`${pathConfiguration.agency.contacts.detail(Number(param?.callTo?.value))}`)
                    }
                  }
                ]
              })
            }, 500)
          }
        }

        return
      }
    })
  }

  return (
    <div className={cn('flex flex-col overflow-hidden', topSpace)}>
      <div className="flex-none border-b border-b-gray-100 px-6 py-2">
        <ContactDetailHeader
          setOpenAddCallLog={setOpenAddCallLog}
          isDrawer={isDrawer}
          defaultValue={defaultValueContactDetail}
          contactDetail={contactData?.contactsShow}
          fetchContactShow={fetchContactShow}
          onSubmit={onUpdateContact}
          submitPartialField={submitPartialField}
          onCloseContactDrawer={() => {
            pushStateBrowser({
              state: {},
              unused: '',
              url: configuration.path.agency.contacts.list
            })
            setSwitchView &&
              setSwitchView({
                id: undefined,
                view: ''
              })
          }}
        />
      </div>
      <div className="flex flex-1 overflow-y-auto">
        <div className="w-[39.34%]">
          <div className="h-full overflow-y-auto px-6 pt-4 pb-10">
            <div className="mb-6">
              <ContactCompanySection
                contactId={contactId}
                data={contactData?.contactsShow?.companies || []}
                fetchContactShow={fetchContactShow}
                contactName={fullNameContact}
                setCount={setCount}
              />
            </div>
            <ContactInformation
              isDrawer={isDrawer}
              defaultValue={defaultValueContactDetail}
              contactDetail={contactData?.contactsShow}
              onSubmit={onUpdateContact}
              submitPartialField={submitPartialField}
            />
          </div>
        </div>
        <div className="w-[60.66%] overflow-hidden border-l border-l-gray-100 pt-4">
          <Tabs
            {...contactTabControl}
            onValueChange={value => {
              contactTabControl.onValueChange(value)
              if (value === ACTIVITIES_TAB) {
                refetchActivityList()
              }
            }}
          >
            <div className="px-6">
              <TabsList size="sm">
                <TabsTrigger value={ACTIVITIES_TAB} size="sm" gapSize="sm">
                  <TabsTriggerView
                    size="sm"
                    session={{
                      value: ACTIVITIES_TAB,
                      label: `${t('contact:detail:tab:activities:title')}`
                    }}
                  />
                </TabsTrigger>
                {isAgencyTenant && (
                  <TabsTrigger value={JOBS_TAB} size="sm" gapSize="sm">
                    <TabsTriggerView
                      size="sm"
                      session={{
                        value: JOBS_TAB,
                        label: `${t('contact:detail:tab:jobs:title')}`,
                        count: count?.jobs
                      }}
                    />
                  </TabsTrigger>
                )}
              </TabsList>
            </div>
            <Divider />
            <TabsContent value={ACTIVITIES_TAB} className="mt-0 pt-4">
              <ContactDetailActivityTab
                isDrawer={isDrawer}
                data={activityList}
                fetchNextPage={fetchNextPage}
                hasNextPage={hasNextPage}
                isLoading={isLoading}
                isFetchedAfterMount={isFetchedAfterMount}
              />
            </TabsContent>
            <TabsContent value={JOBS_TAB} className="mt-0 pt-3">
              <ContactDetailJobTab isDrawer={isDrawer} setCount={setCount} contactId={contactId} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
      {openAddCallLog ? (
        <CallLogsFormModal
          isContact
          isSubmitting={isLoadingAddCallLog}
          onSubmit={onAddCallLog}
          open={openAddCallLog}
          defaultValue={{
            direction: OPTIONS_DIRECTIONS(t)[0],
            outCome: OPTIONS_OUTCOME(t)[0],
            callTo: {
              value: contactId.toString(),
              supportingObj: {
                name: defaultValueContactDetail.firstName || ''
              }
            },
            callTime: new Date(),
            phoneNumber: ''
          }}
          setOpen={setOpenAddCallLog}
        />
      ) : null}
    </div>
  )
}

export default withQueryClientProvider(ContactAgencyDetailContainer)
