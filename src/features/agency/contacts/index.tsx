'use client'

import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IFormAction } from '~/core/@types/global'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { Badge } from '~/core/ui/Badge'
import { Dialog } from '~/core/ui/Dialog'
import { Drawer } from '~/core/ui/Drawer'
import Empty from '~/core/ui/Empty'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { pushStateBrowser } from '~/core/utilities/is-browser'

import type { CompanyContactFormType } from '~/lib/features/agency/companies/types/company-detail'
import MutationContactCreate from '~/lib/features/agency/contacts/graphql/mutation-contact-create'
import QueryContactsList from '~/lib/features/agency/contacts/graphql/query-contact-list'
import QueryContactsShow from '~/lib/features/agency/contacts/graphql/query-contact-show'
import type { IContactDetailType, IContactFilter } from '~/lib/features/agency/contacts/types'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import CompanyContactForm from '~/components/Agency/Companies/[id]/CompanyContactForm'
import ContactListingTable from '~/components/Agency/Contacts/ContactListingTable'
import FilterContactManagement from '~/components/Agency/Contacts/FilterContactManagement'
import { TopSpaceProvider, useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import SwitchLayoutView from '~/components/SwitchLayout/SwitchLayoutView'

import ContactAgencyDetailContainer from './[id]'

const DEFAULT_FILTER = {
  page: 1
}

const ContactsAgencyContainer = () => {
  const { clientGraphQL } = useContextGraphQL()
  const { t } = useTranslation()
  const { setRefetchMyList, refetchMyList } = useBoundStore()
  const { setToast } = useToastStore()
  const [filter, changeFilter] = useState<IContactFilter | undefined>(DEFAULT_FILTER)
  const contactRef = useRef<HTMLDivElement>(null)
  const drawerContainerRef = useRef<HTMLDivElement>(null)
  const [openCreateContact, setOpenCreateContact] = useState<boolean>(false)
  const [configSwitchLayout, setConfigSwitchLayout] = useState({
    path: [`${configuration.path.agency.contacts.list}/`],
    redirectUrls: ['']
  })
  const { trigger: triggerContactCreate, isLoading: loadingContactCreate } = useSubmitCommon(MutationContactCreate)
  const router = useRouter()
  const { pathname, params } = useRouterContext()

  const { data, refetch, fetchPagination, forceChangeCurrentPage, isFetching } = usePaginationGraphPage({
    queryDocumentNode: QueryContactsList,
    queryKey: 'my-contacts-management',
    filter: {
      companyIds: filter?.companyIds ? [Number(filter.companyIds.value)] : undefined,
      status: filter?.status ? filter.status.value : undefined,
      search: filter?.search
    }
  })

  const fetchContactAndCheckContact = useCallback(
    (
      email: string,
      formAction: IFormAction,
      session: {
        field: string
        message: string
      }
    ) => {
      return clientGraphQL
        .query(QueryContactsShow, {
          email
        })
        .toPromise()
        .then((result: { error: { graphQLErrors: Array<object> }; data: { contactsShow: IContactDetailType } }) => {
          if (result.error) {
            return catchErrorFromGraphQL({
              error: result.error,
              setToast,
              router: {
                push: router.push,
                pathname,
                params
              }
            })
          }

          const { contactsShow } = result.data
          if (contactsShow?.id) {
            formAction.setError('email', {
              type: 'custom',
              message: `[${contactsShow?.id}]`
            })
          } else {
            formAction?.setError('email', {
              type: 'custom',
              message: session.message
            })
          }

          return
        })
    },
    []
  )

  const onSubmitAddForm = useCallback(
    async (data: CompanyContactFormType, formAction?: IFormAction) => {
      if (loadingContactCreate) {
        return
      }
      triggerContactCreate({
        title: data.title,
        firstName: data.firstName || '',
        lastName: data.lastName,
        phoneNumber: !!data.phoneNumber && data.phoneNumber !== data.countryCode ? data.phoneNumber : '',
        email: data.email || '',
        companyIds: data.companyIds ? [Number(data.companyIds.value)] : undefined
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            callbackHandleStatusError422: keys => {
              keys.forEach(session => {
                if (session.field === 'email') {
                  if (data?.email && formAction) {
                    fetchContactAndCheckContact(data.email, formAction, session)
                  }
                } else {
                  if (session.field && formAction?.control._fields[session.field]) {
                    formAction?.setError(session.field, {
                      type: 'custom',
                      message: session.message
                    })
                  }
                }
              })
            }
          })
        }

        const { contactsCreate } = result.data
        if (contactsCreate) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('contact:contactCreated')}`,
            classNameConfig: {
              viewport: 'mb-[48px]'
            }
          })
          setOpenCreateContact(false)
          refetch()
        }
        return
      })
    },
    [loadingContactCreate]
  )

  const topSpace = useClassBasedTopSpace({
    34: 'h-full',
    default: 'h-screen'
  })

  useEffect(() => {
    if (refetchMyList) {
      refetch()
      setRefetchMyList(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchMyList])

  return (
    <>
      <SwitchLayoutView
        configurations="ghost"
        returnUrl={configuration.path.agency.contacts.list}
        paths={configSwitchLayout.path}
        redirectUrls={configSwitchLayout.redirectUrls}
      >
        {({ switchView, setSwitchView, renderedActions }) => {
          return (
            <div ref={contactRef} className={cn('flex flex-col', topSpace)}>
              <div className="flex h-[56px] flex-none items-center justify-between px-6 py-4">
                <div className="flex items-center">
                  <p className="mr-2 text-lg font-medium text-gray-900 dark:text-gray-200">{t('contact:table:heading')}</p>
                  {data?.meta?.totalRowCount && data?.meta?.totalRowCount > 0 ? (
                    <Badge radius="circular" size="md">
                      {data.meta.totalRowCount}
                    </Badge>
                  ) : null}
                </div>
                <FilterContactManagement filter={filter} changeFilter={changeFilter} setOpenCreateContact={() => setOpenCreateContact(true)} />
              </div>

              {data?.meta.totalRowCount === 0 && filter?.isFilterTouched ? (
                <div style={{ minHeight: 'calc(100vh - 170px)' }} className="flex flex-1 items-center">
                  <Empty
                    type="empty-search"
                    title={t('contact:table:emptySearch:title') || ''}
                    description={t('contact:table:emptySearch:description') || ''}
                    onClick={() => changeFilter(DEFAULT_FILTER)}
                  />
                </div>
              ) : (
                <div className="flex-1 pl-6">
                  <ContactListingTable
                    classNameEmpty="-ml-6"
                    setOpenCreateCandidate={setOpenCreateContact}
                    data={data}
                    fetcher={{
                      fetchPagination,
                      forceChangeCurrentPage
                    }}
                    isFetching={isFetching}
                    filter={filter}
                    actions={{
                      configSwitchLayout,
                      setConfigSwitchLayout,
                      switchView,
                      setSwitchView
                    }}
                  />
                </div>
              )}
              <Dialog
                className="min-w-[480px]"
                open={openCreateContact}
                isPreventAutoFocusDialog
                label={`${t('company:contact:add_contact')}`}
                onOpenChange={setOpenCreateContact}
              >
                <CompanyContactForm
                  onClickCancelButton={() => setOpenCreateContact(false)}
                  onSubmit={onSubmitAddForm}
                  showSelectCompany={true}
                  loadingContactCreate={loadingContactCreate}
                />
              </Dialog>
              {switchView.view === 'contacts' ? (
                <Drawer
                  position="right"
                  size="full"
                  drawerClassName="max-w-[calc(100vw-60px)] border-0!"
                  contentRef={drawerContainerRef}
                  open
                  onEscapeKeyDown={() => {
                    setRefetchMyList(true)

                    pushStateBrowser({
                      state: {},
                      unused: '',
                      url: configuration.path.agency.contacts.list
                    })
                    setSwitchView({
                      id: undefined,
                      view: '',
                      applicantId: undefined
                    })
                  }}
                  customCloseButton={
                    <div className="absolute top-4 -left-10 z-50 flex flex-col space-y-4">{renderedActions && renderedActions()}</div>
                  }
                >
                  <TopSpaceProvider value={{ top: undefined }}>
                    <ContactAgencyDetailContainer isDrawer={true} contactId={Number(switchView?.id)} setSwitchView={setSwitchView} />
                  </TopSpaceProvider>
                </Drawer>
              ) : null}
            </div>
          )
        }}
      </SwitchLayoutView>
    </>
  )
}

export default ContactsAgencyContainer
