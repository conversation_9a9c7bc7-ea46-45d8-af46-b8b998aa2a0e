import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { useMutation } from 'urql'

import { PUBLIC_APP_NAME } from '~/core/constants/env'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useQueryAgencyGraphQL from '~/core/middleware/use-query-agency-graphQL'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryAgencyTenantJobDetailClientContact from '~/lib/features/jobs/graphql/agency-query-job-detail-client-contact'
import type { IJobDetail } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import type { IJobDetailParams } from '~/lib/features/jobs/types'
import { removeEmptyField } from '~/lib/features/placements/utilities'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import MutationInviteContact from '~/features/client-contact/graphql/mutation-invite-contact'
import MutationCancelInviteMember from '~/features/client-contact/graphql/mutation-invite-contact-cancel'
import MutationDeleteMember from '~/features/client-contact/graphql/mutation-remove-job'
import queryCompanyContactList from '~/features/client-contact/graphql/query-company-contact-list'
import type { AddContactType } from '~/features/client-contact/schema'

const useClientContact = ({ companyId, reload }: { companyId?: number; reload?: () => Promise<void> }) => {
  const { user } = useBoundStore()
  const { t } = useTranslation()
  const [_, inviteMutation] = useMutation(MutationInviteContact)
  const [__, cancelInviteMutation] = useMutation(MutationCancelInviteMember)
  const [___, deleteClient] = useMutation(MutationDeleteMember)
  const { params } = useRouterContext()
  const id = params?.id
  const { clientGraphQL } = useContextGraphQL()
  const { data: companyContactList } = useQueryAgencyGraphQL({
    query: queryCompanyContactList,
    shouldPause: !companyId,
    variables: { companyId: companyId as number, limit: 10, page: 1 },
    user: user
  })

  const fetchData = async (paramsJobDetail: IJobDetailParams) => {
    return clientGraphQL
      .query(QueryAgencyTenantJobDetailClientContact, {
        id: Number(id),
        ...paramsJobDetail
      })
      .toPromise()
      .then((result: { error: { graphQLErrors: Array<object> }; data: { jobsShow: IJobDetail['jobsShow'] } }) => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
          return
        }
        const { jobsShow } = result.data
        if (jobsShow.clientInvitedJob) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:clientContact:successSentTitle')}`,
            description: `${t('notification:clientContact:successSentContentInvitation')}`
          })
        } else {
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:clientContact:successSentTitle')}`,
            description: `${t('notification:clientContact:successSentContentNewInvitation', { domain: PUBLIC_APP_NAME })}`
          })
        }
      })
  }
  const { setToast } = useToastStore()

  return {
    companyContactList: companyContactList?.companyContactsList.collection,
    onInviteContact: useCallback(
      (data: Partial<AddContactType> & { jobId?: number; contactId?: number }) => {
        return inviteMutation(
          removeEmptyField({
            email: data.email,
            jobId: data.jobId,
            contactId: data.contactId
          })
        ).then(result => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              setToast
            })
            return
          }

          return (
            reload &&
            reload().then(() => {
              fetchData({
                contactId: data.contactId,
                clientInvitationEmail: data.email
              })
            })
          )
        })
      },
      [reload]
    ),
    onDeleteContact: useCallback(
      (id: number, contactName: string, jobId?: number) => {
        return cancelInviteMutation({ id, jobId }).then(() => {
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:clientContact:contactHasBeenRemoved', {
              contactName
            })}`
          })
          return reload && reload()
        })
      },
      [reload]
    ),
    onDeleteClient: useCallback(
      (jobId: number, clientId: number) => {
        return deleteClient({
          id: jobId,
          clientDeletedMemberId: clientId
        }).then(result => {
          // if (result.error) {
          //   catchErrorFromGraphQL({
          //     error: result.error,
          //     setToast
          //   })
          //   return
          // }
          return reload && reload()
        })
      },
      [reload]
    ),
    resenEmail: useCallback(
      (invite: { id: string; email: string }) => {
        return inviteMutation(
          removeEmptyField({
            invitationId: parseInt(invite.id.toString()),
            email: invite.email,
            resend: true
          })
        ).then(result => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              setToast
            })
            return
          }
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:clientContact:invitationForEmailHasBeenResent', {
              email: invite.email
            })}`
          })
          return reload && reload()
        })
      },
      [reload]
    )
  }
}
export default useClientContact
