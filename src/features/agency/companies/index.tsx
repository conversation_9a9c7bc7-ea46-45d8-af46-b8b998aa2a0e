import { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import configuration from '~/configuration'
import { AGENCY_TENANT, KEY_FILTER_TEAM_AND_MEMBER, TAB_MEMBERS, TAB_TEAMS } from '~/core/constants/enum'
import { Badge } from '~/core/ui/Badge'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import { Drawer } from '~/core/ui/Drawer'
import type { LucideIconName } from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { Tabs, TabsList, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'
import { Tooltip } from '~/core/ui/Tooltip'
import { ACTIONS_PERMISSIONS, canAccessFeature, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'
import { pushStateBrowser } from '~/core/utilities/is-browser'

import QueryCompanyStatusesListFilter from '~/lib/features/agency/companies/graphql/query-company-status-list'
import QueryAgencyLocations from '~/lib/features/agency/companies/graphql/query-location-agency-list'
import type { ICompaniesFilter } from '~/lib/features/agency/companies/types'
import usePermissionCompany from '~/lib/features/permissions/hooks/use-permission-company'
import QueryTenantDepartment from '~/lib/features/settings/departments/graphql/query-tenant-department'
import QueryHiringMembers from '~/lib/features/settings/members/graphql/query-member-filter'
import QueryTenantMembers from '~/lib/features/settings/members/graphql/query-tenant-members'
import type { IMember, IMemberFilterList } from '~/lib/features/settings/members/types'
import type { ITeamFilterList } from '~/lib/features/settings/teams/types'
import QueryPublicIndustries from '~/lib/graphql/query-public-industry'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useMappingFilterData } from '~/lib/hooks/useMappingFilterData'
import useBoundStore from '~/lib/store'

import CompaniesView from '~/components/Agency/Companies/CompaniesView'
import CompanyForm from '~/components/Agency/Companies/CompanyForm'
import SwitchTabs from '~/components/Agency/Companies/SwitchTabs'
import Filter from '~/components/Filter/Filter'
import LayoutGrid from '~/components/Layout/LayoutGrid'
import { TopSpaceProvider } from '~/components/Subscription/TopSpace'
import SwitchLayoutView from '~/components/SwitchLayout/SwitchLayoutView'

import CompaniesAgencyDetailContainer from './[id]'

const VIEW_TABS: Array<{
  value: string
  iconMenus: LucideIconName
}> = [
  {
    value: 'listing',
    iconMenus: 'Table2'
  },
  {
    value: 'kanban',
    iconMenus: 'StretchVertical'
  }
]
export const COMPANIES_DEFAULT_FILTER = {
  page: 1,
  operator: 'or'
}

const CompaniesAgencyContainer = () => {
  const [openCreateCompany, setOpenCreateCompany] = useState<boolean>(false)
  const [filter, changeFilter] = useState<ICompaniesFilter | undefined>(COMPANIES_DEFAULT_FILTER)
  const [count, setCount] = useState<number | undefined>(undefined)
  const { t } = useTranslation()
  const [configSwitchLayout, setConfigSwitchLayout] = useState({
    path: [`${configuration.path.candidates.list}/`],
    redirectUrls: ['']
  })
  const [currentView, setCurrentView] = useState<string>(VIEW_TABS[0]?.value || 'listing')

  const { user, setRefetchMyList } = useBoundStore()
  const { actionCompany } = usePermissionCompany()
  const { mappingTeamsData, mappingMembersData } = useMappingFilterData()
  const drawerContainerRef = useRef<HTMLDivElement>(null)
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })

  return (
    <LayoutGrid>
      <SwitchLayoutView
        configurations="ghost"
        returnUrl={configuration.path.agency.companies}
        paths={configSwitchLayout.path}
        redirectUrls={configSwitchLayout.redirectUrls}
      >
        {({ switchView, setSwitchView, renderedActions }) => {
          return (
            <div className="flex h-full flex-col">
              <div className="flex h-[56px] flex-none items-center justify-between px-6 py-4">
                <div className="flex items-center">
                  <p className="mr-2 text-lg font-medium text-gray-900 dark:text-gray-200">{t('companiesListing:heading')}</p>
                  {(count || 0) > 0 ? (
                    <Badge radius="circular" size="md">
                      {count}
                    </Badge>
                  ) : null}
                </div>

                <Filter
                  value={filter}
                  onChange={(filterVariable, name) => {
                    if (name === KEY_FILTER_TEAM_AND_MEMBER.ownerIds) {
                      return changeFilter({
                        ...filterVariable,
                        departmentIds: []
                      })
                    }
                    if (name === KEY_FILTER_TEAM_AND_MEMBER.departmentIds) {
                      return changeFilter({
                        ...filterVariable,
                        ownerIds: []
                      })
                    }
                    changeFilter(filterVariable)
                  }}
                >
                  <div className="flex">
                    <div className={'mr-2 w-[288px]'}>
                      <Filter.SearchText
                        typingDebounceSubmit={500}
                        maxLength={50}
                        size="xs"
                        placeholder={`${t('companiesListing:searchPlaceholder')}`}
                        name="search"
                      />
                    </div>

                    <div className="mr-2">
                      <Filter.Item<string> name="tabsMembersTeams">
                        {({ value, onChange }) => {
                          // eslint-disable-next-line react-hooks/rules-of-hooks
                          const [currentTab, setCurrentTab] =
                            // eslint-disable-next-line react-hooks/rules-of-hooks
                            useState<string>()

                          return (
                            <Filter.Combobox
                              defaultOpenDropdown={!!currentTab}
                              key={currentTab}
                              isMulti
                              isMultiGroup
                              tooltipOption={{
                                position: 'bottom',
                                content: currentTab === TAB_TEAMS ? t('tooltip:filter_by_teams') : t('tooltip:filter_by_members')
                              }}
                              containerMenuClassName="max-w-[320px]"
                              dropdownMenuClassName="w-[320px]!"
                              size="sm"
                              menuOptionAlign="end"
                              menuOptionSide="bottom"
                              avatarToolTipPosition="bottom"
                              toolTipPositionAvatarCount="bottom"
                              tooltipAlignAvatarCount="end"
                              placeholder={currentTab === TAB_TEAMS ? `${t('label:placeholder:teams')}` : `${t('label:placeholder:members')}`}
                              searchPlaceholder={`${t('label:placeholder:search')}`}
                              loadingMessage={`${t('label:loading')}`}
                              noOptionsMessage={`${t('label:noOptions')}`}
                              countName={currentTab === TAB_TEAMS ? `${t('label:teamsLowercase')}` : ''}
                              name={currentTab === TAB_TEAMS ? KEY_FILTER_TEAM_AND_MEMBER.departmentIds : KEY_FILTER_TEAM_AND_MEMBER.ownerIds}
                              customerDropdownHeader={
                                <>
                                  <div className="pt-2 pr-3 pb-1 pl-3">
                                    <Tabs
                                      value={currentTab || TAB_MEMBERS}
                                      onValueChange={newValue => {
                                        setCurrentTab(newValue)
                                      }}
                                    >
                                      <TabsList isFullWidth size="xs" typeTab="switch-tab">
                                        <TabsTrigger isFullWidth className="flex" size="xs" typeTab="switch-tab" value={TAB_MEMBERS}>
                                          <TabsTriggerView
                                            session={{
                                              label: `${t('label:members')}`,
                                              value: TAB_MEMBERS
                                            }}
                                            size="xs"
                                            typeTab="switch-tab"
                                          />
                                        </TabsTrigger>
                                        <TabsTrigger isFullWidth gapSize="md" size="xs" typeTab="switch-tab" value={TAB_TEAMS}>
                                          <TabsTriggerView
                                            session={{
                                              label: `${t('label:teams')}`,
                                              value: TAB_TEAMS
                                            }}
                                            size="xs"
                                            typeTab="switch-tab"
                                          />
                                        </TabsTrigger>
                                      </TabsList>
                                    </Tabs>
                                  </div>
                                </>
                              }
                              //@ts-ignore - doesn't need to fix
                              optionsFromDocumentNode={
                                currentTab === TAB_TEAMS
                                  ? {
                                      documentNode: QueryTenantDepartment,
                                      variable: searchParams => ({
                                        ...searchParams,
                                        limit: 25
                                      }),
                                      mapping: (data: ITeamFilterList | IMemberFilterList) => {
                                        const cloneData = data as ITeamFilterList
                                        return mappingTeamsData(
                                          cloneData?.tenantDepartmentsList?.collection || [],
                                          cloneData?.tenantDepartmentsList?.metadata
                                        )
                                      }
                                    }
                                  : {
                                      documentNode: isCompanyKind ? QueryHiringMembers : QueryTenantMembers,
                                      variable: searchParams => ({
                                        ...searchParams,
                                        ...(isCompanyKind
                                          ? {}
                                          : {
                                              jobOwnable: true
                                            }),
                                        limit: 25
                                      }),
                                      mapping: (data: {
                                        [key: string]: {
                                          collection: IMember[]
                                          metadata: {
                                            totalCount: number
                                          }
                                        }
                                      }) =>
                                        mappingMembersData(
                                          (isCompanyKind ? data?.membersList?.collection : data?.tenantMembers?.collection) || [],
                                          isCompanyKind ? data?.membersList?.metadata : data?.tenantMembers?.metadata
                                        )
                                    }
                              }
                              buttonClassName="max-w-[110px]"
                            />
                          )
                        }}
                      </Filter.Item>
                    </div>
                    <If condition={currentView !== 'kanban'}>
                      <div className="mr-2">
                        <Filter.Combobox
                          tooltipOption={{
                            position: 'bottom',
                            content: t('tooltip:filter_by_status')
                          }}
                          menuOptionAlign="end"
                          menuOptionSide="bottom"
                          isSearchable={false}
                          isMulti
                          buttonClassName="text-gray-900 max-w-[100px]"
                          buttonFontWeightClassName="font-normal"
                          dropdownMenuClassName="w-[156px]!"
                          size="sm"
                          countName={`${t('label:placeholder:status')}`}
                          optionsFromDocumentNode={{
                            documentNode: QueryCompanyStatusesListFilter,
                            variable: searchParams => ({
                              limit: undefined,
                              page: undefined,
                              search: undefined
                            }), //useMemo(() => ({}), []),
                            mapping: data => {
                              return {
                                metadata: data?.companyStatusesList.metadata,
                                collection: data?.companyStatusesList.collection.map((status: { id: number; name: string }) => ({
                                  value: String(status.id),
                                  supportingObj: {
                                    name: status.name
                                  }
                                }))
                              }
                            }
                          }}
                          placeholder={`${t('label:placeholder:status')}`}
                          searchPlaceholder={`${t('label:placeholder:search')}`}
                          loadingMessage={`${t('label:loading')}`}
                          noOptionsMessage={`${t('label:noOptions')}`}
                          name="companyStatusIds"
                        />
                      </div>
                    </If>
                    <div className="mr-2">
                      <Filter.Combobox
                        tooltipOption={{
                          position: 'bottom',
                          content: t('tooltip:filter_by_location')
                        }}
                        menuOptionAlign="end"
                        menuOptionSide="bottom"
                        isSearchable
                        isMulti
                        buttonClassName="min-w-[81px] text-gray-900 max-w-[110px]"
                        buttonFontWeightClassName="font-normal"
                        dropdownMenuClassName="w-[248px]!"
                        containerMenuClassName="w-[248px]!"
                        size="sm"
                        countName={`${t('label:industryCount')}`}
                        optionsFromDocumentNode={{
                          documentNode: QueryPublicIndustries,
                          variable: searchParams => ({
                            ...searchParams
                          }), //useMemo(() => ({}), []),
                          mapping: data => {
                            return {
                              metadata: data?.publicIndustriesList.metadata,
                              collection: data?.publicIndustriesList.collection.map(industry => ({
                                value: industry.id,
                                supportingObj: {
                                  name: industry.name,
                                  status: industry.status
                                }
                              }))
                            }
                          }
                        }}
                        placeholder={
                          `${t('label:industry')}`
                          // t('job:filter:placeholderSelectLocation') || ''
                        }
                        searchPlaceholder={`${t('label:placeholder:search')}`}
                        loadingMessage={`${t('label:loading')}`}
                        noOptionsMessage={`${t('label:noOptions')}`}
                        name="industryIds"
                      />
                    </div>
                    <div className="mr-2">
                      <Filter.Combobox
                        tooltipOption={{
                          position: 'bottom',
                          content: t('tooltip:filter_by_location')
                        }}
                        menuOptionAlign="end"
                        menuOptionSide="bottom"
                        isSearchable
                        buttonClassName="min-w-[81px] text-gray-900 max-w-[110px]"
                        buttonFontWeightClassName="font-normal"
                        dropdownMenuClassName="w-[248px]!"
                        containerMenuClassName="w-[248px]!"
                        size="sm"
                        countName={`${t('label:locations')}`}
                        optionsFromDocumentNode={{
                          documentNode: QueryAgencyLocations,
                          variable: searchParams => ({
                            ...searchParams,
                            tenantSlug: user?.currentTenant?.slug
                          }), //useMemo(() => ({}), []),
                          mapping: data => {
                            return {
                              metadata: data?.companyLocsByCountryStateList.metadata,
                              collection: data?.companyLocsByCountryStateList.collection.map(
                                (location: { id: number; state: string; country: string; countryStateId?: number }) => ({
                                  id: location?.countryStateId,
                                  value: `${location.state}*${location.country}`,
                                  supportingObj: {
                                    name: `${location.state}, ${location.country}`
                                  }
                                })
                              )
                            }
                          }
                        }}
                        placeholder={t('job:filter:placeholderSelectLocation') || ''}
                        searchPlaceholder={`${t('label:placeholder:search')}`}
                        loadingMessage={`${t('label:loading')}`}
                        noOptionsMessage={`${t('label:noOptions')}`}
                        name="location"
                      />
                    </div>
                    <div className="mr-2">
                      <Filter.Combobox
                        tooltipOption={{
                          position: 'bottom',
                          content: t('tooltip:filter_by_job')
                        }}
                        options={[
                          {
                            value: 'had_open_jobs',
                            supportingObj: {
                              name: `${t('job:had_open_jobs')}`
                            }
                          },
                          {
                            value: 'no_open_jobs',
                            supportingObj: {
                              name: `${t('job:no_open_jobs')}`
                            }
                          }
                        ]}
                        size="sm"
                        configSelectOption={{}}
                        placeholder={`${t('label:placeholder:job')}`}
                        searchPlaceholder={`${t('label:placeholder:search')}`}
                        loadingMessage={`${t('label:loading')}`}
                        noOptionsMessage={`${t('label:noOptions')}`}
                        isSearchable={false}
                        name="jobStatistic"
                        buttonClassName="max-w-[200px]"
                        dropdownMenuClassName="min-w-[164px]!"
                        containerMenuClassName="max-w-[388px]"
                        menuOptionAlign="end"
                        menuOptionSide="bottom"
                      />
                    </div>
                    <div className="mr-2">
                      <Tooltip position="bottom" content={t('tooltip:filter_by_created_at')}>
                        <Filter.RangeDatePicker
                          size="sm"
                          name="dateRange"
                          placeholder={`${t('label:placeholder:formTo')}`}
                          config={{ showOutsideDays: false }}
                        />
                      </Tooltip>
                    </div>
                    <div className="mr-2">
                      <SwitchTabs
                        tabs={VIEW_TABS}
                        currentTab={currentView}
                        onTabChange={tab => {
                          setCurrentView(tab)
                        }}
                      />
                    </div>
                    <If condition={actionCompany.create}>
                      <div>
                        <Button
                          onClick={() => setOpenCreateCompany(true)}
                          htmlType="button"
                          size="xs"
                          iconMenus="Plus"
                          label={t('companiesListing:buttonAddNew') || ''}
                        />
                      </div>
                    </If>
                  </div>
                </Filter>
              </div>

              <div className="flex-1 pl-6">
                <CompaniesView
                  view={currentView}
                  setOpenCreateCompany={setOpenCreateCompany}
                  setCount={setCount}
                  count={count}
                  filter={filter}
                  changeFilter={changeFilter}
                  actions={{
                    configSwitchLayout,
                    setConfigSwitchLayout,
                    switchView,
                    setSwitchView
                  }}
                />
              </div>

              <Dialog
                className="min-w-[480px]"
                open={openCreateCompany}
                isPreventAutoFocusDialog
                label={`${t('companiesListing:companyForm:titleAddNew')}`}
                onOpenChange={setOpenCreateCompany}
              >
                <CompanyForm reload={() => setRefetchMyList(true)} onClickCancelButton={() => setOpenCreateCompany(false)} />
              </Dialog>
              {switchView.view === 'companies' ? (
                <Drawer
                  position="right"
                  size="full"
                  drawerClassName="max-w-[calc(100vw-60px)] border-0!"
                  contentRef={drawerContainerRef}
                  open
                  onEscapeKeyDown={() => {
                    setRefetchMyList(true)

                    pushStateBrowser({
                      state: {},
                      unused: '',
                      url: configuration.path.agency.companies
                    })
                    setSwitchView({
                      id: undefined,
                      view: '',
                      applicantId: undefined
                    })
                  }}
                  customCloseButton={
                    <div className="absolute top-4 -left-10 z-50 flex flex-col space-y-4">{renderedActions && renderedActions()}</div>
                  }
                >
                  <TopSpaceProvider value={{ top: undefined }}>
                    <CompaniesAgencyDetailContainer isDrawer={true} companyId={Number(switchView?.id)} setSwitchView={setSwitchView} />
                  </TopSpaceProvider>
                </Drawer>
              ) : null}
            </div>
          )
        }}
      </SwitchLayoutView>
    </LayoutGrid>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.company_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.company_management.objects.company.keyModuleObject],
    action: ACTIONS_PERMISSIONS.show
  },
  CompaniesAgencyContainer
)
