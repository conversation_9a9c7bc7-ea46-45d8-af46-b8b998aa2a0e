import { createContext } from 'react'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { AGENCY_TENANT } from '~/core/constants/enum'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { ACTIONS_PERMISSIONS, canAccessFeature, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import CompanyJobCreateView from '~/components/Jobs/CompanyJobCreateView'
import JobCreateView from '~/components/Jobs/JobCreateView'
import LayoutGrid from '~/components/Layout/LayoutGrid'

export const JobCreatePermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const JobCreateManagementContainer = () => {
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { actionJob } = usePermissionJob()

  return (
    <LayoutGrid>
      <JobCreatePermissionContext.Provider value={actionJob}>
        {isCompanyKind === true ? <CompanyJobCreateView /> : <JobCreateView />}
      </JobCreatePermissionContext.Provider>
    </LayoutGrid>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.job_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.job_management.objects.job.keyModuleObject],
    action: ACTIONS_PERMISSIONS.create
  },
  withQueryClientProvider(JobCreateManagementContainer)
)
