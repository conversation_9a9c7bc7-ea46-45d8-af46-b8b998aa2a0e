'use client'

import type { FC } from 'react'
import { memo, useCallback, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import type { IRouterWithID } from '~/core/@types/global'
import { But<PERSON> } from '~/core/ui/Button'
import type { ISelectOption } from '~/core/ui/Select'
import type { IPagePagination } from '~/core/ui/TablePagination'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import MutationProfilesAssignJob from '~/lib/features/candidates/graphql/mutation-assign-profiles-to-job'
import MutationProfilesAssignTalentPools from '~/lib/features/candidates/graphql/mutation-profiles-assign-talent-pools'
import { mappingAdvancedFilterCandidates } from '~/lib/features/candidates/mapping/candidate-filter-mapping'
import type { AddCandidateFormType, ICandidateProfile } from '~/lib/features/candidates/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useLoadingBlockStore from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'

import BottomActionBar from '~/components/BottomActionBar'
import TalentPoolSelector from '~/components/Candidates/BulkActions/Candidate/TalentPoolSelector'

const BulkActions: FC<{
  candidates?: IPagePagination
  jobId: IRouterWithID
  callback?: () => void
}> = ({ candidates, jobId, callback }) => {
  const { bulkValues, bulkSelectedAll, resetBulkValues, setBulkSelectedAll, setBulkValues, filterValues, user, setRefetchMyList } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()
  const { t } = useTranslation()

  const { trigger: assignTalentPools, isLoading: isLoadingAssignTalentPools } = useSubmitCommon(MutationProfilesAssignTalentPools)

  const { trigger: assignJob, isLoading: isLoadingAssignJob } = useSubmitCommon(MutationProfilesAssignJob)

  const handleClose = () => {
    resetBulkValues()
  }

  const handleCheckAll = (e: { target: { checked: boolean } }) => {
    const { checked } = e.target
    setBulkSelectedAll(checked)
    if (checked) setIds()
    else resetBulkValues()
  }

  const setIds = () => {
    let ids: string[] = []
    candidates?.data?.map((candidate: ICandidateProfile) => {
      ids = [...ids, String(candidate?.id)]
    })
    setBulkValues(ids)
  }

  const mappingParamsFilters = () => {
    const { owners, tags, locationId, jobLevel, search, salary, currency, page, talentPool, operator } = filterValues
    return {
      salaryFrom: salary && salary !== '' ? Number(salary) : undefined,
      currency: currency ? currency : user?.currentTenant?.currency,
      operator,
      search: search || '',
      jobId: Number(jobId),
      ownerIds: owners?.map((item: ISelectOption) => parseInt(item.value)),
      tagIds: tags ? tags.map((t: ISelectOption) => Number(t.value)) : undefined,
      country: locationId?.supportingObj?.description || undefined,
      state: locationId?.supportingObj?.descriptionHelpName || undefined,
      profileLevel: jobLevel?.value || undefined,
      countryStateId: Number(locationId?.value),
      filterTalentPoolIds: (talentPool?.length || 0) > 0 ? talentPool?.map((item: ISelectOption) => parseInt(item.value)) : undefined
    }
  }

  const onAssignTalentPools = useCallback(
    async (data: AddCandidateFormType) => {
      const { profileTalentPoolIds } = data
      setShowLockApp('')
      // const params = mappingAdvancedFilterCandidates(filterValues, user)
      if (isLoadingAssignTalentPools) {
        return
      }
      assignTalentPools({
        profileIds: bulkValues?.map(id => Number(id)),
        talentPoolIds: profileTalentPoolIds?.map(poolItem => Number(poolItem.value)),
        selectAll: bulkSelectedAll,
        profileFrom: 'recommendation', // bulkSelectedAll ? 'recommendation' : null,
        ...mappingParamsFilters()
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }
        const { profilesAssignTalentPools } = result.data
        if (profilesAssignTalentPools.success) {
          setCloseLockApp()
          setRefetchMyList(true)
          resetBulkValues()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:assign_talent_pool_success')
          })
        }
        return
      })
    },
    [bulkValues, filterValues, user, jobId]
  )

  const onAssignJob = useCallback(
    async (jobId: number) => {
      setShowLockApp('')
      // const params = mappingAdvancedFilterCandidates(filterValues, user)
      if (isLoadingAssignJob) {
        return
      }
      assignJob({
        profileIds: bulkValues?.map(id => Number(id)),
        assignJobId: Number(jobId),
        selectAll: bulkSelectedAll,
        profileFrom: 'recommendation',
        ...mappingParamsFilters()
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }
        const { profilesAssignJobs } = result.data
        if (profilesAssignJobs.success) {
          setCloseLockApp()
          setRefetchMyList(true)
          resetBulkValues()
          callback && callback()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:assign_job_success')
          })
        }
        return
      })
    },
    [bulkValues, filterValues, user]
  )

  useEffect(() => {
    if (bulkSelectedAll) setIds()
  }, [candidates?.data])

  useEffect(() => {
    return () => {
      // clear data when unmout
      resetBulkValues()
    }
  }, [])

  if (bulkSelectedAll || bulkValues?.length)
    return (
      <BottomActionBar
        totalCount={candidates?.meta?.totalRowCount}
        data={bulkValues || []}
        isSelectedAll={bulkSelectedAll || false}
        onClose={handleClose}
        setSelectedAll={handleCheckAll}
        className="mb-6"
      >
        {jobId && (
          <Button size="sm" iconMenus="Send" type="secondary" onClick={() => onAssignJob(Number(jobId))} label={`${t('button:assignJob')}`} />
        )}
        <TalentPoolSelector onSubmit={onAssignTalentPools} />
      </BottomActionBar>
    )

  return null
}

export default memo(BulkActions)
