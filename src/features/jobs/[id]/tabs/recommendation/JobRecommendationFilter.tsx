import type { Dispatch, SetStateAction } from 'react'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import { But<PERSON> } from '~/core/ui/Button'
import If from '~/core/ui/If'
import type { ISelectOption } from '~/core/ui/Select'
import { Skeleton } from '~/core/ui/Skeleton'
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'

import QueryTalentPoolList from '~/lib/features/candidates/graphql/query-talent-pool-list'
import type { IRecommendationSetting } from '~/lib/features/recommendation-setting/type/recommendation-setting'
import { RECOMMENDATION_TYPE_SETTINGS_ENUMS } from '~/lib/features/recommendation-setting/utilities/enum'
import QueryHiringMembers from '~/lib/features/settings/members/graphql/query-member-filter'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useTenantSettingJobFieldsHook from '~/lib/features/settings/profile-fields/hooks/use-tenant-setting-job-field-hook'
import QueryTagsList from '~/lib/features/settings/tags/graphql/query-tags-list'
import { TAG_KIND } from '~/lib/features/settings/tags/utilities/enum'
import QueryCountryStates from '~/lib/graphql/query-country-states'
import useBoundStore from '~/lib/store'

import Filter from '~/components/Filter/Filter'
import type { RecommendationSettingModalProps } from '~/components/RecommendationSetting/RecommendationSettingModal'
import RecommendationSettingModal from '~/components/RecommendationSetting/RecommendationSettingModal'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import type { IRecommendationProfileFilter } from './JobRecommendationTab'

const JobRecommendationFilter = ({
  filter,
  changeFilter
}: {
  filter?: IRecommendationProfileFilter
  changeFilter: Dispatch<SetStateAction<IRecommendationProfileFilter | undefined>>
}) => {
  const { t, i18n } = useTranslation()
  const { isShowSystemField, tenantSettingsJobFieldsData } = useTenantSettingJobFieldsHook()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const [recommendationSetting, setRecommendationSetting] = useState<IRecommendationSetting | null>(null)

  const { user } = useBoundStore()

  const jobCurrency = useEnumsData({
    enumType: 'JobCurrency',
    locale: i18n.language
  })

  const customDataJobCurrency = useMemo(() => {
    return jobCurrency.map((jc: ISelectOption) => ({
      ...jc,
      supportingObj: { name: jc.value }
    }))
  }, [jobCurrency])

  const [openSetting, setOpenSetting] = useState<RecommendationSettingModalProps>({
    open: false,
    defaultValue: recommendationSetting || undefined,
    action: 'apply'
  })

  const handleOpenRecommendationSetting = () => {
    setOpenSetting({
      open: true,
      defaultValue: recommendationSetting || undefined,
      action: 'apply'
    })
  }

  const handleCallbackRecommendationSetting = (dataCallback: IRecommendationSetting) => {
    if (dataCallback) {
      setRecommendationSetting(dataCallback)
      changeFilter({
        ...filter,
        ...{ recommendWeight: dataCallback }
      })
    }
  }
  const jobLevel = useEnumsData({
    enumType: 'JobJobLevel',
    locale: i18n.language
  })
  return !tenantSettingsJobFieldsData ? (
    <Skeleton className="h-6 w-1/2 rounded-xs" />
  ) : (
    <div className="mb-3 pr-6">
      <Filter
        value={filter}
        onChange={(filterVariable, name) => {
          if (name === 'operator') {
            if (filterVariable?.tags?.length) {
              changeFilter(filterVariable)
            }
          } else {
            changeFilter(filterVariable)
          }
        }}
      >
        <div className="flex flex-wrap justify-between gap-x-2">
          <div className="flex flex-wrap">
            <div className="mr-2 w-[240px]">
              <Filter.SearchText
                typingDebounceSubmit={500}
                maxLength={50}
                size="xs"
                placeholder={`${t('label:placeholder:search_by_name_phone_email_link')}`}
                name="search"
              />
            </div>
            <div className="mr-2">
              <Filter.Combobox
                menuOptionSide="bottom"
                isSearchable={true}
                closeOnSelect
                buttonClassName="min-w-[81px] text-gray-900 max-w-[116px]"
                buttonFontWeightClassName="font-normal"
                containerMenuClassName="max-w-[248px]"
                dropdownMenuClassName="w-[248px]!"
                size="sm"
                countName={`${t('label:placeholder:locations')}`}
                configSelectOption={{
                  supportingText: ['name']
                }}
                tooltipOption={{
                  position: 'bottom',
                  content: `${t('tooltip:filter_by_location')}`
                }}
                optionsFromDocumentNode={{
                  documentNode: QueryCountryStates,
                  variable: searchParams => ({
                    ...searchParams,
                    stateOnly: true,
                    defaultShow: true
                  }),
                  mapping: data => {
                    return {
                      metadata: data?.countryStates.metadata,
                      collection: data?.countryStates.collection.map(location => ({
                        value: String(location.id),
                        supportingObj: {
                          name: location.fullName,
                          description: location.country.name,
                          descriptionHelpName: location.name
                        }
                      }))
                    }
                  }
                }}
                placeholder={t('job:filter:placeholderSelectLocation') || ''}
                searchPlaceholder={`${t('label:placeholder:search')}`}
                loadingMessage={`${t('label:loading')}`}
                noOptionsMessage={`${t('label:noOptions')}`}
                name="locationId"
              />
            </div>
            <div className="mr-2">
              <Filter.Combobox
                tooltipOption={{
                  position: 'bottom',
                  content: `${t('tooltip:filter_by_level')}`
                }}
                menuOptionSide="bottom"
                isSearchable={false}
                buttonClassName="min-w-[62px] text-gray-900 max-w-[130px]"
                buttonFontWeightClassName="font-normal"
                dropdownMenuClassName="w-[164px]!"
                size="sm"
                options={jobLevel}
                placeholder={t('candidates:tabs:candidateRecommendation:level') || ''}
                searchPlaceholder={`${t('label:placeholder:search')}`}
                loadingMessage={`${t('label:loading')}`}
                noOptionsMessage={`${t('label:noOptions')}`}
                name="jobLevel"
              />
            </div>
            <div className="mr-2">
              <Filter.SearchOutFocus
                maxLength={50}
                size="xs"
                className="w-[180px]"
                inputType="number"
                placeholder={`${t('label:placeholder:min_salary')}`}
                name="salary"
                config={{
                  tooltipOption: {
                    position: 'bottom',
                    content: `${t('tooltip:filter_by_currency')} `
                  },
                  size: 'sm',
                  isSearchable: false,
                  isClearable: false,
                  options: customDataJobCurrency,
                  placeholder: `${t('label:placeholder:currency')}`,
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  name: 'currency',
                  dropdownMenuClassName: 'w-[120px]!',
                  menuOptionAlign: 'end',
                  menuOptionSide: 'bottom',
                  buttonClassName: 'rounded-l-none w-[64px]',
                  value: customDataJobCurrency.filter((item: ISelectOption) =>
                    filter?.currency ? filter?.currency === item.value : user?.currentTenant?.currency === item.value
                  )[0],
                  onChange: (newValue: ISelectOption) => {
                    changeFilter({
                      ...filter,
                      currency: String(newValue?.value)
                    })
                  }
                }}
              />
            </div>

            <div className="mr-2">
              <Filter.Item<string> name="operator">
                {({ value, onChange }) => {
                  return (
                    <Filter.Combobox
                      tooltipOption={{
                        position: 'bottom',
                        content: (
                          <>{`${t('tooltip:filter_by_tag_name', {
                            name: {
                              or: `${t('label:tag_any_match')}`,
                              and: `${t('label:tag_match_all')}`
                            }[value]
                          })}`}</>
                        )
                      }}
                      size="sm"
                      isSearchable={true}
                      isMulti
                      countName={`${t('label:placeholder:tags')}`}
                      menuOptionAlign="end"
                      menuOptionSide="bottom"
                      customerDropdownHeader={
                        <div className="pt-2 pr-3 pb-1 pl-3">
                          <Tabs
                            value={value}
                            onValueChange={value => {
                              onChange(value)
                            }}
                          >
                            <TabsList isFullWidth size="xs" typeTab="switch-tab">
                              <TabsTrigger isFullWidth className="flex" size="xs" typeTab="switch-tab" value="or">
                                <TabsTriggerView
                                  session={{
                                    label: `${t('label:tab:any_match')}`,
                                    value: 'or'
                                  }}
                                  size="xs"
                                  typeTab="switch-tab"
                                />
                              </TabsTrigger>
                              <TabsTrigger isFullWidth gapSize="md" size="xs" typeTab="switch-tab" value="and">
                                <TabsTriggerView
                                  session={{
                                    label: `${t('label:tab:match_all')}`,
                                    value: 'and'
                                  }}
                                  size="xs"
                                  typeTab="switch-tab"
                                />
                              </TabsTrigger>
                            </TabsList>
                          </Tabs>
                        </div>
                      }
                      optionsFromDocumentNode={{
                        documentNode: QueryTagsList,
                        variable: searchParams => ({
                          ...searchParams,
                          sortByAlphabet: 'true',
                          kind: TAG_KIND.profile
                        }),
                        mapping: data => {
                          return {
                            metadata: data.tagsList.metadata,
                            collection: data?.tagsList.collection.map(tag => ({
                              value: String(tag.id),
                              supportingObj: {
                                name: tag.name
                              }
                            }))
                          }
                        }
                      }}
                      placeholder={`${t('label:placeholder:tag')}`}
                      buttonFontWeightClassName="font-normal"
                      searchPlaceholder={`${t('label:placeholder:search')}`}
                      loadingMessage={`${t('label:loading')}`}
                      noOptionsMessage={`${t('label:noOptions')}`}
                      name="tags"
                      containerMenuClassName="w-[230px]!"
                      dropdownMenuClassName="w-[230px]!"
                      buttonClassName="max-w-[172px]"
                    />
                  )
                }}
              </Filter.Item>
            </div>
            <If condition={isShowSystemField('job_talent_pool_ids')}>
              <div className="mr-2">
                <Filter.Combobox
                  menuOptionSide="bottom"
                  isMulti
                  buttonClassName="min-w-[95px] text-gray-900 max-w-[118px]"
                  buttonFontWeightClassName="font-normal"
                  containerMenuClassName="max-w-[248px]"
                  dropdownMenuClassName="w-[248px]!"
                  size="sm"
                  configSelectOption={{
                    supportingText: ['name']
                  }}
                  tooltipOption={{
                    position: 'bottom',
                    content: `${t('tooltip:filter_by_talent_pool')}`
                  }}
                  optionsFromDocumentNode={{
                    documentNode: QueryTalentPoolList,
                    variable: searchParams => ({
                      ...searchParams,
                      status: 'active'
                    }),
                    mapping: data => {
                      return {
                        metadata: data?.talentPoolsList.metadata,
                        collection: data?.talentPoolsList.collection.map(talentPool => ({
                          value: String(talentPool.id),
                          supportingObj: {
                            name: talentPool.name
                          }
                        }))
                      }
                    }
                  }}
                  placeholder={t('job:filter:placeholder_select_talent_pool') || ''}
                  searchPlaceholder={`${t('label:placeholder:search')}`}
                  loadingMessage={`${t('label:loading')}`}
                  noOptionsMessage={`${t('label:noOptions')}`}
                  name="talentPool"
                />
              </div>
            </If>
            <div className="mr-2">
              <Filter.Combobox
                isMulti
                tooltipOption={{
                  position: 'bottom',
                  content: `${t('tooltip:filter_by_owner')} `
                }}
                containerMenuClassName="w-[320px]!"
                optionsFromDocumentNode={{
                  documentNode: QueryHiringMembers,
                  variable: searchParams => searchParams,
                  mapping: data => {
                    return {
                      metadata: data?.membersList.metadata,
                      collection: data?.membersList.collection.map(member => ({
                        value: String(member.id),
                        avatar: member?.avatarVariants?.thumb?.url,
                        avatarVariants: member.avatarVariants,
                        supportingObj: {
                          name: member.fullName,
                          defaultColour: member.defaultColour
                        }
                      }))
                    }
                  }
                }}
                size="sm"
                menuOptionAlign="end"
                menuOptionSide="bottom"
                avatarToolTipPosition="bottom"
                toolTipPositionAvatarCount="bottom"
                tooltipAlignAvatarCount="end"
                placeholder={`${t('label:placeholder:owner')}`}
                searchPlaceholder={`${t('label:placeholder:search')}`}
                loadingMessage={`${t('label:loading')}`}
                noOptionsMessage={`${t('label:noOptions')}`}
                name="owners"
              />
            </div>
          </div>
          <div>
            <If condition={isFeatureEnabled(PLAN_FEATURE_KEYS.recommendation_weight) && isUnLockFeature(PLAN_FEATURE_KEYS.recommendation_weight)}>
              <Button
                size="xs"
                type="secondary"
                iconMenus="SlidersHorizontal"
                label={`${t('button:setting')}`}
                onClick={handleOpenRecommendationSetting}
              />
            </If>
          </div>

          <RecommendationSettingModal
            open={openSetting.open}
            setOpen={setOpenSetting}
            type={RECOMMENDATION_TYPE_SETTINGS_ENUMS?.JOB}
            data={openSetting}
            callback={handleCallbackRecommendationSetting}
          />
        </div>
      </Filter>
    </div>
  )
}

export default JobRecommendationFilter
