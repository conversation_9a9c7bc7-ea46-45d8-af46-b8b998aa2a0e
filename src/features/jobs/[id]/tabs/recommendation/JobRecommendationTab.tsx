import type { FC } from 'react'
import React, { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import type { IRouterWithID } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'
import type { IPagePagination } from '~/core/ui/TablePagination'
import { cn } from '~/core/ui/utils'
import { adminAndMemberCanAction } from '~/core/utilities/permission'

import QueryRecommendedProfilesList from '~/lib/features/jobs/graphql/query-recommended-profile-list'
import type { IRecommendationSetting } from '~/lib/features/recommendation-setting/type/recommendation-setting'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import useBoundStore from '~/lib/store'

import CandidateListingTable from '~/components/Candidates/CandidateListingTable'
import MatchedRankDetail from '~/components/Candidates/Profile/components/Recommendation/MatchedRankDetail'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'

import BulkActions from './BulkActions'
import JobRecommendationFilter from './JobRecommendationFilter'

const DEFAULT_FILTER_RECOMMENDATION = {
  page: 1,
  operator: 'or'
}

export interface IRecommendationProfileFilter {
  jobId?: { value?: number }
  sorting?: {
    created_at?: 'desc' | 'asc'
  }
  salary?: string | number
  currency?: string
  jobLevel?: ISelectOption
  locationId?: ISelectOption
  page?: number
  search?: string
  tags?: ISelectOption[]
  owners?: ISelectOption[]
  operator?: string
  talentPool?: ISelectOption[]
  recommendWeight?: IRecommendationSetting
}

const JobRecommendationTab: FC<{
  jobId: IRouterWithID
  suspend?: boolean
}> = ({ jobId, suspend = false }) => {
  const { t } = useTranslation()
  const { user, currentRole } = useBoundStore()
  const tableRef = useRef<any>(null)
  const [filter, changeFilter] = useState<IRecommendationProfileFilter | undefined>(DEFAULT_FILTER_RECOMMENDATION)

  const { data, refetch, fetchPagination, forceChangeCurrentPage, isFetching } = usePaginationGraphPage({
    queryDocumentNode: QueryRecommendedProfilesList,
    queryKey: 'my-jobRecommendation-management',
    filter: {
      salaryFrom: filter?.salary && filter?.salary !== '' ? Number(filter.salary) : undefined,
      currency: filter?.currency ? filter.currency : user?.currentTenant?.currency,
      operator: filter?.operator,
      search: filter?.search || '',
      jobId: Number(jobId),
      ownerIds: filter?.owners?.map(item => parseInt(item.value)),
      tagIds: filter?.tags?.length ? filter.tags.map(t => Number(t.value)) : undefined,
      country: filter?.locationId?.supportingObj?.description || undefined,
      state: filter?.locationId?.supportingObj?.descriptionHelpName || undefined,
      profileLevel: filter?.jobLevel?.value || undefined,
      countryStateId: Number(filter?.locationId?.value),
      filterTalentPoolIds: filter?.talentPool?.length ? filter.talentPool?.map(item => parseInt(item.value)) : undefined,
      recommendWeight: filter?.recommendWeight
    },
    enabled: !suspend
  })

  const calcHeightScroll = useClassBasedTopSpace({
    34: 'h-[calc(100vh-169px)]',
    default: 'h-[calc(100vh-135px)]'
  })

  const calcHeightPlanSubscription = useClassBasedTopSpace({
    34: 'h-[calc(100vh-194px)]',
    default: 'h-[calc(100vh-160px)]'
  })

  return !suspend ? (
    <React.Fragment key="job-recommendation-tab">
      <JobRecommendationFilter filter={filter} changeFilter={changeFilter} />
      <div className={cn('mt-3 overflow-y-auto')}>
        <MatchedRankDetail>
          {setModalMatchedRank => {
            if (!data) return null

            return (
              <CandidateListingTable
                tableType="recommendation"
                calcHeightScroll={calcHeightScroll}
                tableRef={(tableEditor: any) => {
                  return (tableRef.current = tableEditor)
                }}
                enableRowSelection={adminAndMemberCanAction(currentRole?.code)}
                hiddenColumns={['jobs', 'stage', 'publicId']}
                configUserDisplay={undefined}
                emptyConfig={{
                  title: `${t('label:noCandidatesMatchThisJob')}`,
                  description: ''
                }}
                setOpenCreateCandidate={() => {}}
                data={data}
                fetcher={{
                  fetchPagination,
                  forceChangeCurrentPage
                }}
                isFetching={isFetching}
                filter={filter}
                clearFilter={() => {
                  changeFilter({ page: 1, currency: 'USD', operator: 'or' })
                }}
                actions={{
                  setModalMatchedRank
                }}
                columnSize={{
                  matchRank: 124,
                  createdAt: 180,
                  lastActivity: 180
                }}
              />
            )
          }}
        </MatchedRankDetail>
        <BulkActions candidates={data as IPagePagination} jobId={jobId} callback={refetch} />
      </div>
    </React.Fragment>
  ) : (
    <div className={`${calcHeightPlanSubscription}`} />
  )
}

export default withQueryClientProvider(
  withSubscriptionPlanLockFearture(JobRecommendationTab, PLAN_FEATURE_KEYS.recommendation as FeatureName, {
    classNameHeightOfView: 'min-h-0 h-full'
  })
)
