import type { Editor as CoreEditor } from '@tiptap/core'
import { formatISO } from 'date-fns'
import type { TFunction } from 'i18next'
import { useCallback, useMemo, useRef, useState } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import type { IFormAction, ISelectOption } from '~/core/@types/global'
import type { IResponseContextResult } from '~/core/middleware/use-context-graphQL'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { openAlert } from '~/core/ui/AlertDialog'
import { Avatar, defaultColorBgAvatar } from '~/core/ui/Avatar'
import { Button } from '~/core/ui/Button'
import { Checkbox } from '~/core/ui/Checkbox'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import Empty from '~/core/ui/Empty'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '~/core/ui/Popover'
import type { IAttachmentsFiles } from '~/core/ui/RichEditor'
import { RichEditorWithActions } from '~/core/ui/RichEditorWithActions'
import { SingleDatePicker } from '~/core/ui/SingleDatePicker'
import { TextButton } from '~/core/ui/TextButton'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { defaultFormatDate } from '~/core/utilities/format-date'

import { useMentionMemberManagement } from '~/lib/features/candidates/hooks/use-mention-members-management'
import useQueryHiringMembersList from '~/lib/features/candidates/hooks/use-query-hiring-members-list'
import { commentFormSchema } from '~/lib/features/candidates/schema/validation-profile-note'
import type { NoteFormInActiveType, NoteFormType } from '~/lib/features/candidates/types'
import MutationDeleteJobNoteAttachment from '~/lib/features/jobs/graphql/delete-job-note-attachment'
import { MutationCreateNote, MutationDeleteNote, MutationUpdateNote, QueryJobNotes } from '~/lib/features/jobs/graphql/query-job-notes'
import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import { JOB_STATUS_ENUM } from '~/lib/features/jobs/utilities/enum'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import useAsyncAction from '~/lib/hooks/use-async-action'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useUploadS3AWS, { ACCEPT_FILES_COMMON, LIST_ACCEPT_FILES_COMMON, MAXIMUM_5_MB } from '~/lib/hooks/use-upload-s3-aws'
import useBoundStore from '~/lib/store'
import useLoadingBlockStore from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'

import ShowMore from '~/components/List/ShowMore'
import NoteItem from '~/components/Notes/NoteItem'

type ICommentForm = NoteFormType
export type NoteTabPropsType = { jobId: number; jobStatus: JOB_STATUS_ENUM }

const deleteNoteModal = (t: TFunction, option: { onDelete: () => void }) => {
  openAlert({
    isPreventAutoFocusDialog: false,
    className: 'w-[480px]',
    title: `${t('common:modal:deleteNoteTitle')}`,
    description: `${t('common:modal:deleteNoteDescription')}`,
    actions: [
      {
        label: `${t('settings:teamMembers:removeMemberAlert:actions:cancel')}`,
        type: 'secondary',
        size: 'sm'
      },
      {
        isCallAPI: true,
        label: `${t('settings:teamMembers:removeMemberAlert:actions:remove')}`,
        type: 'destructive',
        size: 'sm',
        onClick: async () => {
          if (option.onDelete) {
            await option.onDelete()
          }
        }
      }
    ]
  })
}

export enum NodeAction {
  ADD = 'ADD',
  DELETE = 'DELETE',
  EDIT = 'EDIT'
}

export const checkNoteCTA = (jobStatus: JOB_STATUS_ENUM, action: NodeAction) => {
  return (
    ({
      [JOB_STATUS_ENUM.publish]: [NodeAction.ADD, NodeAction.DELETE, NodeAction.EDIT],
      [JOB_STATUS_ENUM.archived]: [],
      [JOB_STATUS_ENUM.draft]: [NodeAction.ADD, NodeAction.DELETE, NodeAction.EDIT],
      [JOB_STATUS_ENUM.internal]: [NodeAction.ADD, NodeAction.DELETE, NodeAction.EDIT]
    }[jobStatus] as NodeAction[]) || []
  ).includes(action)
}

const NoteTabContent = ({ jobId, jobStatus }: NoteTabPropsType) => {
  const { clientGraphQL } = useContextGraphQL()
  const [formInActive, setFormInActive] = useState<NoteFormInActiveType>({})
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()
  const { t, i18n } = useTranslation()
  const { actionJobNote } = usePermissionJob()
  const [isExpandModal, setIsExpandModal] = useState<boolean>(false)
  const { files, setFiles } = useUploadS3AWS({ defaultFiles: [] })
  const richEditorRef = useRef<CoreEditor>(undefined)

  const { refetch, data, hasNextPage, fetchNextPage, isFetching } = useInfinityGraphPage({
    queryDocumentNote: useMemo(() => QueryJobNotes, []),
    getVariable: useCallback(page => ({ jobId, limit: 10, page }), [jobId]),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.jobNotesList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.jobNotesList?.collection?.length)
    }),
    queryKey: ['job-note-list', jobId.toString()]
  })
  const { promiseMentionMembersOptions } = useMentionMemberManagement({ jobId })
  const { promiseHiringMemberOptions, optionHiringMemberDefault } = useQueryHiringMembersList({
    jobId,
    everyoneOption: {
      value: '-1',
      isHideAvatar: true,
      supportingObj: {
        name: `${t('job:detail:notes:hiring_member_select:default_name')}`,
        description: `${t('job:detail:notes:hiring_member_select:default_description')}`,
        defaultColour: defaultColorBgAvatar
      }
    }
  })

  const onAddNote = useCallback(
    async (comment: ICommentForm, formAction: IFormAction) => {
      if (files?.length) {
        setShowLockApp(t('label:savingNote'))
      }

      await clientGraphQL
        .mutation(MutationCreateNote, {
          commentableId: jobId,
          content: comment.content,
          sharedUserIds:
            comment?.sharedUsers && (comment?.sharedUsers[0]?.value === '-1' ? [] : comment?.sharedUsers.map(assignee => Number(assignee.value))),
          ...(files?.length
            ? {
                attachments: files.filter(item => item.status === 'pending').map(item => item.file)
              }
            : undefined),
          ...(comment?.withFollowUpTask ? { withFollowUpTask: comment.withFollowUpTask } : undefined),
          ...(comment?.dueDate ? { dueDate: formatISO(new Date(comment.dueDate)) } : undefined)
        })
        .toPromise()
        .then((result: IResponseContextResult<{}>) => {
          if (result.error) {
            setCloseLockApp()
            return catchErrorFromGraphQL({
              error: result.error,
              setToast
            })
          }

          refetch()
          if (formAction) {
            formAction.reset({
              content: '',
              sharedUsers: [optionHiringMemberDefault],
              withFollowUpTask: false,
              dueDate: undefined
            })
          }
          if (richEditorRef.current) {
            richEditorRef.current.commands.setContent('')
          }
          if (files?.length) {
            setCloseLockApp()
            setFiles([])
          }
          setToast({
            open: true,
            type: 'success',
            title: t('notification:note_added')
          })

          return
        })
    },
    [jobId, clientGraphQL, refetch, files]
  )
  const [onSubmitAddForm, submitting] = useAsyncAction(onAddNote, [onAddNote])

  const [edittingNote, setEditingNote] = useState<{
    content: string
    onEditSubmit: (data: ICommentForm, formAction: IFormAction) => Promise<void>
  }>()

  const onEditNote = useCallback(async (id?: number, data?: NoteFormType, fileList?: IAttachmentsFiles) => {
    if (fileList?.length) {
      setShowLockApp(t('label:savingNote'))
    }

    await clientGraphQL
      .mutation(MutationUpdateNote, {
        id: id,
        content: data?.content,
        sharedUserIds: data?.sharedUsers && (data?.sharedUsers[0]?.value === '-1' ? [] : data?.sharedUsers.map(assignee => Number(assignee.value))),
        ...(fileList?.length
          ? {
              attachments: fileList.filter(item => item.status === 'pending').map(item => item.file)
            }
          : undefined)
      })
      .toPromise()
      .then((result: IResponseContextResult<{}>) => {
        if (result.error) {
          setCloseLockApp()
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }

        refetch()
        if (fileList?.length) {
          setCloseLockApp()
          setFiles([])
        }
        setToast({
          open: true,
          type: 'success',
          title: t('notification:note_updated')
        })

        return
      })
  }, [])

  const [callDeleteNote, deleting] = useAsyncAction(async (id: number) => {
    await clientGraphQL
      .mutation(MutationDeleteNote, {
        id: id
      })
      .toPromise()
      .then((result: IResponseContextResult<{}>) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }

        refetch()
        setToast({
          open: true,
          type: 'success',
          title: t('notification:note_deleted')
        })

        return
      })
  }, [])

  const onDeleteNote = useCallback((id: number) => {
    deleteNoteModal(t, {
      onDelete: () => callDeleteNote(id)
    })
  }, [])

  const { trigger: triggerDeleteAttachment, isLoading: isLoadingAttachment } = useSubmitCommon(MutationDeleteJobNoteAttachment)
  const deleteJobNoteAttachmentCallback = useCallback(
    async (params: { attachmentId: string; index: number; noteId: string }) => {
      if (isLoadingAttachment) {
        return Promise.resolve(false)
      }

      return triggerDeleteAttachment({
        id: Number(params.noteId),
        attachmentId: Number(params.attachmentId)
      }).then(result => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
          return Promise.resolve(false)
        }

        const { jobNotesDeleteAttachment } = result.data
        if (jobNotesDeleteAttachment.success) {
          refetch()
          setToast({
            open: true,
            type: 'success',
            title: t('notification:attachment_deleted')
          })
        }

        return Promise.resolve(true)
      })
    },
    [isLoadingAttachment, triggerDeleteAttachment, setToast, refetch]
  )

  return (
    <div className="mx-auto mt-1 mb-[87px] max-w-[776px]">
      <div>
        {actionJobNote.create ? (
          <div className="flex items-start">
            <div className="mr-3">
              <Avatar color={user.defaultColour} size="md" src={user.avatarVariants?.thumb?.url} alt={user.fullName} />
            </div>
            <DynamicImportForm
              id="note-form"
              className="max-w-[730px] flex-1"
              defaultValue={{
                sharedUsers: [optionHiringMemberDefault]
              }}
              schema={commentFormSchema(t)}
              onSubmit={onSubmitAddForm}
            >
              {({ formState, control, setValue, reset, setError, submit }) => {
                return (
                  <div>
                    <Controller
                      control={control}
                      name="content"
                      defaultValue=""
                      render={({ field: { onChange, value } }) => {
                        return (
                          <>
                            <FormControlItem labelRequired destructiveText={formState.errors && (formState.errors?.content?.message as string)}>
                              <RichEditorWithActions
                                getElementAppendById=""
                                editorRef={(editor: CoreEditor) => {
                                  richEditorRef.current = editor
                                }}
                                autoFocus
                                isExpand={true}
                                setIsExpand={() => {}}
                                isExpandModal={isExpandModal}
                                setIsExpandModal={setIsExpandModal}
                                variant="outline"
                                value={value}
                                onChange={onChange}
                                destructiveText={formState.errors && (formState.errors?.content?.message as string)}
                                placeholder={`${t('candidates:tabs:candidateNote:addNotePlaceholder')}`}
                                size="sm"
                                classNameWrapper="min-w-full w-full"
                                className="w-full min-w-full"
                                extraToolbar={{
                                  attachments: {
                                    show: true,
                                    acceptedFiles: ACCEPT_FILES_COMMON,
                                    classNameItem: isExpandModal ? 'w-full' : 'w-[calc(35vw-90px)] tablet:w-[calc(35vw-110px)]',
                                    fileChange: fileList => {
                                      if ((fileList || []).length + files.length <= 10) {
                                        const arr: IAttachmentsFiles = []
                                        Array.from(fileList || []).forEach(file => {
                                          arr.push({
                                            id: undefined,
                                            url: '',
                                            name: file.name,
                                            status: file.size > MAXIMUM_5_MB || !LIST_ACCEPT_FILES_COMMON.includes(file.type) ? 'error' : 'pending',
                                            statusDescription:
                                              file.size > MAXIMUM_5_MB
                                                ? `${t('form:maximumSizeIs5MB')}`
                                                : !LIST_ACCEPT_FILES_COMMON.includes(file.type)
                                                  ? `${t('form:onlySupported_PDF_DOCX_DOC_PNG_JPEG')}`
                                                  : '',
                                            file: file
                                          })
                                        })
                                        setFiles([...files, ...arr])
                                      } else {
                                        setToast({
                                          open: true,
                                          type: 'error',
                                          title: `${t('common:modal:attachment_failed_title')}`,
                                          description: `${t('common:modal:attachment_failed_description')}`
                                        })
                                      }
                                    },
                                    itemPerRow: isExpandModal ? 'calc(33.33333% - 8px)' : 'calc(50% - 8px)',
                                    list: files,
                                    onDelete: ({ index }) => {
                                      setFiles((files || [])?.filter((_, i) => i !== index))
                                    }
                                  },
                                  expand: {
                                    show: true,
                                    isActive: isExpandModal,
                                    onClick: () => {
                                      setIsExpandModal(!isExpandModal)
                                    }
                                  },
                                  mentions: {
                                    show: true,
                                    suggestion: {
                                      component: ComboboxSelect,
                                      componentProps: {
                                        searchPlaceholder: `${t('label:placeholder:search')}`,
                                        loadingMessage: `${t('label:loading')}`,
                                        noOptionsMessage: `${t('label:noOptions')}`,
                                        dropdownMenuClassName: 'min-w-[320px] max-w-[320px]',
                                        size: 'md',
                                        options: promiseMentionMembersOptions,
                                        configSelectOption: {
                                          avatar: true
                                        }
                                      },
                                      componentPropsEditor: {
                                        defaultOpenDropdown: true,
                                        renderTrigger: () => <span className="absolute -top-[7px] left-[7px] h-4 w-4" />,
                                        dropdownMenuClassName: 'min-w-[320px] max-w-[320px]',
                                        size: 'md',
                                        options: promiseMentionMembersOptions,
                                        configSelectOption: {
                                          avatar: true
                                        }
                                      }
                                    }
                                  }
                                }}
                                actions={{
                                  modal: {
                                    title: `${t('button:addNote')}`
                                  },
                                  onSubmitText: t('job:detail:notes:form:button_add_note') || '',
                                  onSubmit: value => {
                                    if (value === '') {
                                      setError('content', {
                                        type: 'custom',
                                        message: `${t('form:requiredField')}`
                                      })
                                    } else {
                                      submit && submit()
                                      if (isExpandModal) {
                                        setIsExpandModal(false)
                                      }
                                    }
                                  },
                                  onCancel: () => {
                                    if (isExpandModal) {
                                      setIsExpandModal(false)
                                    }
                                    reset()
                                    if (richEditorRef.current) {
                                      richEditorRef.current.commands.setContent('')
                                    }
                                    setFiles([])
                                  },
                                  onCancelText: `${t('button:cancel')}`,
                                  leftComponents: () => (
                                    <div className="flex items-center space-x-4">
                                      <Controller
                                        control={control}
                                        name="sharedUsers"
                                        defaultValue={[]}
                                        render={({ field: { onChange, value } }) => {
                                          return (
                                            <ComboboxSelect
                                              tooltipOption={{
                                                content: t('label:placeholder:visibleTo')
                                              }}
                                              getCustomValueRender={values => {
                                                return (values as ISelectOption[]).find(item => item.value === '-1') ? (
                                                  <>{t('label:everyone')}</>
                                                ) : undefined
                                              }}
                                              options={promiseHiringMemberOptions}
                                              size="sm"
                                              isMulti
                                              dropdownMenuClassName="w-[320px]!"
                                              containerMenuClassName="max-w-[320px]"
                                              isClearable={false}
                                              onChange={(newValue, actionMeta) => {
                                                if ((newValue as ISelectOption[])?.length > 0) {
                                                  const filterOption =
                                                    actionMeta?.option?.value === '-1'
                                                      ? [actionMeta.option]
                                                      : ((newValue as ISelectOption[]) || [])?.filter(item => item.value !== '-1')
                                                  onChange(filterOption)
                                                } else {
                                                  setToast({
                                                    open: true,
                                                    type: 'error',
                                                    title: t('notification:pleaseSelectAtLeastAssignee', { number: 1 })
                                                  })
                                                }
                                              }}
                                              placeholder={`${t('label:placeholder:visibleTo')}`}
                                              searchPlaceholder={`${t('label:placeholder:visibleTo')}`}
                                              loadingMessage={`${t('label:loading')}`}
                                              noOptionsMessage={`${t('label:noOptions')}`}
                                              value={value}
                                              destructive={formState.errors && !!formState.errors.sharedUsers}
                                            />
                                          )
                                        }}
                                      />

                                      <div className="flex items-center space-x-[3px]">
                                        <Controller
                                          control={control}
                                          name="withFollowUpTask"
                                          render={({ field: { onChange: onChangeFollowUpTask, value: valueFollowUpTask } }) => (
                                            <Checkbox
                                              size="sm"
                                              text={`${t('label:follow_up_task')}`}
                                              isChecked={!!valueFollowUpTask}
                                              onCheckedChange={e => {
                                                const checked = e.target.checked
                                                onChangeFollowUpTask(checked)
                                              }}
                                            />
                                          )}
                                        />
                                        <Controller
                                          control={control}
                                          name="dueDate"
                                          defaultValue={undefined}
                                          render={({ field: { onChange, value: valueDueDate } }) => {
                                            const [openDueDate, setOpenDueDate] =
                                              // eslint-disable-next-line react-hooks/rules-of-hooks
                                              useState<boolean>(false)

                                            return (
                                              <Popover open={openDueDate} onOpenChange={open => setOpenDueDate(open)}>
                                                <PopoverTrigger asChild>
                                                  <div>
                                                    <TextButton
                                                      classNameText="font-normal"
                                                      className="text-primary-600 [&>svg]:text-primary-600"
                                                      size="md"
                                                      type="secondary"
                                                      icon="trailing"
                                                      iconMenus="ChevronDown"
                                                      underline={false}
                                                      label={
                                                        valueDueDate ? defaultFormatDate(new Date(valueDueDate)) : t('label:placeholder:dueDate')
                                                      }
                                                    />
                                                  </div>
                                                </PopoverTrigger>

                                                <PopoverPortal>
                                                  <PopoverContent side="top" align="start" className="w-full min-w-[300px]">
                                                    <SingleDatePicker
                                                      locale={i18n.language}
                                                      useOnlyFunction
                                                      className="max-w-[107px]"
                                                      config={{
                                                        defaultOpen: true,
                                                        disabled: {
                                                          before: new Date()
                                                        },
                                                        onChange,
                                                        value: valueDueDate ? new Date(valueDueDate) : undefined,
                                                        showSelectTime: true
                                                      }}
                                                      placeholder={`${t('label:placeholder:dueDate')}`}
                                                      size="sm"
                                                    />
                                                  </PopoverContent>
                                                </PopoverPortal>
                                              </Popover>
                                            )
                                          }}
                                        />
                                      </div>
                                    </div>
                                  )
                                }}
                              />
                            </FormControlItem>
                            {edittingNote && (
                              <div className="mt-3 flex justify-end">
                                <Button
                                  className="mr-2"
                                  size="sm"
                                  onClick={() => {
                                    reset()
                                    if (richEditorRef.current) {
                                      richEditorRef.current.commands.setContent('')
                                    }
                                    setEditingNote(undefined)
                                  }}
                                  label={`${t('button:cancel')}`}
                                  type="secondary"
                                />
                              </div>
                            )}
                          </>
                        )
                      }}
                    />
                  </div>
                )
              }}
            </DynamicImportForm>
          </div>
        ) : null}

        <div>
          {data?.pages?.[0]?.jobNotesList?.metadata?.totalCount === 0 ? (
            <div className="mt-[80px]">
              <Empty type="empty-data" title={`${t('job:detail:notes:empty:title')}`} description={`${t('job:detail:notes:empty:description')}`} />
            </div>
          ) : null}

          <ShowMore hasNextPage={hasNextPage} isFetching={isFetching} fetchNextPage={() => fetchNextPage()}>
            <div className="mt-6 space-y-6">
              {data?.pages.map(page =>
                page?.jobNotesList?.collection.map(item => (
                  <NoteItem
                    isDrawer={false}
                    jobId={jobId}
                    key={item.id}
                    note={item}
                    allowAction={{
                      delete: checkNoteCTA(jobStatus, NodeAction.DELETE),
                      edit: checkNoteCTA(jobStatus, NodeAction.EDIT)
                    }}
                    ownedAction={{
                      delete: actionJobNote.owned_delete,
                      edit: actionJobNote.owned_update
                    }}
                    onEditNote={(id, data, files) => onEditNote(id, data, files)}
                    onDeleteNote={onDeleteNote}
                    onDeleteAttachment={deleteJobNoteAttachmentCallback}
                    setFormInActive={setFormInActive}
                    formInActive={formInActive}
                  />
                ))
              )}
            </div>
          </ShowMore>
        </div>
      </div>
    </div>
  )
}
const NotesTabProvider = ({ jobId, jobStatus }: NoteTabPropsType) => {
  return <NoteTabContent jobId={jobId} jobStatus={jobStatus} />
}
export const checkNoteForbidden = (jobStatus: JOB_STATUS_ENUM, canAccessJobNote: boolean) => {
  return jobStatus === JOB_STATUS_ENUM.archived && canAccessJobNote
}

export default withQueryClientProvider(NotesTabProvider)
