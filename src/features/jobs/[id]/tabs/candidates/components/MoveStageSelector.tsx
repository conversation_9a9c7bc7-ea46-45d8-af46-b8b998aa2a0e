import type { FC } from 'react'
import { useCallback, useMemo, useState } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import useStaticData from 'src/hooks/data/use-static-data'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { openAlert } from '~/core/ui/AlertDialog'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import type { IDotColorProps } from '~/core/ui/Dot'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import type { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { NativeSelect } from '~/core/ui/NativeSelect'
import type { ISelectOption } from '~/core/ui/Select'

import { schemaApplicantMoveToStage } from '~/lib/features/candidates/schema/validation-applicant-move-to-stage'
import type { ICandidateApplicant } from '~/lib/features/candidates/types'
import type { IJobApplicants, IMarkAsHiredForm, IStageType } from '~/lib/features/jobs/types'
import { JOB_STAGE_GROUP } from '~/lib/features/jobs/utilities/enum'
import usePermissionPlacement from '~/lib/features/permissions/hooks/use-permission-placement'
import { mappingColorByStageType } from '~/lib/features/settings/hiring-pipelines/utilities/common'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import { IPlacement } from '~/features/placements/placement'
import type { ICreateEditPlacement } from '~/features/placements/schema'

import type { BulkStageItem } from './BulkActions'
import CreatePlacementBulkModal from './CreatePlacementBulkModal'
import MarkAsHiredBulkModal from './MarkAsHiredBulkModal'

export type MoveStageFormType = {
  jobStageId: ISelectOption
}

const MoveStageSelector: FC<{
  onSubmit: (data: MoveStageFormType) => Promise<any>
  onSubmitMarkAsHired?: (data: IMarkAsHiredForm, formAction?: IFormAction) => Promise<void>
  onSubmitCreatePlacement?: (data: ICreateEditPlacement) => Promise<any>
  onSubmitDeletePlacements?: (data: MoveStageFormType) => Promise<any>
  stages?: Array<BulkStageItem>
  applicants?: IJobApplicants[]
}> = ({ onSubmit, stages, applicants, onSubmitMarkAsHired, onSubmitCreatePlacement, onSubmitDeletePlacements }) => {
  const { t, i18n } = useTranslation()
  const { bulkValuesKanban } = useBoundStore()
  const { setToast } = useToastStore()
  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const [markAsHiredModal, setMarkAsHiredModal] = useState<boolean>(false)
  const [jobStageId, setJobStageId] = useState<undefined | number>()
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const { isFeatureEnabled, isUnLockFeature, data: dataPlan } = useSubscriptionPlan()

  const { actionPlacement, fullPermission: fullPermissionPlacement } = usePermissionPlacement()

  const isShowPlacementFeature = isFeatureEnabled(PLAN_FEATURE_KEYS.placement) && isUnLockFeature(PLAN_FEATURE_KEYS.placement)

  const isOpenCreatePlacementModal = useMemo(() => {
    return isCompanyKind || (isShowPlacementFeature && applicants?.[0]?.job?.permittedFields?.company?.value?.id)
  }, [isCompanyKind, isShowPlacementFeature, applicants])

  const applicantInBulkValues = useMemo(() => {
    return (applicants || []).filter(applicant => bulkValuesKanban?.includes(applicant.id))
  }, [applicants, bulkValuesKanban])

  const isIncludeHiredStageApplicant = useMemo(() => {
    return applicantInBulkValues.some(applicant => applicant.jobStage.stageGroup === JOB_STAGE_GROUP.hires)
  }, [applicantInBulkValues])

  const isAllBulkApplicantsAreOnHiredStage = useMemo(() => {
    return applicantInBulkValues.every(applicant => applicant.jobStage.stageGroup === JOB_STAGE_GROUP.hires)
  }, [applicantInBulkValues])

  const canPermissionDeletePlacement = useMemo(() => {
    return applicantInBulkValues.every(
      applicant =>
        applicant.jobStage.stageGroup === JOB_STAGE_GROUP.hires && (applicant.placement?.editablePlacement || applicant?.placement?.ownedPlacement)
    )
  }, [applicantInBulkValues])

  const stageTypes = useStaticData({
    keyName: 'agency_stageTypes',
    locale: i18n.language
  })
  const getColorOfState = (stageType: string) =>
    mappingColorByStageType(stageTypes.filter((item: IStageType) => String(item.id) === String(stageType))?.[0]?.colorClassName) as
      | IDotColorProps
      | undefined

  const handleShowToastErrPermission = useCallback((title: string) => {
    setToast({
      open: true,
      type: 'error',
      title
    })
  }, [])

  const openMarkAsHiredModal = useCallback(
    (isOpen: boolean) => {
      if (isOpenCreatePlacementModal && !actionPlacement.create) {
        handleShowToastErrPermission(t('placements:permission:createPlacement'))
        return
      }
      setMarkAsHiredModal(isOpen)
    },
    [actionPlacement.create, isOpenCreatePlacementModal]
  )

  const handleSubmit = async (data: MoveStageFormType) => {
    setOpenDialog(false)

    const moveToHiredStageType = data?.jobStageId?.supportingObj?.shortDescription === JOB_STAGE_GROUP.hires
    const hasDeletePermissionOrFullPermission = canPermissionDeletePlacement || fullPermissionPlacement

    // move to hire stage but not have permission
    if (moveToHiredStageType && isAllBulkApplicantsAreOnHiredStage && !hasDeletePermissionOrFullPermission) {
      handleShowToastErrPermission(t('placements:permission:moveToAnotherStage'))
      return
    }
    // move to hire stage and have permission
    if (moveToHiredStageType && isAllBulkApplicantsAreOnHiredStage) {
      onSubmit(data)
      return
    }
    // move to hire stage and open mark as hired modal
    if (moveToHiredStageType) {
      setJobStageId(Number(data?.jobStageId?.value))
      openMarkAsHiredModal(true)
      return
    }
    // move to another stage but not have permission
    if (isIncludeHiredStageApplicant && !hasDeletePermissionOrFullPermission) {
      handleShowToastErrPermission(t('placements:permission:deletePlacement'))
      return
    }
    // move to another stage and show alert
    if (isIncludeHiredStageApplicant) {
      openAlert({
        isPreventAutoFocusDialog: false,
        className: 'w-[480px]',
        title: `${t('common:modal:moveApplicantsToNewStage')}`,
        description: isCompanyKind
          ? `${t('common:modal:moveApplicantsToNewStageAgencyDescription')}`
          : `${t('common:modal:moveApplicantsToNewStageDescription')}`,
        actions: [
          {
            label: `${t('button:cancel')}`,
            type: 'secondary',
            size: 'sm'
          },
          {
            isCallAPI: true,
            label: `${t('button:move')}`,
            size: 'sm',
            onClick: () => {
              if (isCompanyKind) onSubmitDeletePlacements && onSubmitDeletePlacements(data)
              else onSubmit(data)
            }
          }
        ]
      })
      return
    }
    // move to another stage
    onSubmit(data)
  }

  return (
    <>
      <Button
        size="sm"
        iconMenus="ArrowRight"
        type="secondary"
        onClick={() => {
          setOpenDialog(true)
        }}
        label={`${t('button:moveStage')}`}
        className="mx-1"
      />
      <Dialog open={openDialog} size="sm" onOpenChange={setOpenDialog} isPreventAutoFocusDialog={true} label={`${t('job:moveToStage')}`}>
        <DynamicImportForm isShowDebug={false} className="w-full" schema={schemaApplicantMoveToStage(t)} onSubmit={handleSubmit}>
          {({ formState, control }) => {
            return (
              <>
                <Controller
                  control={control}
                  name="jobStageId"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <FormControlItem
                        destructive={formState.errors && !!formState.errors?.jobStageId}
                        destructiveText={formState.errors && (formState.errors?.jobStageId?.message as string)}
                      >
                        <NativeSelect
                          options={(stages || []).map(stage => ({
                            value: String(stage.jobStageId),
                            dot: getColorOfState(String(stage.stageTypeId)),
                            supportingObj: {
                              name: stage.label,
                              shortDescription: stage?.stageGroup
                            }
                          }))}
                          size="sm"
                          onChange={onChange}
                          placeholder={`${t('label:placeholder:select')}`}
                          value={value}
                          menuPlacement="bottom"
                          configSelectOption={{
                            dot: true,
                            supportingText: ['name']
                          }}
                          classNameOverride={{
                            loadingMessage: `${t('label:loading')}`,
                            noOptionsMessage: `${t('label:noOptions')}`
                          }}
                        />
                      </FormControlItem>
                    )
                  }}
                />
                <div className="flex items-center justify-end pt-6">
                  <Button type="secondary" className="mr-3" size="sm" onClick={() => setOpenDialog(false)} label={`${t('button:cancel')}`} />
                  <Button isDisabled={false} isLoading={false} size="sm" htmlType="submit" label={`${t('button:move')}`} />
                </div>
              </>
            )
          }}
        </DynamicImportForm>
      </Dialog>
      {jobStageId &&
        (isOpenCreatePlacementModal ? (
          <CreatePlacementBulkModal
            open={markAsHiredModal}
            setOpen={openMarkAsHiredModal}
            applicants={applicants}
            onSubmit={onSubmitCreatePlacement}
            jobStageId={jobStageId}
          />
        ) : (
          <MarkAsHiredBulkModal
            open={markAsHiredModal}
            setOpen={openMarkAsHiredModal}
            applicants={applicants as unknown as ICandidateApplicant[]}
            onSubmit={onSubmitMarkAsHired}
            jobStageId={jobStageId}
          />
        ))}
    </>
  )
}

export default MoveStageSelector
