'use client'

import { formatDistanceToNowStrict } from 'date-fns'
import { t } from 'i18next'
import type { FC } from 'react'
import React, { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { areEqual, FixedSizeList as List } from 'react-window'
import InfiniteLoader from 'react-window-infinite-loader'

import configuration from '~/configuration'
import type { IRouterWithID } from '~/core/@types/global'
import { AGENCY_TENANT, DEFAULT_PAGE_SIZE } from '~/core/constants/enum'
import { Avatar } from '~/core/ui/Avatar'
import { Badge } from '~/core/ui/Badge'
import { Button } from '~/core/ui/Button'
import { Checkbox } from '~/core/ui/Checkbox'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { KanbanDraggable, KanbanDraggableContent, KanbanDroppable } from '~/core/ui/Kanban'
import { Skeleton } from '~/core/ui/Skeleton'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { checkIfArrayStringsContainsObjectKeys } from '~/core/utilities/common'
import { defaultFormatDate, monthDayFormatDate } from '~/core/utilities/format-date'
import { pushStateBrowser } from '~/core/utilities/is-browser'
import { adminAndMemberCanAction, limitedMemberCanAction } from '~/core/utilities/permission'

import type { IApplicableJobs } from '~/lib/features/calendar/types'
import type { ICandidateApplicant, ICandidateProfile } from '~/lib/features/candidates/types'
import { shortEnLocale } from '~/lib/features/candidates/utilities'
import { NOTE_TAB, OVERVIEW_TAB } from '~/lib/features/candidates/utilities/enum'
import QueryTenantCompanyJobApplicants from '~/lib/features/jobs/graphql/query-company-job-applicants'
import QueryTenantJobApplicants from '~/lib/features/jobs/graphql/query-job-applicants'
import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import type { IJobApplicants, IJobStages } from '~/lib/features/jobs/types'
import { JOB_APPLICANT_STATUS, JOB_STAGE_GROUP, JOB_STATUS_ENUM, PLACE_VIEW_PIPELINE_ENUM } from '~/lib/features/jobs/utilities/enum'
import usePermissionCandidate from '~/lib/features/permissions/hooks/use-permission-candidate'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import type { IPlaceViewPipeline } from '~/lib/features/settings/hiring-pipelines/types'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import { isFeedbackDue } from '~/components/Calendar/InterviewCalendarView'
import SendToCandidateButton from '~/components/Candidates/Profile/components/SendToCandidate/SendToCandidateButton'
import SendToClientButton from '~/components/Candidates/Profile/components/SendToClient/SendToClientButton'
import SocialLinks from '~/components/Candidates/SocialLinks'
import type { ChangeJobStageWithModalActionProps } from '~/components/ChangeJobStageWithModal/ChangeJobStageWithModalView'
import SkeletonContainer from '~/components/Skeleton'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import type { ISwitchLayoutView } from '~/components/SwitchLayout/SwitchLayoutView'

import type { IRefetchActions, IStageKanban } from '../CandidatesTabContent'
import DisqualifyCandidateAction from './DisqualifyCandidateAction'
import HiredInfoBadge from './HiredInfoBadge'
import ScheduleInterviewAction from './ScheduleInterviewAction'

const JobItem = ({
  isLoading,
  provided,
  actions,
  item,
  refetch,
  style,
  isDragging,
  openPlacementDetailModel,
  jobTitle,
  companyName,
  jobStages,
  jobStatus,
  setOpenMarkAsHired
}: {
  provided: any
  isLoading: boolean
  refetch: () => void
  actions?: {
    configSwitchLayout: {
      path: Array<string>
      redirectUrls: Array<string>
    }
    setConfigSwitchLayout: (param: { path: Array<string>; redirectUrls: Array<string> }) => void
    switchView: ISwitchLayoutView
    setSwitchView: (param: ISwitchLayoutView) => void
  }
  item: IJobApplicants
  style?: object
  isDragging?: boolean
  openPlacementDetailModel?: any
  jobTitle?: string
  companyName?: string
  jobStages?: IJobStages
  jobStatus?: string
  setOpenMarkAsHired?: ChangeJobStageWithModalActionProps['setOpenMarkAsHired']
}) => {
  const { t } = useTranslation()
  const { actionInterview } = usePermissionJob()
  const { actionApplicantNote, actionApplicant } = usePermissionCandidate({})
  const { currentRole, bulkValuesKanban, setBulkValuesKanban } = useBoundStore()
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const [applicantData, setApplicantData] = useState<ICandidateProfile>()
  const isFeedbackDueInterview =
    item?.nearestInterview?.fromDatetime || item?.lastestInterview?.fromDatetime
      ? isFeedbackDue({
          startTime: item?.nearestInterview?.fromDatetime || item?.lastestInterview?.fromDatetime,
          hasFeedback: item.nearestInterview
            ? item.nearestInterview?.ikitFeedbacksSummary?.length > 0
            : item.lastestInterview?.ikitFeedbacksSummary?.length > 0
        })
      : false

  function getStyle() {
    // If you don't want any spacing between your items
    // then you could just return this.
    // I do a little bit of magic to have some nice visual space
    // between the row items
    const combined = {
      ...style,
      ...provided.draggableProps.style
    }

    // Being lazy: this is defined in our css file
    const grid = 8

    // when dragging we want to use the draggable style for placement, otherwise use the virtual style
    const result = {
      ...combined,
      height: isDragging ? combined.height : combined.height - grid,
      left: isDragging ? combined.left : combined.left + 1,
      width: `calc(${combined.width} - 2px)`,
      minWidth: isDragging ? 262 : 'auto',
      maxWidth: isDragging ? 262 : 'auto',
      marginBottom: grid
    }

    return result
  }

  useEffect(() => {
    const formatData = {
      ...item.profile,
      applicantId: Number(item.id),
      createdAt: item.createdAt,
      company: item.job?.permittedFields?.company?.value,
      accountManagers: item.job?.accountManagers
    }

    setApplicantData(formatData)
  }, [item])

  if (isLoading) {
    return (
      <div className="shadow-dialog relative mb-0 flex h-[68px] items-center space-x-2 rounded-xs bg-white px-4 py-3 hover:bg-[#FBFBFD]">
        <Skeleton className="h-6 w-6 min-w-[24px] rounded-full" />
        <div className="w-full space-y-1">
          <Skeleton className="h-2 w-2/3" />
          <Skeleton className="h-2 w-full" />
        </div>
      </div>
    )
  }

  const permissionForAction = item.status !== JOB_APPLICANT_STATUS.rejected
  const applicant = {
    id: String(item.id),
    jobId: item.jobId,
    coverLetter: item.coverLetter,
    incoming: item.incoming,
    createdBy: item.createdBy,
    job: item.job
  }
  return (
    <KanbanDraggableContent className="kabanJobItem group/item relative mb-0 hover:bg-[#FBFBFD]" style={getStyle()} provided={provided}>
      <div
        className="relative flex flex-col"
        onClick={e => {
          if (actions?.setSwitchView) {
            //check this for preventing trigger this func when open another modal whose triggered button placed inside kanban item
            const targetEle = e.target as EventTarget & {
              closest?: any
            }
            if (!!targetEle.closest('.kabanJobItem')) {
              actions?.setSwitchView({
                id: item.profile?.id,
                applicantId: item.id,
                view: 'candidates'
              })
              pushStateBrowser({
                state: {
                  id: item.profile?.id,
                  applicantId: item.id
                },
                unused: '',
                url: configuration.path.candidates.detail(item.profile?.id, item.id)
              })
            }
          } else {
            window.open(configuration.path.candidates.detail(item.profile?.id, item.id), '_blank')
          }
        }}
      >
        <div className="flex items-center">
          <div className={cn('flex w-full flex-1 items-center', permissionForAction ? 'group-hover/item:pr-[86px]' : '')}>
            {!item.rejectedAt && !limitedMemberCanAction(currentRole?.code) && item.status !== JOB_APPLICANT_STATUS['rejected'] && (
              <div
                className={cn(
                  'absolute -top-3.5 -left-3.5 bg-white group-hover/item:inline-flex',
                  (bulkValuesKanban || []).includes(item.id) ? 'inline-flex' : 'hidden'
                )}
              >
                <Checkbox
                  size="sm"
                  isChecked={(bulkValuesKanban || []).includes(item.id)}
                  onCheckedChange={e => {
                    const checked = e.target.checked
                    const applicantIds = checked ? [...(bulkValuesKanban || []), item.id] : (bulkValuesKanban || []).filter(id => id !== item.id)
                    setBulkValuesKanban(applicantIds)
                  }}
                  onClick={e => e.stopPropagation()}
                />
              </div>
            )}
            <div>
              <Tooltip content={item.profile?.fullName}>
                <TypographyText className="line-clamp-1 text-sm font-medium break-all text-gray-900 hover:cursor-pointer hover:underline">
                  {item.profile?.fullName}
                </TypographyText>
              </Tooltip>
            </div>
            <div className="ml-2" onClick={e => e.stopPropagation()}>
              {Object.keys(item.profile?.links || {}).length > 0 ? (
                <SocialLinks
                  source={item.profile?.links || {}}
                  maxItems={Object.values(item.profile?.links || {}).flat().length > 2 ? 1 : 2}
                  size="2xs"
                />
              ) : null}
            </div>
          </div>
          {permissionForAction && (
            <div className="group/action invisible absolute -right-2.5 bg-white group-hover/item:visible" onClick={e => e.stopPropagation()}>
              <div className="flex rounded-xs border border-solid border-gray-100 p-0.5 shadow-2xs">
                <If condition={adminAndMemberCanAction(currentRole?.code) && !isCompanyKind}>
                  <div className="mr-1">
                    <SendToCandidateButton
                      applicantId={Number(item.id)}
                      applicantData={applicantData}
                      job={item?.job}
                      useIconButton={true}
                      positionTooltip={'top'}
                      jobStage={item?.jobStage}
                      refetch={refetch}
                      jobStages={jobStages}
                      showMoveStage={jobStatus !== JOB_STATUS_ENUM.archived}
                    />
                  </div>
                </If>
                <If condition={adminAndMemberCanAction(currentRole?.code) && isCompanyKind}>
                  <div className="mr-1">
                    <SendToClientButton
                      applicantId={Number(item.id)}
                      applicantData={applicantData}
                      job={item?.job}
                      useIconButton={true}
                      positionTooltip={'top'}
                      jobStage={item?.jobStage}
                      refetch={refetch}
                      jobStages={jobStages}
                      showMoveStage={jobStatus !== JOB_STATUS_ENUM.archived}
                    />
                  </div>
                </If>
                <If condition={actionInterview.create}>
                  <div className="mr-1">
                    <ScheduleInterviewAction
                      applicantId={Number(item.id)}
                      candidateProfile={item.profile}
                      applicant={applicant as unknown as IApplicableJobs}
                      reload={refetch}
                    />
                  </div>
                </If>
                <If condition={actionApplicantNote.owned_update}>
                  <div className="mr-1">
                    <Tooltip content={`${t('tooltip:note')}`}>
                      <IconButton
                        onClick={() => {
                          if (actions?.setSwitchView) {
                            actions?.setSwitchView({
                              id: item.profile?.id,
                              applicantId: item.id,
                              view: 'candidates'
                            })
                            pushStateBrowser({
                              state: {
                                id: item.profile?.id,
                                applicantId: item.id,
                                tabs: NOTE_TAB
                              },
                              unused: '',
                              url: `${configuration.path.candidates.detail(item.profile?.id, item.id)}?profile-tabs=${OVERVIEW_TAB}&tabs=${NOTE_TAB}`
                            })
                          } else {
                            window.open(
                              `${configuration.path.candidates.detail(item.profile?.id, item.id)}?profile-tabs=${OVERVIEW_TAB}&tabs=${NOTE_TAB}`,
                              '_blank'
                            )
                          }
                        }}
                        type="secondary"
                        size="xs"
                        iconMenus="FileSignature"
                      />
                    </Tooltip>
                  </div>
                </If>
                <If condition={actionApplicant.update}>
                  <DisqualifyCandidateAction
                    callback={refetch}
                    applicantId={Number(item.id)}
                    email={item.profile?.email}
                    fullName={item.profile?.fullName}
                    id={item.profile?.id.toString()}
                    defaultValue={applicant as unknown as ICandidateApplicant}
                  />
                </If>
              </div>
            </div>
          )}
          {item.flagNew && (
            <div className={cn('ml-4', permissionForAction ? 'group-hover/item:hidden' : '')}>
              <Badge color="green" radius="circular" size="sm">
                {t('label:new')}
              </Badge>
            </div>
          )}
        </div>

        <If condition={item?.createdAt || item?.updatedAt}>
          <div className="mt-2 flex justify-between">
            <div className="flex items-center space-x-2.5">
              <If condition={item?.createdAt}>
                <Tooltip
                  content={`${t('tooltip:createdAtDate', {
                    date: defaultFormatDate(new Date(item.createdAt))
                  })}`}
                >
                  <div className="flex items-center justify-center space-x-1">
                    <IconWrapper name="Clock" size={14} className="text-gray-400" />
                    <TypographyText className="text-xs font-medium text-gray-600">
                      {formatDistanceToNowStrict(new Date(item.createdAt), {
                        locale: shortEnLocale
                      })}
                    </TypographyText>
                  </div>
                </Tooltip>
              </If>
              <If condition={item?.updatedAt}>
                <Tooltip
                  content={`${t('tooltip:lastActivityDate', {
                    date: defaultFormatDate(new Date(item?.updatedAt))
                  })}`}
                >
                  <div className="flex items-center justify-center space-x-1">
                    <IconWrapper name="Activity" size={14} className="text-gray-400" />
                    <TypographyText className="text-xs font-medium text-gray-600">
                      {formatDistanceToNowStrict(new Date(item?.updatedAt), {
                        locale: shortEnLocale
                      })}
                    </TypographyText>
                  </div>
                </Tooltip>
              </If>
              <If
                condition={
                  checkIfArrayStringsContainsObjectKeys(['profileCvs'], item.profile?.permittedFields) &&
                  item.profile?.profileCvs &&
                  item.profile?.profileCvs.length > 0
                }
              >
                <Tooltip content={`${t('tooltip:viewCV')}`}>
                  <Button size="xs" type="secondary" iconMenus="Paperclip" className="h-5! w-5!" />
                </Tooltip>
              </If>
              <If
                condition={
                  (item.hiredDate || item.placement?.permittedFields?.hiredDate?.value) &&
                  !isCompanyKind &&
                  item.jobStage?.stageGroup === JOB_STAGE_GROUP.hires
                }
              >
                <HiredInfoBadge
                  openPlacementDetailModel={openPlacementDetailModel}
                  applicant={item}
                  onEditedHiredInfo={refetch}
                  jobTitle={jobTitle}
                  companyName={companyName}
                />
              </If>
              <If
                condition={
                  item.jobStage?.stageGroup !== JOB_STAGE_GROUP.hires && //Not Show interview badge from all stage type is Hired
                  (item?.nearestInterview?.fromDatetime || item?.lastestInterview?.fromDatetime)
                }
              >
                <Badge color={isFeedbackDueInterview ? 'red' : 'blue'} icon="CalendarCheck2" size="md" radius="rounded" type="iconLeading">
                  <div className="line-clamp-1 break-all">
                    {item?.nearestInterview?.fromDatetime || item?.lastestInterview?.fromDatetime
                      ? monthDayFormatDate(new Date(item?.nearestInterview?.fromDatetime || item?.lastestInterview?.fromDatetime))
                      : ''}
                  </div>
                </Badge>
              </If>
              <If condition={isCompanyKind && item.placement}>
                {item.placement?.permittedFields?.hiredDate?.value && (
                  <If
                    condition={adminAndMemberCanAction(currentRole?.code)}
                    fallback={
                      <Tooltip content={t('tooltip:hiredDate')}>
                        <div>
                          <Badge icon="CheckCircle" color="green" size="md" variant="outline" type="iconLeading">
                            <div className="line-clamp-1 break-all">
                              {monthDayFormatDate(new Date(item.placement?.permittedFields?.hiredDate?.value))}
                            </div>
                          </Badge>
                        </div>
                      </Tooltip>
                    }
                  >
                    <Tooltip content={t('tooltip:viewPlacementInfo')}>
                      <div
                        className="cursor-pointer"
                        onClick={e => {
                          e.stopPropagation()
                          e.preventDefault()
                          openPlacementDetailModel({
                            placement: item.placement,
                            header: {
                              candidateName: item.profile?.fullName,
                              jobTitle: jobTitle,
                              companyName: companyName
                            },
                            onPlacementEdited: () => {
                              refetch()
                              return Promise.resolve()
                            }
                          })
                        }}
                      >
                        <Badge icon="CheckCircle" color="green" size="md" variant="outline" type="iconLeading">
                          <div className="line-clamp-1 break-all">
                            {monthDayFormatDate(new Date(item.placement?.permittedFields?.hiredDate?.value))}
                          </div>
                        </Badge>
                      </div>
                    </Tooltip>
                  </If>
                )}
              </If>
            </div>
            <If condition={item.createdBy?.fullName}>
              <Tooltip
                content={`${t('tooltip:sourcedByUser', {
                  user: item.createdBy?.fullName || item.createdBy?.email
                })}`}
              >
                <div className="ml-2.5">
                  <Avatar
                    color={item?.createdBy?.defaultColour}
                    size="xs"
                    alt={item.createdBy?.fullName || item.createdBy?.email}
                    src={item.createdBy?.avatarVariants?.thumb?.url}
                  />
                </div>
              </Tooltip>
            </If>
          </div>
        </If>
      </div>
    </KanbanDraggableContent>
  )
}

const Row = React.memo(function Row(props: {
  index: number
  data: {
    isDragDisabledKanban?: boolean
    items?: IJobApplicants[]
    lastTotalCount?: number
    refetch: () => void
    actions?: {
      configSwitchLayout: {
        path: Array<string>
        redirectUrls: Array<string>
      }
      setConfigSwitchLayout: (param: { path: Array<string>; redirectUrls: Array<string> }) => void
      switchView: ISwitchLayoutView
      setSwitchView: (param: ISwitchLayoutView) => void
    }
    openPlacementDetailModel?: any
    jobTitle?: string
    companyName?: string
    jobStages?: IJobStages
    jobStatus?: string
    setOpenMarkAsHired: ChangeJobStageWithModalActionProps['setOpenMarkAsHired']
  }
  style?: object
}) {
  const { data, index, style } = props
  const {
    isDragDisabledKanban,
    items,
    lastTotalCount,
    actions,
    refetch,
    openPlacementDetailModel,
    jobTitle,
    companyName,
    jobStages,
    jobStatus,
    setOpenMarkAsHired
  } = data
  const item = items?.[index]

  if (lastTotalCount === index && index > DEFAULT_PAGE_SIZE) {
    return (
      <div style={{ ...style, pointerEvents: 'none' }}>
        <TypographyText className="mt-4 text-center text-base text-gray-600">{t('label:endOfList')}</TypographyText>
      </div>
    )
  }

  // We are rendering an extra item for the placeholder
  if (!item) {
    return null
  }

  return (
    <KanbanDraggable isDragDisabled={isDragDisabledKanban} key={item.id} draggableId={String(item.id)} index={index}>
      {providedDraggable => (
        <JobItem
          style={style}
          isLoading={false}
          provided={providedDraggable}
          actions={actions}
          item={item}
          refetch={refetch}
          openPlacementDetailModel={openPlacementDetailModel}
          jobTitle={jobTitle}
          companyName={companyName}
          jobStages={jobStages}
          jobStatus={jobStatus}
          setOpenMarkAsHired={setOpenMarkAsHired}
        />
      )}
    </KanbanDraggable>
  )
}, areEqual)

interface JobStageKanbanProps {
  getUUidV4?: string
  actions?: {
    configSwitchLayout: {
      path: Array<string>
      redirectUrls: Array<string>
    }
    setConfigSwitchLayout: (param: { path: Array<string>; redirectUrls: Array<string> }) => void
    switchView: ISwitchLayoutView
    setSwitchView: (param: ISwitchLayoutView) => void
  }
  isDragDisabledKanban?: boolean
  placeViewPipeline?: IPlaceViewPipeline
  el: IStageKanban
  ind?: number
  searchState?: string
  statusState?: string
  jobId?: IRouterWithID
  callbackLoaded?: (
    newValue: IStageKanban & {
      totalCount: number
      isRefetchAll: boolean
    }
  ) => void
  refetchActions?: IRefetchActions
  isFirstLoading: boolean
  openPlacementDetailModel?: any
  jobTitle?: string
  companyName?: string
  jobStages?: IJobStages
  jobStatus?: string
  isCompanyKind?: boolean
  setOpenMarkAsHired: ChangeJobStageWithModalActionProps['setOpenMarkAsHired']
}

const JobStageKanban: FC<JobStageKanbanProps> = ({
  getUUidV4 = '',
  actions,
  isDragDisabledKanban,
  placeViewPipeline,
  el,
  ind,
  searchState,
  statusState,
  jobId,
  callbackLoaded,
  refetchActions,
  isFirstLoading = true,
  openPlacementDetailModel,
  jobTitle,
  companyName,
  jobStages = [],
  jobStatus,
  isCompanyKind,
  setOpenMarkAsHired
}) => {
  const { t } = useTranslation()
  const { setRefetchMyList, refetchMyList, ignoreListRefetch, refetchMyDelete, setRefetchMyDelete } = useBoundStore()
  const [isRefetchAll, setRefetchAll] = useState(false)

  const queryKey = [String(el.jobStageId), getUUidV4, statusState, searchState].join('-')

  const { data, refetch, fetchNextPage, hasNextPage } = useInfinityGraphPage({
    queryDocumentNote: isCompanyKind ? QueryTenantCompanyJobApplicants : QueryTenantJobApplicants,
    getVariable: useCallback(
      page => ({
        jobId: Number(jobId),
        jobStageId: Number(el.jobStageId),
        limit: DEFAULT_PAGE_SIZE,
        search: searchState,
        status: statusState,
        page
      }),
      [jobId, el.jobStageId, searchState, statusState]
    ),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.jobApplicantsList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.jobApplicantsList?.collection?.length)
    }),
    queryKey: ['kanban-jobApplicantsList', queryKey]
  })

  const totalCount = data?.pages?.[data?.pages?.length - 1]?.jobApplicantsList?.metadata?.totalCount

  useEffect(() => {
    if (refetchMyList || refetchMyDelete) {
      if (!ignoreListRefetch?.includes('job-kanban')) {
        setTimeout(() => {
          refetch()
        }, 100)
      }
      setRefetchAll(true)
      setRefetchMyList(false)
      setRefetchMyDelete(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchMyList, refetchMyDelete])

  useEffect(() => {
    if (data && callbackLoaded) {
      callbackLoaded({
        ...el,
        isRefetchAll,
        totalCount,
        data: data?.pages
          ?.map(
            (item: {
              [type: string]: {
                collection: IJobApplicants[]
              }
            }) => item?.jobApplicantsList?.collection || []
          )
          .flat()
      })
      setRefetchAll(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  useEffect(() => {
    if (refetchActions?.length) {
      for (let i = 0; i < refetchActions.length; i++) {
        if (refetchActions?.[i]?.label === el?.label) {
          if (refetchActions?.[i]?.action === 'refetch') {
            refetch()
          }
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchActions])

  // Every row is loaded except for our loading indicator row.
  const isItemLoaded = (index: number) => !hasNextPage || index < el?.data?.length

  // ---------- RENDERED ----------
  const topSpace = useClassBasedTopSpace({
    34: window.innerHeight - 216 - 18,
    default: window.innerHeight - 182 - 18
  })
  const height = placeViewPipeline === PLACE_VIEW_PIPELINE_ENUM.jobDetail ? topSpace : 400

  return (
    <>
      <div
        className={cn(
          'rounded-t-2 sticky top-0 z-10 flex min-h-[28px] items-center space-x-2 px-2 pt-[11px]',
          placeViewPipeline === PLACE_VIEW_PIPELINE_ENUM.jobDetail ? 'bg-gray-50' : 'bg-gray-100'
        )}
      >
        <Tooltip content={el.label}>
          <TypographyText className="line-clamp-1 text-xs font-semibold text-gray-600 uppercase">{el.label}</TypographyText>
        </Tooltip>

        <Badge color="white" size="sm" radius="circular">
          {el?.totalCount || 0}
        </Badge>
      </div>

      <div className="min-w-[280px] px-2 pt-[11px] pb-2">
        <SkeletonContainer
          showMoreLabel={`${t('common:infinity:showMore')}`}
          useLoading={false}
          isFirstLoading={isFirstLoading}
          renderCustomSkeleton={
            <div style={{ height }}>
              <div className="shadow-dialog relative mb-0 flex h-[68px] items-center space-x-2 rounded-xs bg-white px-4 py-3 hover:bg-[#FBFBFD]">
                <Skeleton className="h-6 w-6 min-w-[24px] rounded-full" />
                <div className="w-full space-y-1">
                  <Skeleton className="h-2 w-2/3" />
                  <Skeleton className="h-2 w-full" />
                </div>
              </div>
            </div>
          }
        >
          <KanbanDroppable
            mode="virtual"
            droppableId={`${ind}`}
            renderClone={(provided, snapshot, data) => (
              <JobItem
                isLoading={!isItemLoaded(data.source.index)}
                provided={provided}
                actions={actions}
                item={el?.data?.[data.source.index] as IJobApplicants}
                refetch={refetch}
                isDragging={snapshot.isDragging}
                openPlacementDetailModel={openPlacementDetailModel}
                jobTitle={jobTitle}
                companyName={companyName}
                setOpenMarkAsHired={setOpenMarkAsHired}
              />
            )}
          >
            {provided => (
              <InfiniteLoader
                isItemLoaded={isItemLoaded}
                itemCount={totalCount > DEFAULT_PAGE_SIZE ? totalCount + 1 : totalCount}
                threshold={3}
                // @ts-expect-error - doesn't need to fix
                loadMoreItems={fetchNextPage}
              >
                {({ onItemsRendered, ref }) => (
                  <List
                    onItemsRendered={onItemsRendered}
                    ref={ref}
                    outerRef={provided.innerRef}
                    height={height}
                    itemCount={totalCount > DEFAULT_PAGE_SIZE ? el?.data?.length + 1 : el?.data?.length}
                    itemData={{
                      isDragDisabledKanban,
                      refetch,
                      actions,
                      items: el?.data,
                      lastTotalCount: el?.totalCount || 0,
                      openPlacementDetailModel,
                      jobTitle,
                      companyName,
                      jobStages,
                      jobStatus,
                      setOpenMarkAsHired
                    }}
                    itemSize={76}
                    width={264}
                  >
                    {Row}
                  </List>
                )}
              </InfiniteLoader>
            )}
          </KanbanDroppable>
        </SkeletonContainer>
      </div>
    </>
  )
}

export default React.memo(JobStageKanban)
