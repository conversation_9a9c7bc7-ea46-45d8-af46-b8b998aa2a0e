import type { FC } from 'react'
import { useTranslation } from 'react-i18next'

import { SpinnerIcon } from '~/core/ui/FillIcons'
import IconWrapper from '~/core/ui/IconWrapper'
import { TypographyText } from '~/core/ui/Text'
import { cn } from '~/core/ui/utils'

import useBoundStore from '~/lib/store'

const MovingStageBulkProgress: FC = () => {
  const { t } = useTranslation()
  const { bulkValuesKanban, bulkValuesKanbanUpdated } = useBoundStore()
  return (
    <div
      className={cn(
        'absolute right-0 bottom-0 z-10 mr-3 mb-2 mb-[23px] flex w-max transform items-center justify-between rounded-md bg-white shadow-[2px_4px_20px_0px_rgba(0,0,0,0.15)]'
      )}
    >
      <div className="flex space-x-3 p-4">
        <div className="py-0.5">
          <IconWrapper name="Loader2" className="animate-spin text-[20px] text-gray-500" />
        </div>
        <div>
          <TypographyText className="mb-1 font-medium text-gray-900">{t('notification:processing')}</TypographyText>
          <TypographyText className="text-sm text-gray-700">
            {t('notification:updatedApplicants', {
              progress: `${(bulkValuesKanbanUpdated || []).length}/${(bulkValuesKanban || []).length}`
            })}
          </TypographyText>
        </div>
      </div>
    </div>
  )
}

export default MovingStageBulkProgress
