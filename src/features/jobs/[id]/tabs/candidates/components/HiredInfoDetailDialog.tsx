import type { Di<PERSON><PERSON>, FC, ReactNode, SetStateAction } from 'react'
import { useTranslation } from 'react-i18next'
import { nullable } from 'zod'

import { Avatar } from '~/core/ui/Avatar'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import type { LucideIconName } from '~/core/ui/IconWrapper'
import IconWrapper from '~/core/ui/IconWrapper'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { changeTimezone, defaultFormatDate } from '~/core/utilities/format-date'

import { removeTzFromDate } from '~/lib/features/calendar/utilities/helper-schedule-interview'
import type { IJobApplicants } from '~/lib/features/jobs/types'
import useBoundStore from '~/lib/store'

import { ChangeJobStageWithModalActionProps } from '~/components/ChangeJobStageWithModal/ChangeJobStageWithModalView'
import ToolTipOnOverflow from '~/features/placements/components/ToolTipOnOverflow'

const RowInfoWrapper: FC<{
  hide?: boolean
  nameIcon: LucideIconName
  labelName: string
  content: string | ReactNode
  classNameLeftView?: string
}> = ({ nameIcon, labelName, content, classNameLeftView, hide = false }) => {
  return !hide ? (
    <div className="tablet:grid-cols-[132px_1fr] tablet:gap-4 mt-3 grid w-full grid-cols-1 gap-[2px]">
      <div className={`flex flex-row items-center self-start ${classNameLeftView || ''}`}>
        <IconWrapper size={16} className="flex-none text-gray-600" name={nameIcon} />
        <TypographyText className="ml-2 text-sm font-normal text-gray-700">{labelName}</TypographyText>
      </div>
      <div className="tablet:w-full text-sm text-gray-900">{content}</div>
    </div>
  ) : (
    <></>
  )
}
const HiredInfoDetailDialog: FC<{
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
  applicant?: IJobApplicants
  setOpenMarkAsHired?: () => void //ChangeJobStageWithModalActionProps['setOpenMarkAsHired']
}> = ({ open, setOpen, applicant, setOpenMarkAsHired }) => {
  const { t } = useTranslation()
  const { user } = useBoundStore()
  return open ? (
    <div
      onClick={e => {
        e.stopPropagation()
      }}
    >
      <Dialog
        open={open}
        onOpenChange={setOpen}
        isDivider={true}
        isPreventAutoFocusDialog={true}
        size="sm"
        label={`${t('job:hireInformation')}`}
        description={
          <div>
            <div className="flex items-center">
              <div className="mr-2">
                <IconWrapper size={16} name="User" />
              </div>
              <Tooltip content={applicant?.profile?.fullName}>
                <div className="line-clamp-1 text-sm text-gray-900">{applicant?.profile?.fullName}</div>
              </Tooltip>
            </div>
            <div className="mt-1.5 flex items-center">
              <div className="mr-2 shrink-0">
                <IconWrapper size={16} name="Briefcase" />
              </div>
              <ToolTipOnOverflow text={applicant?.job?.title} className="max-w-[200px] shrink-0 truncate text-sm text-gray-900" />
            </div>
          </div>
        }
      >
        <RowInfoWrapper
          hide={!applicant?.hiredDate}
          nameIcon="CalendarCheck2"
          labelName={`${t('label:hireDate')}`}
          content={
            <div className="flex text-sm text-gray-900">
              {applicant?.hiredDate &&
                defaultFormatDate(
                  changeTimezone({
                    date: applicant?.hiredDate,
                    timezone: user?.timezone
                  })
                )}
            </div>
          }
        />
        <RowInfoWrapper
          hide={!applicant?.onboardDate}
          nameIcon="Calendar"
          labelName={`${t('label:startDate')}`}
          content={
            <div className="flex text-sm text-gray-900">
              {applicant?.onboardDate &&
                defaultFormatDate(
                  changeTimezone({
                    date: applicant?.onboardDate,
                    timezone: user?.timezone
                  })
                )}
            </div>
          }
        />
        <RowInfoWrapper
          hide={!applicant?.hiredBy}
          nameIcon="UserCheck"
          labelName={`${t('placements:management:table:hiredBy')}`}
          content={
            <div className="flex items-center">
              <Avatar
                size="xs"
                color={applicant?.hiredBy?.defaultColour}
                src={applicant?.hiredBy?.avatarVariants?.thumb?.url}
                alt={applicant?.hiredBy?.fullName}
              />
              <Tooltip content={applicant?.hiredBy?.fullName}>
                <div className="ml-2 line-clamp-1 text-sm font-medium text-gray-900">{applicant?.hiredBy?.fullName}</div>
              </Tooltip>
            </div>
          }
        />
        <div className="flex justify-end pt-4">
          <Button
            icon="leading"
            iconMenus="Edit3"
            label={`${t('button:edit')}`}
            type="primary"
            size="sm"
            onClick={e => {
              e.stopPropagation()
              setOpenMarkAsHired && setOpenMarkAsHired()
            }}
          />
        </div>
      </Dialog>
    </div>
  ) : null
}

export default HiredInfoDetailDialog
