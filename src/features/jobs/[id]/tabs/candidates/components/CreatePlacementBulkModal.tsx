'use client'

import type { FC } from 'react'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { AGENCY_TENANT } from '~/core/constants/enum'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { But<PERSON> } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import IconWrapper from '~/core/ui/IconWrapper'
import { Tooltip } from '~/core/ui/Tooltip'

import type { IJobApplicants } from '~/lib/features/jobs/types'
import usePermissionPlacement from '~/lib/features/permissions/hooks/use-permission-placement'
import type { IPlacementCustomField } from '~/lib/features/placements/types/management-page-type'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import QueryAgencyPlacementFormFieldsList from '~/lib/features/settings/profile-fields/graphql/query-agency-placement-form-fields'
import { formatInitialValueCustomField } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useUserCheckKindOf } from '~/lib/hooks/use-user-check-kind'
import useBoundStore from '~/lib/store'

import LongContentDisplay from '~/components/LongContentDisplay'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import PlacementForm from '~/features/placements/components/PlacementForm'
import ToolTipOnOverflow from '~/features/placements/components/ToolTipOnOverflow'
import type { IPlacement } from '~/features/placements/placement'
import type { ICreateEditPlacement } from '~/features/placements/schema'

const CreatePlacementBulkModal: FC<{
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit?: (data: ICreateEditPlacement) => Promise<IPlacement>
  applicants?: IJobApplicants[]
  jobStageId: number
}> = ({ open, setOpen, onSubmit, applicants, jobStageId }) => {
  const { t } = useTranslation()
  const { user, bulkValuesKanban } = useBoundStore()
  const [isLoading, setLoading] = useState(false)
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const { userIsAsClient } = useUserCheckKindOf()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const isShowPlacementFeature = isFeatureEnabled(PLAN_FEATURE_KEYS.placement) && isUnLockFeature(PLAN_FEATURE_KEYS.placement)
  const { canAccessPage: canAccessPlacement } = usePermissionPlacement()
  const isAccessPlacement = isShowPlacementFeature && canAccessPlacement

  const { data: dataPlacementCustomFields, trigger: triggerPlacementFormFieldsList } = useQueryGraphQL({
    query: QueryAgencyPlacementFormFieldsList,
    variables: {},
    shouldPause: true
  })

  useEffect(() => {
    if ((isCompanyKind && !userIsAsClient()) || isAccessPlacement) {
      triggerPlacementFormFieldsList()
    }
  }, [])

  const placementCustomFields = useMemo(() => {
    return (dataPlacementCustomFields?.placementFieldsSettingList || []).filter((field: IPlacementCustomField) => field.field_level == 'custom')
  }, [dataPlacementCustomFields])

  const placementSystemFields = useMemo(() => {
    return (dataPlacementCustomFields?.placementFieldsSettingList || []).filter((field: IPlacementCustomField) => field.field_level == 'system')
  }, [dataPlacementCustomFields])

  const customFieldsFormatted = useMemo(() => {
    return formatInitialValueCustomField(placementCustomFields)
  }, [placementCustomFields])

  const defaultValue = useMemo<any>(
    () => ({
      jobStageId: String(jobStageId),
      typeOfSalary: 'annually',
      typeOfFee: 'percentage',
      currencyOfRevenue: user?.currentTenant?.currency,
      profitSplits: [
        {
          user_id: {
            value: user.id?.toString() as string,
            avatar: user.avatar,
            avatarVariants: user.avatarVariants,
            supportingObj: { name: user.fullName }
          },
          profit_percentage: '100'
        }
      ],
      applicantId: (applicants || [])?.[0]?.id,
      customFields: customFieldsFormatted
    }),
    [user, applicants, jobStageId, customFieldsFormatted]
  )
  const profileNames = useMemo(
    () =>
      applicants
        ?.filter(applicant => (bulkValuesKanban || []).includes(applicant.id))
        .map(applicant => applicant.profile?.fullName)
        .join(', ') || '',
    [applicants, bulkValuesKanban]
  )

  const jobInfo = useMemo(() => {
    return applicants?.[0]?.job
  }, [applicants])

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
      isDivider={true}
      isPreventAutoFocusDialog={true}
      size="md"
      label={`${t('placements:createPlacement')}`}
      description={
        <div className="mb-5">
          <div className="flex items-center">
            <div className="mr-2 shrink-0">
              <IconWrapper size={16} name="User" />
            </div>
            <Tooltip content={profileNames}>
              <div className="line-clamp-2 text-sm text-gray-900">
                <LongContentDisplay
                  isHTML
                  limitLines={2}
                  className="overflow-hidden text-sm text-ellipsis text-gray-900"
                  content={profileNames}
                  textButtonProps={{ size: 'md' }}
                  classNameWrapper="text-ellipsis"
                />
              </div>
            </Tooltip>
          </div>
          <div className="mt-1.5 flex items-center">
            <div className="mr-2 shrink-0">
              <IconWrapper size={16} name="Briefcase" />
            </div>
            <ToolTipOnOverflow text={jobInfo?.title} className="max-w-[200px] shrink-0 truncate text-sm text-gray-900" />
            <div className="mr-2 ml-2 h-0.5 w-0.5 shrink-0 rounded-full bg-gray-400"></div>
            <ToolTipOnOverflow className="shrink-1 truncate" text={jobInfo?.company?.permittedFields?.name?.value} />
          </div>
        </div>
      }
      headingClassName="tablet:pb-0"
    >
      {defaultValue && (
        <PlacementForm
          placementSystemFields={placementSystemFields}
          placementCustomFields={placementCustomFields}
          hiddenFields={['status']}
          onSubmit={data => {
            if (onSubmit) {
              setLoading(true)

              return onSubmit(data).then(placement => {
                setLoading(false)
                setOpen(false)
              })
            }

            return Promise.reject('Handler not provided')
          }}
          defaultValue={defaultValue}
        >
          <div className="mt-6 flex items-center justify-end space-x-3">
            <Button label={`${t('button:cancel')}`} size="sm" type="secondary" onClick={() => setOpen(false)} />
            <Button isDisabled={isLoading} isLoading={isLoading} size="sm" label={`${t('interview:email_modal:save')}`} htmlType="submit" />
          </div>
        </PlacementForm>
      )}
    </Dialog>
  )
}

export default CreatePlacementBulkModal
