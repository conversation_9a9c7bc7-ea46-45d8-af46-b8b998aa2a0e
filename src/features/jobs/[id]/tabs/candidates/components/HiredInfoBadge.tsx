import type { FC } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import { Badge } from '~/core/ui/Badge'
import If from '~/core/ui/If'
import { Tooltip } from '~/core/ui/Tooltip'
import { changeTimezone, defaultFormatDate, monthDayFormatDate } from '~/core/utilities/format-date'
import { adminAndMemberCanAction, limitedMemberCanAction } from '~/core/utilities/permission'

import type { IJobApplicants } from '~/lib/features/jobs/types'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBoundStore from '~/lib/store'

import ChangeJobStageWithModalView from '~/components/ChangeJobStageWithModal/ChangeJobStageWithModalView'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import HiredInfoDetailDialog from './HiredInfoDetailDialog'

const HiredInfoBadge: FC<{
  applicant?: IJobApplicants
  onEditedHiredInfo?: () => void
  openPlacementDetailModel?: any
  jobTitle?: string
  companyName?: string
}> = ({ applicant, onEditedHiredInfo, openPlacementDetailModel, jobTitle, companyName }) => {
  const { t } = useTranslation()
  const { currentRole, user } = useBoundStore()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const enablePlacement = isFeatureEnabled(PLAN_FEATURE_KEYS.placement) && isUnLockFeature(PLAN_FEATURE_KEYS.placement)
  const [hiredInfoDetail, setHiredInfoDetail] = useState<boolean>(false)
  const hiredDate = applicant?.hiredDate || applicant?.placement?.permittedFields?.hiredDate?.value
  if (enablePlacement && applicant?.placement?.permittedFields?.hiredDate?.value)
    return (
      <If
        condition={adminAndMemberCanAction(currentRole?.code)}
        fallback={
          <Tooltip content={t('tooltip:hiredDate')}>
            <div>
              <Badge icon="CheckCircle" color="green" size="md" variant="outline" type="iconLeading">
                <div className="line-clamp-1 break-all">{monthDayFormatDate(new Date(applicant?.placement?.permittedFields?.hiredDate?.value))}</div>
              </Badge>
            </div>
          </Tooltip>
        }
      >
        <Tooltip content={t('tooltip:viewPlacementInfo')}>
          <div
            className="cursor-pointer"
            onClick={e => {
              e.stopPropagation()
              e.preventDefault()
              openPlacementDetailModel({
                placement: applicant.placement,
                header: {
                  candidateName: applicant.profile?.fullName,
                  jobTitle: jobTitle,
                  companyName: companyName
                },
                onPlacementEdited: () => {
                  onEditedHiredInfo && onEditedHiredInfo()
                  return Promise.resolve()
                }
              })
            }}
          >
            <Badge icon="CheckCircle" color="green" size="md" variant="outline" type="iconLeading">
              <div className="line-clamp-1 break-all">{monthDayFormatDate(new Date(applicant?.placement?.permittedFields?.hiredDate?.value))}</div>
            </Badge>
          </div>
        </Tooltip>
      </If>
    )
  return (
    <ChangeJobStageWithModalView>
      {({ setOpenMarkAsHired, setApplicantCurrent }) => {
        return (
          <>
            {applicant && hiredDate && (
              <div
                onClick={e => {
                  e.stopPropagation()
                  if (!limitedMemberCanAction(currentRole?.code)) {
                    setApplicantCurrent &&
                      setApplicantCurrent({
                        item: {
                          ...applicant,
                          jobId: String(applicant.jobId),
                          jobStageId: Number(applicant?.jobStage?.id)
                        },
                        callback: () => {
                          onEditedHiredInfo && onEditedHiredInfo()
                          setApplicantCurrent({})
                        }
                      })
                    setHiredInfoDetail(true)
                  }
                }}
              >
                <Tooltip
                  content={
                    limitedMemberCanAction(currentRole?.code)
                      ? `${defaultFormatDate(
                          changeTimezone({
                            date: hiredDate,
                            timezone: user.timezone
                          })
                        )}`
                      : t('tooltip:viewHireInformation', {
                          time: monthDayFormatDate(
                            changeTimezone({
                              date: hiredDate,
                              timezone: user.timezone
                            })
                          )
                        })
                  }
                >
                  <Badge icon="CheckCircle" size="md" radius="rounded" variant="outline" classNameIcon="text-green-500" type="iconLeading">
                    <div className="line-clamp-1 break-all">
                      {monthDayFormatDate(
                        changeTimezone({
                          date: hiredDate,
                          timezone: user.timezone
                        })
                      )}
                    </div>
                  </Badge>
                </Tooltip>
              </div>
            )}
            <HiredInfoDetailDialog
              open={hiredInfoDetail}
              setOpen={setHiredInfoDetail}
              applicant={applicant}
              setOpenMarkAsHired={() => {
                setOpenMarkAsHired && setOpenMarkAsHired(true)
                setHiredInfoDetail(false)
              }}
            />
          </>
        )
      }}
    </ChangeJobStageWithModalView>
  )
}

export default HiredInfoBadge
