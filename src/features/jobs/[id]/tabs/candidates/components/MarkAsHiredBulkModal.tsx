import type { FC } from 'react'
import { useMemo } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { AsyncSingleSearchWithSelect } from '~/core/ui/AsyncSingleSearchWithSelect'
import { <PERSON><PERSON> } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import type { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import IconWrapper from '~/core/ui/IconWrapper'
import { SingleDatePicker } from '~/core/ui/SingleDatePicker'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'

import useQueryHiringMembersList from '~/lib/features/candidates/hooks/use-query-hiring-members-list'
import schemaMarkAsHiredBulkForm from '~/lib/features/candidates/schema/validation-mark-as-hired-bulk'
import type { ICandidateApplicant } from '~/lib/features/candidates/types'
import type { IMarkAsHiredBulkForm } from '~/lib/features/jobs/types'
import useBoundStore from '~/lib/store'

import LongContentDisplay from '~/components/LongContentDisplay'

const MarkAsHiredBulkModal: FC<{
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit?: (data: IMarkAsHiredBulkForm, formAction?: IFormAction) => Promise<void>
  applicants?: Array<ICandidateApplicant & { jobStageId?: number }>
  isDisableSubmitButton?: boolean
  jobStageId: number
}> = ({ onSubmit, applicants = [], isDisableSubmitButton, open, setOpen, jobStageId }) => {
  const { t, i18n } = useTranslation()
  const { bulkValuesKanban, user } = useBoundStore()
  const { promiseHiringMemberOptions: fetchRecruiters } = useQueryHiringMembersList({
    jobId: Number(applicants?.[0]?.job?.id)
  })
  const profileNames = useMemo(
    () =>
      applicants
        ?.filter(applicant => (bulkValuesKanban || []).includes(applicant.id))
        .map(applicant => applicant.profile?.fullName)
        .join(', ') || '',
    [applicants, bulkValuesKanban]
  )
  return (
    <Dialog
      className="min-w-[480px]"
      open={open}
      size="sm"
      onOpenChange={setOpen}
      isPreventAutoFocusDialog={true}
      label={`${t('label:markAsHired')}`}
      description={
        <>
          <div className="mb-1 flex items-start">
            <div className="py-1">
              <IconWrapper name="User" size={16} className="mr-2 text-gray-600" />
            </div>
            <Tooltip content={profileNames}>
              <LongContentDisplay
                isHTML
                limitLines={2}
                className="overflow-hidden text-sm text-ellipsis text-gray-900"
                content={profileNames}
                textButtonProps={{ size: 'md' }}
                classNameWrapper="text-ellipsis"
              />
            </Tooltip>
          </div>
          <div className="flex items-center">
            <div>
              <IconWrapper name="Briefcase" size={16} className="mr-2 text-gray-600" />
            </div>
            <Tooltip content={applicants?.[0]?.job?.title}>
              <TypographyText className="line-clamp-1 text-sm text-gray-900">{applicants?.[0]?.job?.title}</TypographyText>
            </Tooltip>
          </div>
        </>
      }
      headingClassName="pb-4 tablet:pb-5 border-b border-gray-100 mb-4"
    >
      <DynamicImportForm
        isShowDebug={false}
        id="mark-as-hired"
        className="w-full"
        schema={schemaMarkAsHiredBulkForm()}
        defaultValue={{
          hiredDate: new Date(),
          jobStageId: jobStageId,
          hiredById: {
            value: user?.id ? user?.id.toString() : undefined,
            avatar: user.avatarVariants?.thumb?.url,
            avatarVariants: user.avatarVariants,
            supportingObj: {
              defaultColour: user.defaultColour,
              description: '',
              name: String(user.fullName || user.email)
            }
          }
        }}
        onSubmit={onSubmit}
      >
        {({ formState, control }) => {
          return (
            <div>
              <div className="mb-4">
                <FormControlItem label={`${t('label:hireDate')}`} labelRequired>
                  <Controller
                    control={control}
                    name="hiredDate"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <SingleDatePicker
                          locale={i18n.language}
                          className="w-full"
                          config={{
                            id: 1,
                            defaultOpen: false,
                            onChange,
                            value: value,
                            showClearIndicator: false
                          }}
                          placeholder={`${t('label:placeholder:selectDate')}`}
                          size="sm"
                        />
                      )
                    }}
                  />
                </FormControlItem>
              </div>
              <div className="mb-4">
                <FormControlItem label={`${t('label:startDate')}`}>
                  <Controller
                    control={control}
                    name="onboardDate"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <SingleDatePicker
                          locale={i18n.language}
                          className="w-full"
                          config={{
                            id: 2,
                            defaultOpen: false,
                            onChange,
                            value: value,
                            showClearIndicator: true,
                            onClear: () => onChange(undefined)
                          }}
                          placeholder={`${t('label:placeholder:selectDate')}`}
                          size="sm"
                        />
                      )
                    }}
                  />
                </FormControlItem>
              </div>
              <div className="mb-4">
                <Controller
                  control={control}
                  name="hiredById"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <FormControlItem
                        destructive={formState.errors && !!formState.errors.hiredById}
                        destructiveText={formState.errors && (formState.errors.hiredById?.message as string)}
                        label={`${t('label:hiredBy')}`}
                        labelRequired
                      >
                        <AsyncSingleSearchWithSelect
                          promiseOptions={fetchRecruiters}
                          isSearchable
                          size="sm"
                          isClearable={false}
                          onChange={onChange}
                          placeholder={`${t('label:placeholder:select')}`}
                          configSelectOption={{
                            supportingText: ['name', 'description'],
                            avatar: true,
                            isHideAvatarSelectedOption: true
                          }}
                          classNameOverride={{
                            loadingMessage: `${t('label:loading')}`,
                            noOptionsMessage: `${t('label:noOptions')}`
                          }}
                          value={value}
                          destructive={formState.errors && !!formState.errors.hiredById}
                          menuPlacement="top"
                        />
                      </FormControlItem>
                    )
                  }}
                />
              </div>
              <div className="tablet:-mx-6 -mx-4 mt-6">
                <div className="tablet:px-6 flex justify-end space-x-3 px-4">
                  <Button type="secondary" size="sm" onClick={() => setOpen(false)} label={`${t('button:cancel')}`} />
                  <Button
                    size="sm"
                    isDisabled={isDisableSubmitButton}
                    isLoading={isDisableSubmitButton}
                    htmlType="submit"
                    label={!applicants?.[0]?.hiredDate ? `${t('button:markAsHired')}` : `${t('button:save')}`}
                  />
                </div>
              </div>
            </div>
          )
        }}
      </DynamicImportForm>
    </Dialog>
  )
}

export default MarkAsHiredBulkModal
