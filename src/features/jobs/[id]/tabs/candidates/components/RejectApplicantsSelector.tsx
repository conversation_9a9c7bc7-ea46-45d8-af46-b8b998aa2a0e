import type { FC } from 'react'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_URL } from '~/core/constants/env'
import { openAlert } from '~/core/ui/AlertDialog'
import { <PERSON><PERSON> } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'

import { QueryGetDefaultTemplateRejctApplicant } from '~/lib/features/candidates/graphql/query-reject-reasons-applicants'
import { useQueryGetDefaultRejectTemplate } from '~/lib/features/candidates/hooks/use-query-candidate-applicants-reject-reason'
import { schemaRejectApplicant } from '~/lib/features/candidates/schema/validation-create-profile'
import type { IJobApplicants } from '~/lib/features/jobs/types'
import useWorkSpace from '~/lib/features/settings/workspace/hooks/use-workspace'
import useBoundStore from '~/lib/store'

import SubmitRejectCandidateForm from '~/components/Candidates/SubmitRejectCandidateForm'
import type { IEmailForm } from '~/components/SendMailFormControl/EmailContentEditor'

const RejectApplicantsSelector: FC<{
  applicants?: Array<IJobApplicants>
  onSubmit: (params: IEmailForm) => Promise<any>
}> = ({ applicants, onSubmit }) => {
  const { t, i18n } = useTranslation()
  const { user, bulkValuesKanban } = useBoundStore()
  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const { tenantShow: dataCompany } = useWorkSpace({
    shouldPause: false
  })

  const canonical_url = useMemo(() => {
    return `${PUBLIC_APP_URL}${dataCompany?.careerSiteSettings?.canonical_url || ''}`
  }, [dataCompany])

  const emails = useMemo(
    () =>
      (applicants || [])
        .filter(applicant => (bulkValuesKanban || []).includes(String(applicant.id)))
        ?.map((candidate: { profile?: { email?: string[] } }) => candidate?.profile?.email || [])
        ?.flatMap(emails => emails)
        .filter((value, index, self) => self.indexOf(value) === index),
    [applicants, bulkValuesKanban]
  )

  const isMissingEmailApplicant = useMemo(() => emails?.length > 0 && emails?.length < (bulkValuesKanban || [])?.length, [bulkValuesKanban, emails])

  const {
    // trigger: triggerGetDefaultRejectTemplate,
    data: defaultTemplateEmail
  } = useQueryGetDefaultRejectTemplate({
    query: QueryGetDefaultTemplateRejctApplicant,
    variables: {},
    shouldPause: false
  })

  const placeholderValue = useMemo(() => {
    const candidatesName = `${t('candidates:candidateSendMail:candidateName')}`
    const job = applicants?.[0]?.job
    const job_link = `${canonical_url}${job?.id}`

    return {
      candidate_email: emails?.join(', ') || '',
      candidate_name: `<strong>${candidatesName}</strong>`,
      candidate_fullname: `<strong>${candidatesName}</strong>`,
      recruiter_email: job?.owner?.email || '',
      recruiter_fullname: job?.owner?.fullName || '',
      job_title: `<strong>${job?.title}</strong>`,
      job_link: `<a href="${job_link}">${t('candidates:candidateSendMail:viewJobDetail')}</a>`,
      company_name: `<strong>${user?.currentTenant?.name}</strong>`,
      career_page: `<a href="${canonical_url}">${user?.currentTenant?.name}</a>`
    }
  }, [canonical_url, emails, user?.currentTenant?.name, applicants, bulkValuesKanban])

  const handleSubmitForm = (data: IEmailForm) => {
    if (isMissingEmailApplicant) {
      openAlert({
        isPreventAutoFocusDialog: false,
        className: 'w-[480px]',
        title: `${t('candidates:rejectCandidate:candidatesLackEmail:title')}`,
        description: `${t('candidates:rejectCandidate:candidatesLackEmail:description')}`,
        actions: [
          {
            label: `${t('button:cancel')}`,
            type: 'secondary',
            size: 'sm'
          },
          {
            label: `${t('button:disqualify')}`,
            size: 'sm',
            onClick: () => {
              onSubmit(data)
            }
          }
        ]
      })

      return Promise.resolve()
    }
    return onSubmit(data)
  }

  return emails !== undefined ? (
    <>
      <Button
        size="sm"
        iconMenus="Slash"
        type="secondary"
        onClick={() => {
          setOpenDialog(true)
        }}
        label={`${t('button:disqualify')}`}
      />
      <Dialog open={openDialog} size="md" onOpenChange={setOpenDialog} isPreventAutoFocusDialog={true} label={`${t('label:disqualifyCandidate')}`}>
        <DynamicImportForm
          isShowDebug={false}
          id="reject-reasons-form"
          className="w-full"
          schema={schemaRejectApplicant(t)}
          onSubmit={handleSubmitForm}
        >
          {({ formState, control, setValue, setError, clearErrors, getValues }) => {
            return (
              <SubmitRejectCandidateForm
                control={control}
                setValue={setValue}
                formState={formState}
                setOpen={setOpenDialog}
                defaultTemplateEmail={defaultTemplateEmail}
                placeholderValue={placeholderValue}
                onSubmitUpdateEmailProfile={() => Promise.resolve()}
                isLoadingUpdateEmailProfile={false}
                email={emails}
                setError={setError}
                clearErrors={clearErrors}
                showFields={['subject', 'cc', 'bcc', 'emailTemplate', 'htmlBody']}
                hidingSendMail={emails.length === 0}
                hidingSendMailNotifyMessage={`${t('candidates:rejectCandidate:selectedCandidatesDoNotHaveEmail')}`}
              />
            )
          }}
        </DynamicImportForm>
      </Dialog>
    </>
  ) : null
}

export default RejectApplicantsSelector
