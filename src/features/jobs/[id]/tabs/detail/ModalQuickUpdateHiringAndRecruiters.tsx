'use client'

import type { FC } from 'react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useSubscription } from 'urql'

import pathConfiguration from 'src/configuration/path'
import useEnumsData from 'src/hooks/data/use-enums-data'
import type { ILogoAndAvatarVariants } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { AsyncSingleSearchWithSelect } from '~/core/ui/AsyncSingleSearchWithSelect'
import { AvatarGroup } from '~/core/ui/AvatarGroup'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import type { ISelectOption } from '~/core/ui/Select'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { limitedMemberCanAction } from '~/core/utilities/permission'

import type { IJobDetail, IMemberInfo } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import QueryTenantAdmins from '~/lib/features/jobs/graphql/query-tenant-admin'
import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import usePromiseOwnerOptions from '~/lib/features/settings/members/hooks/use-promise-owner-options'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import EmptyJobRecruiterView from './components/ModalHiringTeam/EmptyJobRecruiterView'
import MemberInHiringTeamCard from './MemberInHiringTeamCard'

const ModalQuickUpdateHiringAndRecruiters: FC<{
  onReloadListMemberPending: () => void
  jobRecruiters?: { id: string; responsibility: string; user: IMemberInfo }[]
  membersPendingOfJob?: {
    id: string
    email: string
    roleId: number
    jobResponsibility: string
    role: { code: string }
  }[]
  membersRecommendedJob?: {
    id: string
    email: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
  }[]
  isLoadingAddHiringMember2Job?: boolean
  isLoadingDeleteHiringMemberOfJob?: boolean
  isLoadingChangeRoleOfJob?: boolean
  open: boolean
  onToggleInviteMember: (flag: boolean) => void
  setOpen: (open: boolean) => void
  jobDetail?: IJobDetail
  changeMemberRoleOfJobCallback: (jobRecruiterId: number, jobRecruiterResponsibility: string) => Promise<void>
  changeMemberRoleJobOfPendingCallback: (jobRecruiterId: number, jobRecruiterResponsibility: string) => Promise<void>
  addHiringMemberOfJobCallback: (teamMemberIds: number[], userName: string | undefined) => Promise<boolean>
  deleteHiringMemberOfJobCallback: (jobRecruiterId: number, userName: string | undefined) => Promise<boolean>
  triggerRecommendedMembersJob: () => void
}> = ({
  open,
  setOpen,
  jobDetail,
  addHiringMemberOfJobCallback,
  changeMemberRoleOfJobCallback,
  changeMemberRoleJobOfPendingCallback,
  deleteHiringMemberOfJobCallback,
  onToggleInviteMember,
  isLoadingAddHiringMember2Job,
  isLoadingDeleteHiringMemberOfJob,
  isLoadingChangeRoleOfJob,
  membersPendingOfJob,
  jobRecruiters = [],
  onReloadListMemberPending,
  membersRecommendedJob = [],
  triggerRecommendedMembersJob
}) => {
  const { t, i18n } = useTranslation()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const [disableMemberSelector, setDisableMemberSelector] = useState<boolean>(false)
  const [currentMember, setCurrentMember] = useState<ISelectOption>()
  const [triggerUpdateTeamMenbers, setTriggerUpdateTeamMenbers] = useState<boolean>(false)
  const jobRecruiterResponsibility = useEnumsData({
    enumType: isCompanyKind ? 'HfAgencyJobRecruiterResponsibility' : 'JobRecruiterResponsibility',
    locale: i18n.language
  })
  const { isFeatureFlagOn } = useSubscriptionPlan()
  const currentRole = useBoundStore(state => state.currentRole)
  const { trigger: triggerFetchListAdmin, data: adminList } = useQueryGraphQL({
    query: QueryTenantAdmins,
    variables: {
      onlyAdmins: true
    },
    shouldPause: true
  })
  const { actionUser: actionUserSetting } = usePermissionSetting()

  const { promiseMembersOwnerOptions: fetchTeamMembers, setFilter: setFilterTeamMembers } = usePromiseOwnerOptions()

  const onDeleteHiringMemberOfJob = (jobRecruiterId: number, userName: string) => {
    deleteHiringMemberOfJobCallback(jobRecruiterId, userName).then(() => {
      setCurrentMember(undefined)
      triggerRecommendedMembersJob()
    })
  }

  const onAddHiringMember2Job = () => {
    setDisableMemberSelector(true)
    addHiringMemberOfJobCallback([Number(currentMember?.value)], currentMember?.supportingObj?.name).then(() => {
      setCurrentMember(undefined)
      setTimeout(() => {
        setDisableMemberSelector(false)
      }, 300)
    })
  }

  const onAddRecommendedUser = (recommendedValue: ISelectOption) => {
    return addHiringMemberOfJobCallback([Number(recommendedValue?.value)], recommendedValue?.supportingObj?.name).then(() => {
      setCurrentMember(undefined)
    })
  }

  const onChangeRoleOfJob = (jobRecruiterId: number, jobRecruiterResponsibility: string) => {
    changeMemberRoleOfJobCallback(jobRecruiterId, jobRecruiterResponsibility)
  }

  const dataAdditionalUsers = jobRecruiters.map(item => Number(item.user.id.toString()))

  useEffect(() => {
    triggerFetchListAdmin()
    if (triggerRecommendedMembersJob && open) {
      triggerRecommendedMembersJob()
    }
  }, [open])

  useEffect(() => {
    setFilterTeamMembers({
      hiringMemberIds: (jobRecruiters || []).map(jr => Number(jr.user.id))
    })
    setTriggerUpdateTeamMenbers(isUpdated => !isUpdated)
  }, [jobRecruiters?.length])

  return (
    <Dialog
      className="relative min-w-[480px]"
      open={open}
      isPreventAutoFocusDialog
      label={`${t('job:detail:hiring_team:title')}`}
      description={
        <>
          {t('job:detail:hiring_team:manage_members_access_this_job')}
          <TextButton
            className="contents"
            classNameText="font-normal"
            label={`${t('common:learn_more')}`}
            onClick={() => {
              window.open(pathConfiguration.helpCenter.hiringTeam, '_blank')
            }}
          />
        </>
      }
      onOpenChange={() => setOpen(false)}
    >
      <div>
        <div className="row mb-[6px] flex items-center">
          <p className="flex-1 text-sm font-medium text-gray-700 uppercase">{t('label:members')}</p>
          <If condition={actionUserSetting}>
            <TextButton underline={false} onClick={() => onToggleInviteMember(true)} iconMenus="Plus" label={`${t('button:inviteMember')}`} />
          </If>
        </div>
        <div className="row flex">
          <AsyncSingleSearchWithSelect
            isDisabled={disableMemberSelector}
            className="flex-1"
            promiseOptions={fetchTeamMembers}
            forceUpdatePromiseOptions={triggerUpdateTeamMenbers}
            isSearchable
            size="sm"
            onChange={newValue => setCurrentMember(newValue as ISelectOption)}
            value={currentMember}
            placeholder={`${t('label:placeholder:select')}`}
            configSelectOption={{
              supportingText: ['name', 'description'],
              avatar: true
            }}
            classNameOverride={{
              loadingMessage: `${t('label:loading')}`,
              noOptionsMessage: `${t('label:noOptions')}`
            }}
          />
          <Button
            isLoading={isLoadingAddHiringMember2Job}
            isDisabled={!currentMember || isLoadingAddHiringMember2Job}
            onClick={onAddHiringMember2Job}
            label={`${t('button:add')}`}
            className="ml-3"
            size="sm"
          />
        </div>
        <div>
          {jobRecruiters.map(({ user, id, responsibility }, index) => {
            let rolesJob = limitedMemberCanAction(user?.roles?.[0]?.code)
              ? jobRecruiterResponsibility.filter((jr: ISelectOption) => jr.value !== 'recruiter')
              : jobRecruiterResponsibility
            if (!isCompanyKind && !isFeatureFlagOn(PLAN_FEATURE_KEYS.company)) {
              rolesJob = rolesJob.filter((jr: ISelectOption) => jr.value !== 'account_manager')
            }

            return (
              <MemberInHiringTeamCard
                jobRecruiterResponsibility={rolesJob as ISelectOption[]}
                responsibility={responsibility}
                id={user.id.toString()}
                name={user.fullName}
                email={user.email}
                defaultColour={user.defaultColour}
                avatarVariants={user.avatarVariants}
                onDeleteHiringMemberOfJob={onDeleteHiringMemberOfJob}
                onChangeRoleOfJob={onChangeRoleOfJob}
                jobRecruiterId={Number(id)}
                typeUser="member"
                deleting={isLoadingDeleteHiringMemberOfJob}
                isLoadingChangeRoleOfJob={isLoadingChangeRoleOfJob}
                key={index}
              />
            )
          })}
          {membersPendingOfJob?.map((member, index) => {
            let rolesJob = limitedMemberCanAction(member?.role?.code)
              ? jobRecruiterResponsibility.filter((jr: ISelectOption) => jr.value !== 'recruiter')
              : jobRecruiterResponsibility
            if (!isCompanyKind && !isFeatureFlagOn(PLAN_FEATURE_KEYS.company)) {
              rolesJob = rolesJob.filter((jr: ISelectOption) => jr.value !== 'account_manager')
            }
            return (
              <MemberInHiringTeamCard
                jobRecruiterResponsibility={rolesJob as ISelectOption[]}
                jobRecruiterId={Number(member.id)}
                responsibility={member.jobResponsibility}
                id={member.id.toString()}
                email={member.email}
                roleId={member.roleId}
                onReloadListMemberPending={onReloadListMemberPending}
                onDeleteHiringMemberOfJob={onDeleteHiringMemberOfJob}
                typeUser="pending"
                deleting={isLoadingDeleteHiringMemberOfJob}
                onChangeRoleOfJob={changeMemberRoleJobOfPendingCallback}
                isLoadingChangeRoleOfJob={isLoadingChangeRoleOfJob}
                key={index}
              />
            )
          })}
          {jobRecruiters?.length === 0 && membersPendingOfJob?.length === 0 ? <EmptyJobRecruiterView /> : null}
        </div>
        {membersRecommendedJob.filter(fi => !dataAdditionalUsers.includes(Number(fi.id))).length > 0 && (
          <div className="row mt-6 flex items-center">
            <p className="text-sm font-medium text-gray-700 uppercase">{t('job:detail:hiring_team:recommendedUsers')}</p>
            <Tooltip classNameConfig={{ content: 'max-w-[320px]' }} content={`${t('tooltip:userMatchingWithJobDepartment')}`}>
              <IconWrapper size={14} className="ml-[6px] text-gray-400" name="HelpCircle" />
            </Tooltip>
          </div>
        )}
        {membersRecommendedJob
          .filter(fi => !dataAdditionalUsers.includes(Number(fi.id)))
          ?.map((member, index) => (
            <MemberInHiringTeamCard
              jobRecruiterResponsibility={jobRecruiterResponsibility as ISelectOption[]}
              id={member.id.toString()}
              name={member.fullName}
              email={member.email}
              defaultColour={member.defaultColour}
              avatarVariants={member.avatarVariants}
              onDeleteHiringMemberOfJob={onDeleteHiringMemberOfJob}
              typeUser="recommended"
              deleting={isLoadingDeleteHiringMemberOfJob}
              key={index}
              onAddRecommendedUser={onAddRecommendedUser}
            />
          ))}
        <div className="h-[40px]" />
      </div>
      <div className="absolute bottom-0 left-0 flex h-[40px] w-[480px] flex-row items-center rounded-b-lg bg-gray-50 px-6 py-[10px]">
        <div className="flex flex-1 flex-row">
          <AvatarGroup
            size="xs"
            maxUser={3}
            toolTipPosition="left"
            toolTipPositionAvatarCount="left"
            source={(adminList?.tenantMembers?.collection || []).map(item => ({
              id: item.id,
              alt: item.fullName || item.email,
              src: item.avatarVariants?.thumb?.url,
              defaultColour: item.defaultColour,
              tooltip: `${item.fullName || item.email}`
            }))}
          />
          <TypographyText className="ml-1.5 text-sm font-normal text-gray-700">{t('label:canAccessThisJob')}</TypographyText>
        </div>
      </div>
    </Dialog>
  )
}

export default ModalQuickUpdateHiringAndRecruiters
