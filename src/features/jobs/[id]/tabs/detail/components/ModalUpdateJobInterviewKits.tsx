'use client'

import type { Dispatch, SetStateAction } from 'react'
import { useCallback, useEffect } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IColorBadgeType } from '~/core/ui/Badge'
import { But<PERSON> } from '~/core/ui/Button'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import type { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { NativeSelect } from '~/core/ui/NativeSelect'
import type { ISelectOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import type { IStageType, IStageTypes } from '~/lib/features/jobs/types'
import { JOB_COLOR_STAGE_NAME } from '~/lib/features/jobs/utilities/enum'
import QueryJobInterviewKitsStagesList from '~/lib/features/settings/interview-kits/graphql/query-job-interview-kits-stages-list'
import QueryUpdateJobInterviewKits from '~/lib/features/settings/interview-kits/graphql/submit-update-job-interview-kits'
import { useQueryJobInterviewKitsStagesList } from '~/lib/features/settings/interview-kits/hooks/use-query-job-interview-kits-stages-list'
import { mappingSubmitDataJobInterviewKits } from '~/lib/features/settings/interview-kits/mapping/job-interview-kits'
import schemaJobInterviewKitsForm from '~/lib/features/settings/interview-kits/schema/job-interview-kits-form'
import type { IInterviewKitTemplateForm } from '~/lib/features/settings/interview-kits/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'

export const ModalUpdateJobInterviewKits = ({
  defaultValue,
  jobId,
  stageTypes,
  setOpenJobInterviewKitsModal,
  callback
}: {
  defaultValue?: IInterviewKitTemplateForm
  jobId?: number
  stageTypes?: IStageTypes
  setOpenJobInterviewKitsModal: Dispatch<SetStateAction<boolean>>
  callback?: () => void
}) => {
  const { t } = useTranslation()
  const { data: interviewKitsStagesList, trigger: triggerFetchStagesList } = useQueryJobInterviewKitsStagesList({
    query: QueryJobInterviewKitsStagesList,
    variables: {
      page: 1,
      limit: configuration.defaultPageSize,
      jobId: Number(jobId)
    },
    shouldPause: true
  })

  useEffect(() => {
    triggerFetchStagesList()
  }, [])

  const { trigger, isLoading } = useSubmitCommon(QueryUpdateJobInterviewKits)
  const { setToast } = useToastStore()

  const onFinishCallback = useCallback(
    async (data: { ikitTemplateIds: ISelectOption[]; jobStageIds: ISelectOption }, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      trigger({
        ...mappingSubmitDataJobInterviewKits(data),
        id: Number(data?.ikitTemplateIds?.[0]?.value)
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.interviewKits,
            formAction,
            setToast
          })
        }
        const { jobIkitsUpdate } = result.data
        if (jobIkitsUpdate?.jobIkit) {
          formAction.reset()
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:interview_kit_updated')}`
          })
          callback && callback()
          setOpenJobInterviewKitsModal(false)
        }

        return
      })
    },
    [isLoading, trigger, setToast, callback, setOpenJobInterviewKitsModal]
  )

  return (
    <DynamicImportForm
      id="update-job-interview-kits-form"
      className="w-full"
      schema={schemaJobInterviewKitsForm(t)}
      onSubmit={onFinishCallback}
      defaultValue={{
        ikitTemplateIds: [{ value: defaultValue?.id }],
        jobStageIds: defaultValue?.jobStageIds
      }}
    >
      {({ formState, control, submit }) => {
        return (
          <>
            <Controller
              control={control}
              name="jobStageIds"
              render={({ field: { onChange, value } }) => (
                <FormControlItem
                  labelRequired
                  destructive={formState.errors && !!formState.errors.jobStageIds}
                  destructiveText={formState.errors && formState.errors.jobStageIds?.message}
                  label={`${t('label:add_to_stage')}`}
                >
                  <NativeSelect
                    size="sm"
                    onChange={onChange}
                    placeholder={`${t('label:placeholder:select')}`}
                    value={value}
                    destructive={formState.errors && !!formState.errors.jobStageIds}
                    isMultiShowType="dot-leading"
                    configSelectOption={{
                      dot: true,
                      supportingText: ['name']
                    }}
                    options={interviewKitsStagesList.map(item => {
                      return {
                        value: String(item.id),
                        dot: JOB_COLOR_STAGE_NAME(
                          String((stageTypes || []).filter((s: IStageType) => String(s.id) === String(item?.stageTypeId))?.[0]?.colorClassName)
                        ) as IColorBadgeType,
                        supportingObj: {
                          name: item.stageLabel
                        }
                      }
                    })}
                    classNameOverride={{
                      loadingMessage: `${t('label:loading')}`,
                      noOptionsMessage: `${t('label:noOptions')}`
                    }}
                  />
                </FormControlItem>
              )}
            />

            <div className="flex items-center justify-end pt-6">
              <Button
                type="secondary"
                className="mr-3"
                size="sm"
                onClick={() => setOpenJobInterviewKitsModal(false)}
                label={`${t('button:cancel')}`}
              />
              <Button onClick={submit} size="sm" htmlType="button" label={`${t('button:save')}`} />
            </div>
          </>
        )
      }}
    </DynamicImportForm>
  )
}

export default ModalUpdateJobInterviewKits
