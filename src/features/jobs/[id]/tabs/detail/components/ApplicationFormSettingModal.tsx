import type { FC } from 'react'
import { useTranslation } from 'react-i18next'

import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import MutationUpdateApplicationFieldsJob from '~/lib/features/jobs/graphql/mutation-update-application-fields-job'
import useApplicationFieldsHook from '~/lib/features/jobs/hooks/use-application-fields-hook'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'

import ApplicationFieldSettingRow from './ApplicationFormSetting/ApplicationFieldSettingRow'
import DragDropList from './ApplicationFormSetting/DragDropList'

const ApplicationFormSettingModal: FC<{
  open: boolean
  setOpenDialog: (open: boolean) => void
  jobId: number
}> = ({ open, setOpenDialog, jobId }) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()

  const { applicationFields, setApplicationFields } = useApplicationFieldsHook({
    jobId
  })

  const { trigger: triggerUpdateApplicationFields, isLoading: updatingApplicationFields } = useSubmitCommon(MutationUpdateApplicationFieldsJob)

  const onUpdateApplicationFields = () => {
    const reorderIndexApplicationFields = (applicationFields || []).map((applicationField, i) => ({
      ...applicationField,
      required: !!applicationField.required,
      index: i + 1
    }))

    return triggerUpdateApplicationFields({
      jobId,
      applicationFields: reorderIndexApplicationFields
    }).then(result => {
      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast
        })
      }

      const { jobsUpdateApplicationFields } = result.data
      if (jobsUpdateApplicationFields) {
        setToast({
          open: true,
          type: 'success',
          title: `${t('notification:jobs:application_form_updated')}`
        })
        setOpenDialog(false)
      }
      return
    })
  }

  return applicationFields ? (
    <Dialog
      size="md"
      open={open}
      isPreventAutoFocusDialog
      label={`${t('job:detail:application_form:title')}`}
      description={`${t('job:detail:application_form:description_modal')}`}
      onOpenChange={setOpenDialog}
    >
      <div className="-mt-2">
        <DragDropList
          fields={applicationFields}
          updateFields={setApplicationFields}
          renderFieldView={(field, onFieldChange) => (
            <div className="mt-2">
              <ApplicationFieldSettingRow field={field} onFieldChange={onFieldChange} />
            </div>
          )}
        />
      </div>
      <div className={'flex items-center justify-end gap-x-3 pt-6'}>
        <Button
          label={`${t('button:cancel')}`}
          isLoading={updatingApplicationFields}
          isDisabled={updatingApplicationFields}
          size="sm"
          type="secondary"
          onClick={() => setOpenDialog(false)}
        />
        <Button
          size="sm"
          isLoading={updatingApplicationFields}
          isDisabled={updatingApplicationFields}
          label={`${t('button:save')}`}
          onClick={() => onUpdateApplicationFields()}
        />
      </div>
    </Dialog>
  ) : null
}

export default ApplicationFormSettingModal
