import type { FC } from 'react'
import type { Control, FieldValues } from 'react-hook-form'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import type { ISelectOption } from '~/core/@types/global'
import { Button } from '~/core/ui/Button'
import type { FormStateType } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { Input } from '~/core/ui/Input'
import { NativeSelect } from '~/core/ui/NativeSelect'
import { Radio } from '~/core/ui/Radio'

import type { IJobForm } from '~/lib/features/jobs/types'
import { REFERRAL_REWARD_MONEY_VALUE, REFERRAL_REWARD_OTHER_VALUE, SOURCE_REFERRAL_REWARD } from '~/lib/features/jobs/utilities/enum'
import useBoundStore from '~/lib/store'

const ReferralRewardForm: FC<{
  formState: FormStateType
  control: Control<FieldValues, any>
  onOpenChange: (open: boolean) => void
  onCancelAction?: () => void
  isLoading?: boolean
  isEdit?: boolean
  data?: IJobForm
}> = ({ control, formState, onOpenChange, onCancelAction, isLoading, isEdit, data }) => {
  const { t, i18n } = useTranslation()
  const { user } = useBoundStore()
  const jobCurrency = useEnumsData({
    enumType: 'JobCurrency',
    locale: i18n.language
  })

  return (
    <>
      <Controller
        control={control}
        name="referralRewardType"
        defaultValue=""
        render={({ field: { onChange, value } }) => (
          <FormControlItem
            destructive={formState.errors && !!formState.errors.referralRewardType}
            destructiveText={formState.errors && (formState.errors.referralRewardType?.message as string)}
          >
            <Radio
              orientation="horizontal"
              size="sm"
              source={SOURCE_REFERRAL_REWARD.map(item => ({
                ...item,
                text: `${t(`job:${item.value}`)}`
              }))}
              onValueChange={onChange}
              value={value}
              radioItemClassName="ml-12! first:ml-0!"
            />
          </FormControlItem>
        )}
      />
      <div className={`mt-4 ${data?.referralRewardType === REFERRAL_REWARD_MONEY_VALUE ? 'flex' : 'grid grid-cols-[150px_266px]'} space-x-4`}>
        <div className="w-full">
          <Controller
            control={control}
            name="rewardAmount"
            defaultValue=""
            render={({ field: { onChange, value } }) => (
              <FormControlItem
                destructive={formState.errors && !!formState.errors.rewardAmount}
                destructiveText={formState.errors && (formState.errors.rewardAmount?.message as string)}
              >
                <Input
                  className="w-full"
                  autoFocus
                  inputType="number"
                  placeholder={`${t('job:detail:referrals:form:amount')}`}
                  size="sm"
                  onChange={onChange}
                  value={value}
                  min={0}
                  destructive={formState.errors && !!formState.errors?.rewardAmount}
                />
              </FormControlItem>
            )}
          />
        </div>
        <div className="w-full">
          {data?.referralRewardType === REFERRAL_REWARD_MONEY_VALUE && (
            <Controller
              control={control}
              name="rewardCurrency"
              defaultValue={''}
              render={({ field: { onChange, value } }) => {
                const findCurrency = jobCurrency?.find((item: ISelectOption) => item.value === (value || user?.currentTenant?.currency))
                const formatCurrentCurrency = findCurrency
                  ? {
                      value: String(value || user?.currentTenant?.currency),
                      supportingObj: {
                        name: findCurrency?.supportingObj?.name || ''
                      }
                    }
                  : undefined

                return (
                  <FormControlItem
                    destructive={formState.errors && !!formState.errors.rewardCurrency}
                    destructiveText={formState.errors && (formState.errors.rewardCurrency?.message as string)}
                  >
                    <NativeSelect
                      isClearable={false}
                      size="sm"
                      onChange={newValue => onChange((newValue as ISelectOption)?.value || '')}
                      placeholder={`${t('label:placeholder:select')}`}
                      value={formatCurrentCurrency}
                      options={jobCurrency}
                      destructive={formState.errors && !!formState.errors.rewardCurrency}
                      classNameOverride={{
                        loadingMessage: `${t('label:loading')}`,
                        noOptionsMessage: `${t('label:noOptions')}`
                      }}
                    />
                  </FormControlItem>
                )
              }}
            />
          )}
          {data?.referralRewardType === REFERRAL_REWARD_OTHER_VALUE && (
            <Controller
              control={control}
              name="rewardGift"
              defaultValue=""
              render={({ field: { onChange, value } }) => {
                return (
                  <FormControlItem
                    destructive={formState.errors && !!formState.errors.rewardGift}
                    destructiveText={formState.errors && (formState.errors.rewardGift?.message as string)}
                  >
                    <Input
                      className="w-full"
                      placeholder={`${t('job:detail:referrals:form:reward_placeholder')}`}
                      size="sm"
                      onChange={onChange}
                      value={value}
                      destructive={formState.errors && !!formState.errors?.rewardGift}
                    />
                  </FormControlItem>
                )
              }}
            />
          )}
        </div>
      </div>
      <div className="mt-6 flex items-center justify-end">
        <Button
          type="secondary"
          className="mr-3"
          htmlType="button"
          onClick={() => {
            onOpenChange(false)
            onCancelAction && onCancelAction()
          }}
          size="sm"
          label={`${t('settings:departments:addDepartmentModal:actions:cancel')}`}
        />

        <Button
          isDisabled={isLoading}
          isLoading={isLoading}
          htmlType="submit"
          size="sm"
          label={`${isEdit ? t('settings:departments:editDepartmentModal:actions:save') : t('settings:departments:addDepartmentModal:actions:add')}`}
        />
      </div>
    </>
  )
}

export default ReferralRewardForm
