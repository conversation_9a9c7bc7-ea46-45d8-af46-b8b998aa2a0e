import type { FC } from 'react'
import { useCallback, useMemo, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import { openAlert } from '~/core/ui/AlertDialog'
import { Badge } from '~/core/ui/Badge'
import { Dialog } from '~/core/ui/Dialog'
import If from '~/core/ui/If'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Toggle } from '~/core/ui/Toggle'
import { cn } from '~/core/ui/utils'
import { limitedMemberCanAction } from '~/core/utilities/permission'

import { checkPermissionForActions } from '~/lib/features/candidates/utilities'
import type { IJobDetail } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import type { IJobForm } from '~/lib/features/jobs/types'
import { JOB_STATUS_ENUM, REFERRAL_REWARD_MONEY_VALUE } from '~/lib/features/jobs/utilities/enum'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import {
  convertMoney,
  convertMoneyNotRounded,
  DEFAULT_REFERRAL_PORTAL,
  REFERRAL_PORTAL_VALUES,
  SOURCE_REFERRAL_PORTAL,
  SOURCE_REFERRAL_PORTAL_JOB_DETAIL
} from '~/lib/features/settings/referrals/utilities/enum'
import useBoundStore from '~/lib/store'

import AddReferralReward from './AddReferralReward'
import EditReferralReward from './EditReferralReward'

const ReferralReward: FC<{
  jobDetail?: IJobDetail
  updateReferralReward: (data: IJobForm) => Promise<void>
}> = ({ jobDetail, updateReferralReward }) => {
  const { t } = useTranslation()
  const [openAddReferralReward, setOpenAddReferralReward] = useState(false)
  const [openEditReferralReward, setOpenEditReferralReward] = useState(false)
  const [isCheckOfferReward, setIsCheckOfferReward] = useState<boolean>(!!jobDetail?.jobsShow?.enablingReward)
  const user = useBoundStore(state => state.user)
  const currentRole = useBoundStore(state => state.currentRole)
  const { canAccessFullPermissionJob } = usePermissionJob()

  const referralPortalSetting = useMemo(() => {
    const portalValues = (jobDetail?.jobsShow?.tenant?.tenantReferralSettings?.referral_portal || {}) as { [key: string]: boolean }
    const portalReferral = Object.keys(portalValues).find(key => portalValues?.[key]) || DEFAULT_REFERRAL_PORTAL

    return {
      value: portalReferral,
      info: Object.values(SOURCE_REFERRAL_PORTAL_JOB_DETAIL)?.find(portal => portal?.value === portalReferral)
    }
  }, [jobDetail?.jobsShow?.tenant?.tenantReferralSettings])

  const isFullAction = checkPermissionForActions({
    user,
    currentRole,
    jobDetail: {
      owner: jobDetail?.jobsShow?.owner,
      jobRecruiters: jobDetail?.jobsShow?.jobRecruiters
    },
    ignoreOwner: true,
    permission: canAccessFullPermissionJob
  })
  const isDisabledAction = !isFullAction || limitedMemberCanAction(currentRole?.code) || jobDetail?.jobsShow?.status === JOB_STATUS_ENUM['archived']

  const onConfirmChangeStatus = () =>
    openAlert({
      isPreventAutoFocusDialog: false,
      className: 'w-[480px]',
      title: `${t('job:detail:referrals:confirm_remove_reward:title')}`,
      description: `${t('job:detail:referrals:confirm_remove_reward:description')}`,
      actions: [
        {
          label: `${t('button:cancel')}`,
          type: 'secondary',
          size: 'sm',
          onClick: () => setIsCheckOfferReward(true)
        },
        {
          isCallAPI: true,
          label: `${t('job:detail:referrals:confirm_remove_reward:btn_confirm')}`,
          type: 'destructive',
          size: 'sm',
          onClick: async () => {
            await updateReferralReward({ enablingReward: false })
            setOpenEditReferralReward(false)
          }
        }
      ]
    })

  const onCancelAddOfferReward = useCallback(() => {
    setIsCheckOfferReward(false)
  }, [])
  return (
    <div className="px-6 py-4">
      <div className="mb-0.5 flex items-center justify-between">
        <TypographyText className="text-lg font-medium text-gray-900">{t(`settings:referrals:${referralPortalSetting?.info?.text}`)}</TypographyText>

        <Toggle
          isDisabled={isDisabledAction}
          isChecked={jobDetail?.jobsShow?.jobReferable}
          name="view-switch"
          onCheckedChange={checked => {
            updateReferralReward({ jobReferable: checked })
          }}
          size="md"
        />
      </div>
      <TypographyText className="text-sm text-gray-600">
        <Trans i18nKey={`settings:referrals:${referralPortalSetting?.info?.description}`}>
          <span className="font-medium text-gray-900" />
        </Trans>
      </TypographyText>
      {jobDetail?.jobsShow?.jobReferable && referralPortalSetting?.value !== REFERRAL_PORTAL_VALUES.job_only && (
        <>
          <If condition={isFullAction}>
            <div className="mt-4">
              <Toggle
                isChecked={isCheckOfferReward}
                name="view-switch"
                isDisabled={isDisabledAction}
                onCheckedChange={checked => {
                  setIsCheckOfferReward(checked)
                  checked
                    ? jobDetail?.jobsShow?.rewardCurrency || jobDetail?.jobsShow?.rewardGift
                      ? updateReferralReward({ enablingReward: true })
                      : setOpenAddReferralReward(true)
                    : onConfirmChangeStatus()
                }}
                size="sm"
                text={`${t('job:detail:referrals:reward_job')}`}
              />
            </div>
          </If>
          <If condition={jobDetail?.jobsShow?.enablingReward}>
            <div className={cn(`flex`, !isFullAction ? 'mt-4' : 'mt-2 ml-[42px]')}>
              <Badge color="blue" type="iconLeading" icon="Gift" size="lg" classNameText="font-normal" classNameIcon="font-normal" radius="rounded">
                {convertMoneyNotRounded(jobDetail?.jobsShow?.rewardAmount)}{' '}
                {jobDetail?.jobsShow?.referralRewardType === REFERRAL_REWARD_MONEY_VALUE
                  ? jobDetail?.jobsShow?.rewardCurrency
                  : jobDetail?.jobsShow?.rewardGift}
                /{`${t('job:detail:referrals:badge_reward_description')}`}
              </Badge>
              <If condition={isFullAction}>
                <TextButton
                  onClick={() => setOpenEditReferralReward(true)}
                  icon="leading"
                  iconMenus="Edit3"
                  className="ml-2"
                  isDisabled={isDisabledAction}
                  size="md"
                  label={`${t('job:detail:referrals:button_edit')}`}
                />
              </If>
            </div>
          </If>
          <If condition={!jobDetail?.jobsShow?.enablingReward && isFullAction}>
            <div className="mt-4 flex">
              <Badge color="gray" type="iconLeading" icon="Gift" size="lg" classNameText="font-normal" classNameIcon="font-normal" radius="rounded">
                {`${t('job:detail:referrals:no_reward')}`}
              </Badge>
            </div>
          </If>
        </>
      )}
      <Dialog
        className="min-w-[480px]"
        description={`${t('job:detail:referrals:form:sub_title')}`}
        open={openAddReferralReward}
        isPreventAutoFocusDialog
        label={`${t('job:detail:referrals:form:title_add')}`}
        onOpenChange={open => {
          setOpenAddReferralReward(open)
          onCancelAddOfferReward()
        }}
      >
        <AddReferralReward
          onOpenChange={setOpenAddReferralReward}
          updateReferralReward={updateReferralReward}
          onCancelAction={onCancelAddOfferReward}
        />
      </Dialog>
      <Dialog
        className="min-w-[480px]"
        description={`${t('job:detail:referrals:form:sub_title')}`}
        open={openEditReferralReward}
        isPreventAutoFocusDialog
        label={`${t('job:detail:referrals:form:title_edit')}`}
        onOpenChange={setOpenEditReferralReward}
      >
        <EditReferralReward onOpenChange={setOpenEditReferralReward} updateReferralReward={updateReferralReward} data={jobDetail} />
      </Dialog>
    </div>
  )
}

export default ReferralReward
