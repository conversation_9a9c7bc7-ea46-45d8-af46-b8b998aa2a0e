import type { FC } from 'react'
import { useCallback } from 'react'
import type { Control, FieldValues } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import type { IFormAction } from '~/core/@types/global'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'

import schemaReferralRewardForm from '~/lib/features/jobs/schema/job-referral-reward'
import type { IJobForm } from '~/lib/features/jobs/types'
import { REFERRAL_REWARD_MONEY_VALUE, REFERRAL_REWARD_OTHER_VALUE } from '~/lib/features/jobs/utilities/enum'
import QueryAddDepartmentMutation from '~/lib/features/settings/departments/graphql/submit-add-department-mutation'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import ReferralRewardForm from './ReferralRewardForm'

const AddReferralReward: FC<{
  onOpenChange: (open: boolean) => void
  updateReferralReward: (data: IJobForm) => Promise<void>
  onCancelAction?: () => void
}> = ({ onOpenChange, updateReferralReward, onCancelAction }) => {
  const { trigger, isLoading } = useSubmitCommon(QueryAddDepartmentMutation)
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const user = useBoundStore(state => state.user)

  const onFinishCallback = useCallback(
    async (data: IJobForm, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      return updateReferralReward({
        ...data,
        rewardAmount: Number(data?.rewardAmount),
        enablingReward: true,
        rewardCurrency: data.referralRewardType === REFERRAL_REWARD_MONEY_VALUE ? data.rewardCurrency || user?.currentTenant?.currency : undefined,
        rewardGift: data.referralRewardType === REFERRAL_REWARD_OTHER_VALUE ? data.rewardGift : undefined
      })
        .then(() =>
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:referral_rewards_added')}`
          })
        )
        .then(() => onOpenChange(false))
    },
    [isLoading, trigger, setToast, t, onOpenChange, user]
  )

  return !user ? null : (
    <DynamicImportForm
      id="add-referral-reward-form"
      className="w-full"
      schema={schemaReferralRewardForm(t)}
      onSubmit={onFinishCallback}
      defaultValue={{
        referralRewardType: REFERRAL_REWARD_MONEY_VALUE,
        rewardCurrency: user?.currentTenant?.currency
      }}
      noUseSubmit
    >
      {({ formState, control, trigger, getValues }) => {
        const valueReferrals = getValues()
        return (
          <ReferralRewardForm
            control={control as unknown as Control<FieldValues, any>}
            formState={formState}
            onOpenChange={onOpenChange}
            onCancelAction={onCancelAction}
            isLoading={isLoading}
            data={valueReferrals}
          />
        )
      }}
    </DynamicImportForm>
  )
}

export default AddReferralReward
