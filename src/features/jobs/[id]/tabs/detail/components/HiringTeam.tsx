import type { FC } from 'react'
import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { Avatar } from '~/core/ui/Avatar'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'

import type { IMemberInfo } from '~/lib/features/jobs/graphql/query-job-detail-mini'

const HiringTeam: FC<{
  title: string
  showCountNumber?: boolean
  isShowCollapse?: boolean
  helpInfoTooltip?: string
  members?: IMemberInfo[]
  isShowDash?: boolean
}> = ({ title, showCountNumber = false, isShowCollapse = false, helpInfoTooltip = '', members = [], isShowDash }) => {
  const { t } = useTranslation()
  const [isCollapse, setIsCollapse] = useState<boolean>(false)

  const toggleCollapse = useCallback(() => {
    setIsCollapse(!isCollapse)
  }, [isCollapse])

  if (isShowDash) {
    return (
      <div className="mt-4">
        <div className="mb-1 flex items-center justify-between">
          <div className="flex items-center">
            <TypographyText className="font-sm mr-0.5 text-sm font-medium text-gray-700 uppercase">{title}</TypographyText>
            {helpInfoTooltip && (
              <Tooltip content={helpInfoTooltip}>
                <IconWrapper name="HelpCircle" size={14} />
              </Tooltip>
            )}
          </div>
        </div>
        <div>
          <span className="text-sm font-normal text-gray-900">-</span>
        </div>
      </div>
    )
  }

  if (members?.length === 0) {
    return (
      <div className="mt-4 flex items-center space-x-2">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="#EDF0F4" />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M17.0874 10.6602H19.3899C22.1574 10.6602 23.5074 12.1378 23.4999 15.1678V18.8203C23.4999 21.7153 21.7149 23.5002 18.8124 23.5002H13.1799C10.2924 23.5002 8.49988 21.7153 8.49988 18.8127V13.1802C8.49988 10.0753 9.87988 8.50025 12.6024 8.50025H13.7874C14.4856 8.49275 15.1374 8.81525 15.5649 9.36275L16.2249 10.2402C16.4349 10.5028 16.7499 10.6602 17.0874 10.6602ZM12.5311 18.4689H19.4761C19.7836 18.4689 20.0311 18.2139 20.0311 17.9064C20.0311 17.5914 19.7836 17.3439 19.4761 17.3439H12.5311C12.2161 17.3439 11.9686 17.5914 11.9686 17.9064C11.9686 18.2139 12.2161 18.4689 12.5311 18.4689Z"
            fill="#B4BDCB"
          />
        </svg>
        <TypographyText className="text-sm text-gray-700">{t('label:noMembersInvited')}</TypographyText>
      </div>
    )
  }

  return (
    <div className="mt-4">
      <div className="mb-1 flex items-center justify-between">
        <div className="flex items-center">
          <TypographyText className="font-sm mr-0.5 text-sm font-medium text-gray-700 uppercase">
            {showCountNumber ? ` (${members?.length})` : ''}
          </TypographyText>
          {helpInfoTooltip && (
            <Tooltip content={helpInfoTooltip}>
              <IconWrapper name="HelpCircle" size={14} />
            </Tooltip>
          )}
        </div>
        {isShowCollapse && (
          <IconButton iconMenus="ChevronDown" size="xs" type="secondary" onClick={toggleCollapse} className={isCollapse ? 'rotate-180' : ''} />
        )}
      </div>
      {(!isShowCollapse || isCollapse) &&
        members?.map(member => (
          <div key={`member-${member?.id}`} className="mt-0.5 flex items-center py-1.5 first:mt-0">
            <div className="mr-2 flex-none">
              <Avatar src={member?.avatarVariants?.thumb?.url} size="sm" color={member?.defaultColour} alt={member?.fullName} />
            </div>
            <div className="text-sm font-medium text-gray-900">
              {member?.fullName}
              <span className="text-sm font-normal text-gray-600"> ({member?.labelRole})</span>
            </div>
          </div>
        ))}
    </div>
  )
}

export default HiringTeam
