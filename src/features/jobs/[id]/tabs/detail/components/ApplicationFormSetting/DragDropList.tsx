import type { DraggingStyle, DropResult, NotDraggingStyle } from '@hello-pangea/dnd'
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd'
import type { FC, ReactNode } from 'react'
import { useMemo } from 'react'

import { reorder } from '~/core/utilities/common'

import type { ApplicationField } from '~/lib/features/jobs/types/application-fields'

const getItemStyle = (isDragging: boolean, draggableStyle?: DraggingStyle | NotDraggingStyle) =>
  ({
    // some basic styles to make the items look a bit nicer
    userSelect: 'none',
    // padding: grid * 2,
    // margin: `0 0 ${grid}px 0`,

    // styles we need to apply on draggables
    ...draggableStyle,
    // change background colour if dragging
    background: 'white'
  }) as object

const DragDropList: FC<{
  fields: ApplicationField[]
  renderFieldView: (field: ApplicationField, onFieldChange?: (field: ApplicationField) => void) => ReactNode
  updateFields?: (fields: ApplicationField[]) => void
}> = ({ fields, renderFieldView, updateFields }) => {
  const { lockedFields, orderingFields } = useMemo(
    () =>
      (fields || []).reduce(
        (result, field) => {
          const lockedFields = [...result.lockedFields, ...(field.locked ? [field] : [])]
          const orderingFields = [...result.orderingFields, ...(field.locked ? [] : [field])]
          return {
            lockedFields,
            orderingFields
          }
        },
        {
          lockedFields: [] as ApplicationField[],
          orderingFields: [] as ApplicationField[]
        }
      ),
    [fields]
  )

  const onFieldChange = (field: ApplicationField) => {
    const newFields = fields.reduce((result, iField) => {
      return [...result, iField.index === field.index ? field : iField]
    }, [] as ApplicationField[])
    updateFields && updateFields(newFields)
  }

  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result

    if (!destination) {
      return
    }

    const items = reorder(orderingFields, source.index, destination.index)
    updateFields && updateFields([...lockedFields, ...items])
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable isDropDisabled={false} isCombineEnabled={false} ignoreContainerClipping={false} droppableId="droppable-config">
        {(providedDroppable, snapshot) => (
          <div {...providedDroppable.droppableProps} ref={providedDroppable.innerRef} className="grid">
            {(lockedFields || []).map((item, index) => (
              <div key={`locked-application-${item.index}`}>{renderFieldView(item, onFieldChange)}</div>
            ))}
            {(orderingFields || []).map((item, index) => {
              return (
                <Draggable
                  isDragDisabled={item?.locked}
                  key={`ordering-application-${item.index}`}
                  draggableId={`ordering-application-${item.index}`}
                  index={index}
                >
                  {(provided, snapshot) => {
                    return (
                      <div
                        className={`${snapshot.isDragging ? 'shadow-lg' : ''}`}
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        style={getItemStyle(snapshot.isDragging, provided.draggableProps.style)}
                      >
                        {renderFieldView(item, onFieldChange)}
                      </div>
                    )
                  }}
                </Draggable>
              )
            })}
            {providedDroppable?.placeholder as string}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  )
}

export default DragDropList
