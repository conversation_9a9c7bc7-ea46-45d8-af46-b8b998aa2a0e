import { TFunction } from 'next-i18next'
import type { FC } from 'react'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import ComboboxSelect from '~/core/ui/ComboboxSelect'
import IconWrapper from '~/core/ui/IconWrapper'
import type { ISelectOption } from '~/core/ui/Select'
import { TypographyText } from '~/core/ui/Text'
import { Toggle } from '~/core/ui/Toggle'
import { Tooltip } from '~/core/ui/Tooltip'

import type { ApplicationField } from '~/lib/features/jobs/types/application-fields'

const ApplicationFieldSettingRow: FC<{
  field: ApplicationField
  onFieldChange?: (field: ApplicationField) => void
}> = ({ field, onFieldChange }) => {
  const { t, i18n } = useTranslation()
  const requireListOptions = useMemo(
    () => [
      {
        value: 'required',
        supportingObj: {
          name: `${t('job:detail:application_form:required')}`
        }
      },
      {
        value: 'optional',
        supportingObj: {
          name: `${t('job:detail:application_form:optional')}`
        }
      }
    ],
    []
  )
  const [requireOption, setRequireOption] = useState<ISelectOption>(
    field.required ? (requireListOptions[0] as ISelectOption) : (requireListOptions[1] as ISelectOption)
  )
  return (
    <div className="flex items-center space-x-3 rounded-xs border border-gray-100 px-3 py-[9px]">
      <Tooltip content={field.locked ? t('tooltip:cannotCustomize') : t('tooltip:dragToReorder')}>
        <IconWrapper name={field.locked ? 'Lock' : 'GripVertical'} size={16} className="hover:pointer-cursor text-gray-400" />
      </Tooltip>

      <div className="flex flex-1 items-center gap-x-2">
        <Tooltip content={field.name[i18n.language]}>
          <TypographyText className="line-clamp-1 text-sm font-medium text-gray-700">{field.name[i18n.language]}</TypographyText>
        </Tooltip>
        {/* {field.field_level === 'custom' && (
          <Tooltip
            content={`${t('tooltip:warning_application_field_deleted')}`}>
            <IconWrapper
              name="HelpCircle"
              size={16}
              className="text-gray-400"
            />
          </Tooltip>
        )} */}
      </div>
      <div className="ml-2 flex items-center gap-x-2">
        <ComboboxSelect
          menuOptionAlign="end"
          avatarToolTipPosition="bottom"
          toolTipPositionAvatarCount="bottom"
          tooltipAlignAvatarCount="end"
          size="sm"
          isClearable={false}
          isSearchable={false}
          options={requireListOptions}
          value={requireOption}
          dropdownMenuClassName="w-[120px]!"
          containerMenuClassName="w-[120px]"
          onChange={value => {
            setRequireOption(value as ISelectOption)
            onFieldChange &&
              onFieldChange({
                ...field,
                required: (value as ISelectOption).value === 'required'
              })
          }}
          searchPlaceholder={`${t('label:placeholder:search')}`}
          loadingMessage={`${t('label:loading')}`}
          noOptionsMessage={`${t('label:noOptions')}`}
          type="unstyled"
          isDisabled={field.locked}
        />
        {!field.locked && (
          <Toggle
            size="sm"
            isChecked={field.visibility}
            onCheckedChange={checked => {
              onFieldChange &&
                onFieldChange({
                  ...field,
                  visibility: checked
                })
            }}
          />
        )}
      </div>
    </div>
  )
}

export default ApplicationFieldSettingRow
