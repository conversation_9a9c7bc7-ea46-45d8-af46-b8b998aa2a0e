'use client'

import type { Dispatch, SetStateAction } from 'react'
import { useCallback, useEffect } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IColorBadgeType } from '~/core/ui/Badge'
import { But<PERSON> } from '~/core/ui/Button'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import type { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { MultipleSelect } from '~/core/ui/MultipleSelect'
import { NativeSelect } from '~/core/ui/NativeSelect'
import type { ISelectOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import type { IStageType, IStageTypes } from '~/lib/features/jobs/types'
import { JOB_COLOR_STAGE_NAME } from '~/lib/features/jobs/utilities/enum'
import QueryInterviewKitsTemplateList from '~/lib/features/settings/interview-kits/graphql/query-interview-kits-template-list'
import QueryJobInterviewKitsStagesList from '~/lib/features/settings/interview-kits/graphql/query-job-interview-kits-stages-list'
import QueryAddJobInterviewKits from '~/lib/features/settings/interview-kits/graphql/submit-add-job-interview-kits'
import { useQueryInterviewKitsTemplateList } from '~/lib/features/settings/interview-kits/hooks/use-query-interview-kits-template-list'
import { useQueryJobInterviewKitsStagesList } from '~/lib/features/settings/interview-kits/hooks/use-query-job-interview-kits-stages-list'
import { mappingSubmitDataJobInterviewKits } from '~/lib/features/settings/interview-kits/mapping/job-interview-kits'
import schemaJobInterviewKitsForm from '~/lib/features/settings/interview-kits/schema/job-interview-kits-form'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'

export const ModalAddJobInterviewKits = ({
  jobId,
  stageTypes,
  setOpenJobInterviewKitsModal,
  callback
}: {
  jobId?: number
  stageTypes?: IStageTypes
  setOpenJobInterviewKitsModal: Dispatch<SetStateAction<boolean>>
  callback?: () => void
}) => {
  const { t } = useTranslation()
  const { data: interviewKitsTemplateList, trigger: triggerFetchTemplateList } = useQueryInterviewKitsTemplateList({
    query: QueryInterviewKitsTemplateList,
    variables: {
      page: 1,
      limit: configuration.defaultPageSize,
      jobId: Number(jobId)
    },
    shouldPause: true
  })
  const { data: interviewKitsStagesList, trigger: triggerFetchStagesList } = useQueryJobInterviewKitsStagesList({
    query: QueryJobInterviewKitsStagesList,
    variables: {
      page: 1,
      limit: configuration.defaultPageSize,
      jobId: Number(jobId)
    },
    shouldPause: true
  })

  useEffect(() => {
    triggerFetchStagesList()
    triggerFetchTemplateList()
  }, [])

  const { trigger, isLoading } = useSubmitCommon(QueryAddJobInterviewKits)
  const { setToast } = useToastStore()

  const onFinishCallback = useCallback(
    async (data: { ikitTemplateIds: ISelectOption[]; jobStageIds: ISelectOption }, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      trigger({ ...mappingSubmitDataJobInterviewKits(data), jobId }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.interviewKits,
            formAction,
            setToast
          })
        }
        const { jobIkitsCreate } = result.data
        if (jobIkitsCreate?.success) {
          formAction.reset()
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:interview_kit_added')}`
          })
          callback && callback()
          setOpenJobInterviewKitsModal(false)
        }

        return
      })
    },
    [isLoading, trigger, jobId, setToast, callback, setOpenJobInterviewKitsModal, t]
  )

  return (
    <DynamicImportForm
      id="add-job-interview-kits-form"
      className="w-full"
      schema={schemaJobInterviewKitsForm(t)}
      onSubmit={onFinishCallback}
      defaultValue={{ ikitTemplateIds: [], jobStageIds: undefined }}
    >
      {({ formState, control, submit }) => {
        return (
          <>
            <div className="mb-4">
              <Controller
                control={control}
                name="jobStageIds"
                render={({ field: { onChange, value } }) => (
                  <FormControlItem
                    labelRequired
                    destructive={formState.errors && !!formState.errors.jobStageIds}
                    destructiveText={formState.errors && formState.errors.jobStageIds?.message}
                    label={`${t('label:add_to_stage')}`}
                  >
                    <NativeSelect
                      size="sm"
                      onChange={onChange}
                      placeholder={`${t('label:placeholder:select')}`}
                      value={value}
                      destructive={formState.errors && !!formState.errors.jobStageIds}
                      isMultiShowType="dot-leading"
                      configSelectOption={{
                        dot: true,
                        supportingText: ['name']
                      }}
                      options={interviewKitsStagesList.map(item => {
                        return {
                          value: String(item.id),
                          dot: JOB_COLOR_STAGE_NAME(
                            String((stageTypes || []).filter((s: IStageType) => String(s.id) === String(item?.stageTypeId))?.[0]?.colorClassName)
                          ) as IColorBadgeType,
                          supportingObj: {
                            name: item.stageLabel
                          }
                        }
                      })}
                      classNameOverride={{
                        loadingMessage: `${t('label:loading')}`,
                        noOptionsMessage: `${t('label:noOptions')}`
                      }}
                    />
                  </FormControlItem>
                )}
              />
            </div>
            <Controller
              control={control}
              name="ikitTemplateIds"
              render={({ field: { onChange, value } }) => (
                <FormControlItem
                  labelRequired
                  destructive={formState.errors && !!formState.errors.ikitTemplateIds}
                  destructiveText={formState.errors && formState.errors.ikitTemplateIds?.message}
                  label={`${t('label:interview_kit')}`}
                >
                  <MultipleSelect
                    size="sm"
                    isClearable={false}
                    onChange={onChange}
                    destructive={formState.errors && !!formState.errors.ikitTemplateIds}
                    placeholder={`${t('label:placeholder:select')}`}
                    value={value}
                    configSelectOption={{
                      option: 'checkbox',
                      supportingText: ['name']
                    }}
                    options={interviewKitsTemplateList.map(item => {
                      return {
                        value: String(item.id),
                        supportingObj: {
                          name: item.name || `${t('label:interview_kits_template')}`
                        }
                      }
                    })}
                    classNameOverride={{
                      loadingMessage: `${t('label:loading')}`,
                      noOptionsMessage: `${t('label:noOptions')}`
                    }}
                  />
                </FormControlItem>
              )}
            />

            <div className="flex items-center justify-end pt-6">
              <Button
                type="secondary"
                className="mr-3"
                size="sm"
                onClick={() => setOpenJobInterviewKitsModal(false)}
                label={`${t('button:cancel')}`}
              />
              <Button onClick={submit} size="sm" htmlType="button" label={`${t('button:add')}`} />
            </div>
          </>
        )
      }}
    </DynamicImportForm>
  )
}

export default ModalAddJobInterviewKits
