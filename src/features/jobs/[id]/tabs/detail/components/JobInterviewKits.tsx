import { useCallback, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { openAlert } from '~/core/ui/AlertDialog'
import type { IColorBadgeType } from '~/core/ui/Badge'
import { Dialog } from '~/core/ui/Dialog'
import { Dot } from '~/core/ui/Dot'
import { IconButton } from '~/core/ui/IconButton'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import type { IJobDetail } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import type { IStageType, IStageTypes } from '~/lib/features/jobs/types'
import { JOB_COLOR_STAGE_NAME } from '~/lib/features/jobs/utilities/enum'
import QueryDeleteJobInterviewKits from '~/lib/features/settings/interview-kits/graphql/submit-delete-job-interview-kits'
import { mappingEditDataJobInterviewKits } from '~/lib/features/settings/interview-kits/mapping/job-interview-kits'
import type { IInterviewKitTemplateForm, IJobInterviewKit } from '~/lib/features/settings/interview-kits/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'

import InterviewKitsForm from '~/components/Settings/InterviewKits/InterviewKitsForm'

import ModalUpdateJobInterviewKits from './ModalUpdateJobInterviewKits'

const JobInterviewKits = ({
  jobDetail,
  jobId,
  jobIkits = undefined,
  stageTypes,
  callback,
  checkPermissionForActions
}: {
  jobDetail?: IJobDetail
  jobId?: number
  jobIkits?: IJobInterviewKit[]
  stageTypes?: IStageTypes
  callback?: () => void
  checkPermissionForActions?: {
    update: boolean
    delete: boolean
  }
}) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const [isOpenEdit, setOpenEdit] = useState<{
    open: boolean
    defaultValue?: IJobInterviewKit
  }>({
    open: false,
    defaultValue: undefined
  })
  const [isOpenPreview, setOpenPreview] = useState<{
    open: boolean
    defaultValue?: IJobInterviewKit
  }>({
    open: false,
    defaultValue: undefined
  })

  const { trigger: triggerDelete, isLoading: isLoadingDelete } = useSubmitCommon(QueryDeleteJobInterviewKits)

  const deleteInterviewKitsTemplateCallback = useCallback(
    async (data: { id?: string; name?: string }) => {
      if (isLoadingDelete) {
        return
      }

      triggerDelete({
        id: Number(data.id)
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.hiringPipelines,
            setToast
          })
        }

        const { jobIkitsDelete } = result.data
        if (jobIkitsDelete.success) {
          callback && callback()
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:interview_kit_deleted')}`
          })
        }

        return
      })
    },
    [isLoadingDelete, triggerDelete, setToast, callback, t]
  )

  if (jobIkits === undefined) return null

  return (
    <>
      <div className="pt-4">
        {jobIkits?.length === 0 ? (
          <div className="flex items-center space-x-2">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="16" cy="16" r="16" fill="#EDF0F4" />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17.0874 10.6602H19.3899C22.1574 10.6602 23.5074 12.1378 23.4999 15.1678V18.8203C23.4999 21.7153 21.7149 23.5002 18.8124 23.5002H13.1799C10.2924 23.5002 8.49988 21.7153 8.49988 18.8127V13.1802C8.49988 10.0753 9.87988 8.50025 12.6024 8.50025H13.7874C14.4856 8.49275 15.1374 8.81525 15.5649 9.36275L16.2249 10.2402C16.4349 10.5028 16.7499 10.6602 17.0874 10.6602ZM12.5311 18.4689H19.4761C19.7836 18.4689 20.0311 18.2139 20.0311 17.9064C20.0311 17.5914 19.7836 17.3439 19.4761 17.3439H12.5311C12.2161 17.3439 11.9686 17.5914 11.9686 17.9064C11.9686 18.2139 12.2161 18.4689 12.5311 18.4689Z"
                fill="#B4BDCB"
              />
            </svg>
            <TypographyText className="text-sm text-gray-700">{t('label:no_interview_kits')}</TypographyText>
          </div>
        ) : (
          <div className="space-y-4">
            {jobIkits.map((item, i) => (
              <div key={`interview-kits-${i}`} className="flex space-x-1.5">
                <div className="w-5">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M15.6738 7.51685C15.2976 7.51685 14.7992 7.50852 14.1787 7.50852C12.6654 7.50852 11.4211 6.25602 11.4211 4.72852V2.04852C11.4211 1.83768 11.2528 1.66602 11.044 1.66602H6.63619C4.57914 1.66602 2.9165 3.35435 2.9165 5.42352V14.4027C2.9165 16.5735 4.65835 18.3327 6.80782 18.3327H13.3717C15.4214 18.3327 17.0832 16.6552 17.0832 14.5843V7.89185C17.0832 7.68018 16.9157 7.50935 16.7061 7.51018C16.3538 7.51268 15.9313 7.51685 15.6738 7.51685Z"
                      fill="#D4DAE0"
                    />
                    <path
                      d="M13.4034 2.1385C13.1542 1.87933 12.7192 2.05766 12.7192 2.41683V4.61433C12.7192 5.536 13.4784 6.29433 14.4001 6.29433C14.9809 6.301 15.7876 6.30266 16.4726 6.301C16.8234 6.30016 17.0017 5.881 16.7584 5.62766C15.8792 4.7135 14.3051 3.07516 13.4034 2.1385Z"
                      fill="#E4E9EF"
                    />
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M7.47815 9.489H10.299C10.6415 9.489 10.9198 9.2115 10.9198 8.869C10.9198 8.5265 10.6415 8.24817 10.299 8.24817H7.47815C7.13565 8.24817 6.85815 8.5265 6.85815 8.869C6.85815 9.2115 7.13565 9.489 7.47815 9.489ZM7.47815 13.6511H12.0148C12.3573 13.6511 12.6357 13.3736 12.6357 13.0311C12.6357 12.6886 12.3573 12.4103 12.0148 12.4103H7.47815C7.13565 12.4103 6.85815 12.6886 6.85815 13.0311C6.85815 13.3736 7.13565 13.6511 7.47815 13.6511Z"
                      fill="#9CA3AF"
                    />
                  </svg>
                </div>
                <div className="group relative flex-1 space-y-1">
                  <Tooltip content={item.name} align="start">
                    <TypographyText
                      className="line-clamp-1 pr-[86px] text-sm font-medium text-gray-900 hover:cursor-pointer hover:underline"
                      onClick={() => {
                        setOpenPreview({
                          open: true,
                          defaultValue: item
                        })
                      }}
                    >
                      {item.name}
                    </TypographyText>
                  </Tooltip>
                  <div className="space-y-0.5">
                    {(item?.jobStages || []).map(jobStage => (
                      <div className="flex items-center space-x-1.5" key={`jobStage-${jobStage.stageTypeId}`}>
                        <Dot
                          size="lg"
                          color={
                            JOB_COLOR_STAGE_NAME(
                              String((stageTypes || []).filter((s: IStageType) => String(s.id) === String(jobStage.stageTypeId))?.[0]?.colorClassName)
                            ) as IColorBadgeType
                          }
                        />
                        <TypographyText className="text-xs text-gray-700">{jobStage.stageLabel}</TypographyText>
                      </div>
                    ))}
                  </div>

                  <div className="shadow-actions absolute -top-1 right-0 flex space-x-1 rounded-xs border border-gray-100 bg-white p-0.5 opacity-0 group-hover:opacity-100">
                    <Tooltip content={`${t('tooltip:preview')}`} mode="icon">
                      <IconButton
                        onClick={() => {
                          setOpenPreview({
                            open: true,
                            defaultValue: item
                          })
                        }}
                        iconMenus="Eye"
                        size="xs"
                        type="secondary"
                      />
                    </Tooltip>

                    {checkPermissionForActions?.update ? (
                      <Tooltip content={`${t('tooltip:edit')}`} mode="icon">
                        <IconButton
                          onClick={() => {
                            setOpenEdit({
                              open: true,
                              defaultValue: item
                            })
                          }}
                          iconMenus="Edit3"
                          size="xs"
                          type="secondary"
                        />
                      </Tooltip>
                    ) : null}

                    {checkPermissionForActions?.delete ? (
                      <Tooltip content={`${t('tooltip:delete')}`} mode="icon">
                        <IconButton
                          iconMenus="Trash2"
                          size="xs"
                          type="secondary-destructive"
                          onClick={() => {
                            openAlert({
                              isPreventAutoFocusDialog: false,
                              className: 'w-[480px]',
                              title: `${t('notification:delete_interview_kit')}`,
                              description: (
                                <div>
                                  <Trans
                                    i18nKey="label:optionJobAction:deleteJob:description"
                                    values={{
                                      jobTitle: item.name
                                    }}
                                  >
                                    <span className="font-medium text-gray-900" />
                                  </Trans>
                                </div>
                              ),
                              actions: [
                                {
                                  label: `${t('button:cancel')}`,
                                  type: 'secondary',
                                  size: 'sm'
                                },
                                {
                                  isCallAPI: true,
                                  label: `${t('button:delete')}`,
                                  type: 'destructive',
                                  size: 'sm',
                                  onClick: async e => {
                                    await deleteInterviewKitsTemplateCallback({
                                      id: item.id,
                                      name: item.name
                                    })
                                  }
                                }
                              ]
                            })
                          }}
                        />
                      </Tooltip>
                    ) : null}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <Dialog
        className="min-w-[480px]"
        open={isOpenEdit.open}
        isPreventAutoFocusDialog
        label={`${t('label:edit_interview_kit')}`}
        description={isOpenEdit.defaultValue?.name}
        onOpenChange={() => {
          setOpenEdit({
            open: false,
            defaultValue: undefined
          })
        }}
      >
        <ModalUpdateJobInterviewKits
          defaultValue={mappingEditDataJobInterviewKits(isOpenEdit.defaultValue, stageTypes) as IInterviewKitTemplateForm}
          setOpenJobInterviewKitsModal={() => {
            setOpenEdit({
              open: false,
              defaultValue: undefined
            })
          }}
          jobId={jobId}
          stageTypes={stageTypes}
          callback={callback}
        />
      </Dialog>

      <Dialog
        size="md"
        className="min-w-[680px]"
        open={isOpenPreview.open}
        isPreventAutoFocusDialog
        label={`${t('label:preview_interview_kit')}`}
        isDivider
        onOpenChange={() => {
          setOpenPreview({
            open: false,
            defaultValue: undefined
          })
        }}
      >
        <InterviewKitsForm
          isPreview
          defaultValue={mappingEditDataJobInterviewKits(isOpenPreview.defaultValue, stageTypes) as IInterviewKitTemplateForm}
          setOpen={() => {
            setOpenPreview({
              open: false,
              defaultValue: undefined
            })
          }}
        />
      </Dialog>
    </>
  )
}

export default JobInterviewKits
