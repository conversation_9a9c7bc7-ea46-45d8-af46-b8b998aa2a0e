'use client'

import { Trans, useTranslation } from 'next-i18next'
import type { FC } from 'react'
import { useCallback, useEffect, useState } from 'react'

import configuration from '~/configuration'
import type { ILogoAndAvatarVariants } from '~/core/@types/global'
import { openAlert } from '~/core/ui/AlertDialog'
import { Avatar } from '~/core/ui/Avatar'
import { Badge } from '~/core/ui/Badge'
import { Button } from '~/core/ui/Button'
import { Divider } from '~/core/ui/Divider'
import { IconButton } from '~/core/ui/IconButton'
import If from '~/core/ui/If'
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '~/core/ui/Popover'
import type { ISelectOption } from '~/core/ui/Select'
import { SelectOption } from '~/core/ui/Select'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { adminCanAction } from '~/core/utilities/permission'

import QueryCancelInviteMemberMutation from '~/lib/features/settings/members/graphql/submit-cancel-invite-member-mutation'
import QueryInviteMemberMutation from '~/lib/features/settings/members/graphql/submit-invite-member-mutation'
import type { IDeleteMember, IResendMember } from '~/lib/features/settings/members/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

const MemberInHiringTeamCard: FC<{
  name?: string
  email: string
  roleId?: number
  id: string
  avatarVariants?: ILogoAndAvatarVariants
  defaultColour?: string
  onReloadListMemberPending?: () => void
  onDeleteHiringMemberOfJob: (jobRecruiterId: number, userName: string) => void
  isLoadingChangeRoleOfJob?: boolean
  onChangeRoleOfJob?: (jobRecruiterId: number, jobRecruiterResponsibility: string) => void
  deleting?: boolean
  typeUser: 'pending' | 'admin' | 'member' | 'recommended'
  onAddRecommendedUser?: (data: ISelectOption) => Promise<void>
  jobRecruiterResponsibility: ISelectOption[]
  responsibility?: string
  jobRecruiterId?: number
}> = ({
  name,
  email,
  roleId,
  id,
  defaultColour,
  typeUser,
  avatarVariants,
  deleting,
  onDeleteHiringMemberOfJob,
  onReloadListMemberPending,
  onAddRecommendedUser,
  isLoadingChangeRoleOfJob,
  jobRecruiterResponsibility,
  responsibility,
  jobRecruiterId,
  onChangeRoleOfJob
}) => {
  const [selectRoleJob, setRoleJob] = useState(false)
  const [roleState, setRoleSate] = useState<ISelectOption>()
  const [activeUserAddingId, setActiveUserAddingId] = useState<number | undefined>()
  const { setToast } = useToastStore()
  const { trigger: triggerResend, isLoading: isLoadingResend } = useSubmitCommon(QueryInviteMemberMutation)
  const { trigger: triggerCancelInvite, isLoading: isLoadingDelete } = useSubmitCommon(QueryCancelInviteMemberMutation)
  const user = useBoundStore(state => state.user)
  const currentRole = useBoundStore(state => state.currentRole)
  const isFullAction = adminCanAction(currentRole?.code)
  const { t } = useTranslation()

  useEffect(() => {
    if (responsibility) {
      setRoleSate(jobRecruiterResponsibility.filter(res => res.value === responsibility)[0])
    }
  }, [responsibility])

  const cancelInviteMemberCallback = useCallback(
    async (data: IDeleteMember) => {
      if (isLoadingDelete) {
        return
      }

      triggerCancelInvite({
        id: Number(data.id)
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.members,
            setToast
          })
        }

        const { tenantInvitationCancel } = result.data
        if (tenantInvitationCancel.success) {
          onReloadListMemberPending && onReloadListMemberPending()
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:settings:teamMembers:success_remove_member', { fullName: data.email })}`
          })
        }

        return
      })
    },
    [isLoadingDelete, triggerCancelInvite, setToast, t]
  )

  const resendMemberCallback = useCallback(
    async (data: IResendMember) => {
      if (isLoadingResend) {
        return
      }

      triggerResend({
        email: data.email,
        roleId: Number(data.roleId),
        resend: true
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.members,
            setToast
          })
        }

        const { tenantInvitationSend } = result.data
        if (tenantInvitationSend.success) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:settings:teamMembers:success_sent_title')}`,
            description: `${t('notification:settings:teamMembers:success_sent_content', { email: data.email })}`
          })
        }

        return
      })
    },
    [isLoadingResend, triggerResend, setToast, t]
  )

  const onConfirmCancelPendingMember = () => {
    openAlert({
      isPreventAutoFocusDialog: false,
      className: 'w-[480px]',
      title: `${t('settings:teamMembers:removeInvitationAlert:title')}`,
      description: (
        <Trans
          i18nKey="settings:teamMembers:removeInvitationAlert:content"
          values={{
            name: email
          }}
        >
          <span className="font-medium text-gray-900" />
        </Trans>
      ),
      actions: [
        {
          label: `${t('settings:teamMembers:removeMemberAlert:actions:cancel')}`,
          type: 'secondary',
          size: 'sm'
        },
        {
          isCallAPI: true,
          label: `${t('settings:teamMembers:removeMemberAlert:actions:remove')}`,
          type: 'destructive',
          size: 'sm',
          onClick: async () =>
            await cancelInviteMemberCallback({
              id,
              email
            })
        }
      ]
    })
  }

  const onConfirmDeleteMemberHiringTeam = () => {
    openAlert({
      isPreventAutoFocusDialog: false,
      className: 'w-[480px]',
      title: `${t('common:modal:remove_member_title')}`,
      description: `${t('common:modal:remove_member_description', {
        name
      })}`,
      actions: [
        {
          label: `${t('button:cancel')}`,
          type: 'secondary',
          size: 'sm'
        },
        {
          isCallAPI: true,
          label: `${t('button:remove')}`,
          type: 'destructive',
          size: 'sm',
          onClick: async () => await onDeleteHiringMemberOfJob(Number(jobRecruiterId), name || '')
        }
      ]
    })
  }

  return (
    <div className="row mt-3 flex h-10 items-center" key={id}>
      <If condition={['member', 'admin', 'recommended'].includes(typeUser)}>
        <Avatar src={avatarVariants?.thumb?.url} alt={name} size="md" color={defaultColour} />
      </If>
      <If condition={['pending'].includes(typeUser)}>
        <Avatar src={avatarVariants?.thumb?.url} alt={email} size="md" color={'#F8EDED'} />
      </If>
      <If condition={['member', 'admin', 'recommended'].includes(typeUser)}>
        <div className="mx-2 flex flex-1 flex-col items-start">
          <Tooltip content={name}>
            <span className="line-clamp-1 block max-w-[250px] overflow-hidden text-sm font-medium text-ellipsis whitespace-nowrap text-gray-900">
              {name}
            </span>
          </Tooltip>
          <p className="line-clamp-1 block max-w-[250px] overflow-hidden text-xs font-normal text-ellipsis whitespace-nowrap text-gray-700">
            {email}
          </p>
        </div>
      </If>
      <If condition={typeUser === 'pending'}>
        <div className="mx-2 flex flex-1 flex-col items-start">
          <div className="flex flex-row items-center">
            <Tooltip content={email}>
              <span className="line-clamp-1 block max-w-[200px] overflow-hidden text-sm font-medium text-ellipsis whitespace-nowrap text-gray-900">
                {email}
              </span>
            </Tooltip>
            <Badge className="ml-1.5" size="sm" radius="circular">
              {t('label:status:pending')}
            </Badge>
          </div>
        </div>
      </If>
      <If condition={!['admin', 'recommended'].includes(typeUser)}>
        <div className="flex flex-row items-center space-x-2">
          <Popover open={selectRoleJob} onOpenChange={value => setRoleJob(value)}>
            <PopoverTrigger asChild>
              <TextButton
                type="tertiary"
                label={roleState?.supportingObj?.name}
                icon="trailing"
                className=""
                iconMenus="ChevronDown"
                underline={false}
              />
            </PopoverTrigger>

            <PopoverPortal>
              <PopoverContent align="start" sideOffset={10} className="min-w-[144px] p-1">
                {(jobRecruiterResponsibility || []).map((item, index) => (
                  <div
                    className="cursor-pointer hover:bg-gray-50"
                    key={index}
                    onClick={e => {
                      if (jobRecruiterId && onChangeRoleOfJob && item.value !== responsibility) {
                        onChangeRoleOfJob(jobRecruiterId, item.value)
                      }
                      setRoleSate(item)
                      setRoleJob(false)
                    }}
                  >
                    <SelectOption
                      option="radio"
                      size="sm"
                      data={item}
                      isSelected={roleState?.value === item.value}
                      isFocused={roleState?.value === item.value}
                      isOption={true}
                      isHeading={false}
                    />
                  </div>
                ))}
                <Divider />
                <If condition={typeUser.includes('pending')}>
                  <div
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={e => {
                      resendMemberCallback({
                        roleId: roleId?.toString(),
                        email
                      })
                      setRoleJob(false)
                    }}
                  >
                    <SelectOption
                      option="radio"
                      size="sm"
                      data={{
                        value: 'resend',
                        supportingObj: {
                          name: `${t('label:resend')}`
                        }
                      }}
                      isSelected={false}
                      isFocused={false}
                      isOption={true}
                      isHeading={true}
                    />
                  </div>
                </If>
                <div
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={e => {
                    typeUser === 'pending' ? onConfirmCancelPendingMember() : onConfirmDeleteMemberHiringTeam()
                    setRoleJob(false)
                  }}
                >
                  <SelectOption
                    classNameOption={{
                      value: 'text-red-500'
                    }}
                    data={{
                      value: 'delete',
                      supportingObj: {
                        name: `${t('button:delete')}`
                      }
                    }}
                    option="radio"
                    size="sm"
                    isSelected={false}
                    isFocused={false}
                    isOption={true}
                    isHeading={true}
                  />
                </div>
              </PopoverContent>
            </PopoverPortal>
          </Popover>
        </div>
      </If>
      <If condition={typeUser === 'recommended'}>
        <Button
          label={`${t('button:add')}`}
          type="tertiary"
          size="xs"
          isLoading={activeUserAddingId === Number(id)}
          isDisabled={activeUserAddingId === Number(id)}
          onClick={() => {
            setActiveUserAddingId(Number(id))
            onAddRecommendedUser &&
              onAddRecommendedUser({
                value: id,
                supportingObj: {
                  defaultColour: defaultColour,
                  description: email,
                  name: name || ''
                },
                avatarVariants: avatarVariants
              }).then(() => {
                setTimeout(() => setActiveUserAddingId(undefined), 200)
              })
          }}
        />
      </If>
    </div>
  )
}

export default MemberInHiringTeamCard
