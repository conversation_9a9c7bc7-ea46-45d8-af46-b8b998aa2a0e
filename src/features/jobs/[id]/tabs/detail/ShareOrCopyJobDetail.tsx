import { useTranslation } from 'next-i18next'
import type { FC } from 'react'
import { useCallback, useRef, useState } from 'react'
import { FacebookShareButton, LinkedinShareButton } from 'react-share'

import pathConfiguration from 'src/configuration/path'
import { SocialButton } from '~/core/ui/SocialButton'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'

import { useClient } from '~/lib/hooks/use-is-client'

const ShareOrCopyJobDetail: FC<{
  className?: string
  jobId: string
  tenantSlug: string
  isDisabled?: boolean
}> = ({ className = '', jobId, tenantSlug = '', isDisabled = false }) => {
  const copyButtonRef = useRef<HTMLButtonElement>(null)
  const [isCopied, setIsCopied] = useState<boolean>(false)
  const { t } = useTranslation()
  const { isClient } = useClient()
  const url = `${pathConfiguration.careers.applyJobWithDomain({
    tenantSlug,
    jobId
  })}?utm_medium=internal_social_share`

  const onCopyLinkToClipboard = useCallback(() => {
    setIsCopied(true)

    navigator.clipboard.writeText(url)

    const timeout = setTimeout(() => {
      setIsCopied(false)
      copyButtonRef?.current?.blur()
    }, 1500)

    return () => clearTimeout(timeout)
  }, [jobId, tenantSlug])

  return isClient ? (
    <div className={className}>
      <TypographyText className="tablet:text-base mb-3 text-lg text-gray-900">{t('careers:detail:shareOrCopy')}</TypographyText>
      <div className="flex">
        <div className="mr-4">
          <LinkedinShareButton disabled={isDisabled} url={url}>
            <SocialButton asChild isDisabled={isDisabled} type="linkedin" />
          </LinkedinShareButton>
        </div>
        <div className="mr-4">
          <FacebookShareButton disabled={isDisabled} url={url}>
            <SocialButton asChild isDisabled={isDisabled} type="facebook" />
          </FacebookShareButton>
        </div>
        <Tooltip content={isCopied ? t('tooltip:copied_link') : t('tooltip:copy_link')} open={isCopied}>
          <SocialButton ref={copyButtonRef} isDisabled={isDisabled} type="share" onClick={!isDisabled ? onCopyLinkToClipboard : undefined} />
        </Tooltip>
      </div>
    </div>
  ) : null
}

export default ShareOrCopyJobDetail
