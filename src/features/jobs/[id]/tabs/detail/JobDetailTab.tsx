'use client'

import DOMPurify from 'dompurify'
import type { Dispatch, SetStateAction } from 'react'
import { useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import type { FCC } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Avatar } from '~/core/ui/Avatar'
import { Divider } from '~/core/ui/Divider'
import If from '~/core/ui/If'
import { TypographyText } from '~/core/ui/Text'
import type { TextButtonProps } from '~/core/ui/TextButton'
import { TextButton } from '~/core/ui/TextButton'
import { cn } from '~/core/ui/utils'
import { convertLinkFromHTML } from '~/core/utilities/common'
import { accessReferralFeature } from '~/core/utilities/feature-permission'
import { adminAndMemberCanAction } from '~/core/utilities/permission'

import { checkPermissionForActions } from '~/lib/features/candidates/utilities'
import type { IJobDetail } from '~/lib/features/jobs/graphql/query-job-detail-mini'
import QueryTenantAdmins from '~/lib/features/jobs/graphql/query-tenant-admin'
import type { IJobForm, IStageTypes } from '~/lib/features/jobs/types'
import { JOB_STATUS_ENUM } from '~/lib/features/jobs/utilities/enum'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useUserCheckKindOf } from '~/lib/hooks/use-user-check-kind'
import useBoundStore from '~/lib/store'

import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import { NoContactComponent } from '~/features/client-contact/components/ClientContactDialog'
import useClientContact from '~/features/client-contact/use-client-contact'
import useClientContactDialog from '~/features/client-contact/use-client-contact-dialog'

import HiringTeam from './components/HiringTeam'
import JobDetailSkeleton from './components/JobDetailSkeleton'
import JobInfo from './components/JobInfo'
import JobInterviewKits from './components/JobInterviewKits'
import ReferralReward from './components/ReferralReward'

const JobDetailSettingLayout: FCC<{
  showSkeleton?: boolean
  title?: string
  description?: string
  isShowButton: boolean
  buttonConfig?: TextButtonProps
}> = ({ children, showSkeleton = false, title, description, isShowButton = true, buttonConfig }) => {
  return (
    <div className="px-6 py-4">
      {showSkeleton ? (
        <JobDetailSkeleton />
      ) : (
        <>
          <div className={`flex items-center justify-between ${description ? 'mb-0.5' : ''}`}>
            <TypographyText className="text-lg font-medium text-gray-900">{title}</TypographyText>
            {buttonConfig && isShowButton ? <TextButton {...buttonConfig} /> : null}
          </div>
          {description && <TypographyText className="text-sm text-gray-600">{description}</TypographyText>}
          {children}
        </>
      )}
    </div>
  )
}

const HtmlDisplay = ({ children, className }: { children?: string; className: string }) => {
  return children ? (
    <div
      dangerouslySetInnerHTML={{
        __html: DOMPurify.sanitize(convertLinkFromHTML(children))
      }}
      className={className}
    />
  ) : (
    <div className={className}>-</div>
  )
}
const JobDetailTab = ({
  stageTypes,
  jobDetail,
  setOpenEditPipeline,
  toggleQuickUpdateModal,
  setOpenJobInterviewKitsModal,
  setOpenApplicationFormSetting,
  callback,
  updateReferralReward
}: {
  stageTypes?: IStageTypes
  jobDetail?: IJobDetail
  setOpenEditPipeline?: Dispatch<SetStateAction<boolean>>
  toggleQuickUpdateModal?: Dispatch<SetStateAction<boolean>>
  setOpenJobInterviewKitsModal?: Dispatch<SetStateAction<boolean>>
  setOpenApplicationFormSetting?: Dispatch<SetStateAction<boolean>>
  callback?: () => void
  updateReferralReward: (data: IJobForm) => Promise<void>
}) => {
  const { userIsAsClient } = useUserCheckKindOf()
  const { t } = useTranslation()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const { user, currentRole, featureSetting } = useBoundStore()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { actionJobIkit } = usePermissionJob()
  const { trigger: triggerFetchListAdmin, data: adminList } = useQueryGraphQL({
    query: QueryTenantAdmins,
    variables: {
      onlyAdmins: true
    },
    shouldPause: true
  })

  const listHiringMembers = [
    ...(!!jobDetail?.jobsShow?.jobRecruiters
      ? jobDetail?.jobsShow?.jobRecruiters?.map(jr => ({
          ...jr?.user,
          labelRole: jr.responsibilityDescription
        }))
      : [])
  ]

  const companyId = useMemo(() => {
    return jobDetail?.jobsShow?.permittedFields?.company?.value?.id || jobDetail?.jobsShow?.company?.id
  }, [jobDetail])

  useEffect(() => {
    triggerFetchListAdmin()
  }, [triggerFetchListAdmin])

  const calcHeight = useClassBasedTopSpace({
    34: 'min-h-[calc(100vh-120px)]',
    default: 'min-h-[calc(100vh-86px)]'
  })

  const { canActionInviteClient } = useClientContact()
  const { ClientContactDialogComponent, openContactModal } = useClientContactDialog()
  return (
    <div className="grid grid-cols-[57.37%_42.63%]">
      <ClientContactDialogComponent
        clientInvitations={jobDetail?.jobsShow.clientInvitations}
        recommendedClientContacts={jobDetail?.jobsShow.recommendedClientContacts}
        clientMembers={jobDetail?.jobsShow.clientMembers}
        jobsShow={jobDetail?.jobsShow}
      />
      <div className={cn('border-r border-r-gray-100 px-6 py-4', calcHeight)}>
        {!jobDetail?.jobsShow ? (
          <JobDetailSkeleton />
        ) : (
          <>
            <JobInfo sizeField="sm" isCompanyKind={isCompanyKind} {...jobDetail?.jobsShow} />
            <Divider className="my-4" />
            <div>
              <div>
                <TypographyText className="text-base font-medium text-gray-900">{t('job:detail:title')}</TypographyText>
                <HtmlDisplay className="prose prose-sm mt-2 max-w-full text-sm break-words">{jobDetail?.jobsShow.pitch}</HtmlDisplay>
              </div>
              <div className="mt-6">
                <TypographyText className="text-base font-medium text-gray-900">{t('job:detail:description')}</TypographyText>
                <HtmlDisplay className="prose prose-sm mt-2 max-w-full text-sm break-words">{jobDetail?.jobsShow.description}</HtmlDisplay>
              </div>
            </div>
          </>
        )}
      </div>
      <div>
        <If condition={adminAndMemberCanAction(currentRole?.code)}>
          <JobDetailSettingLayout
            showSkeleton={!jobDetail?.jobsShow}
            title={`${t('job:detail:hiring_process:title')}`}
            description={`${t('job:detail:hiring_process:description')}`}
            isShowButton={adminAndMemberCanAction(currentRole?.code)}
            buttonConfig={{
              label: `${t('button:edit')}`,
              size: 'md',
              iconMenus: 'Edit3',
              underline: false,
              isDisabled: jobDetail?.jobsShow?.status === JOB_STATUS_ENUM.archived,
              onClick: () => setOpenEditPipeline?.(true)
            }}
          />
          <Divider />
          <JobDetailSettingLayout
            showSkeleton={!jobDetail?.jobsShow}
            title={`${t('job:detail:hiring_team:title')}`}
            isShowButton={adminAndMemberCanAction(currentRole?.code)}
            buttonConfig={{
              label: listHiringMembers.length === 0 ? `${t('button:add')}` : `${t('button:edit')}`,
              size: 'md',
              iconMenus: listHiringMembers.length === 0 ? 'Plus' : 'Edit3',
              underline: false,
              isDisabled: jobDetail?.jobsShow?.status === JOB_STATUS_ENUM.archived,
              onClick: () => toggleQuickUpdateModal?.(true)
            }}
          >
            <HiringTeam title={`${t('job:detail:hiring_team:recruiter')}`} members={listHiringMembers} />
          </JobDetailSettingLayout>
          <Divider />
        </If>
        <If condition={isCompanyKind || (!!companyId && isFeatureEnabled(PLAN_FEATURE_KEYS.company))}>
          <JobDetailSettingLayout
            title={`${t('job:detail:client_contact:title')}`}
            // description={`${t('job:detail:client_contact:description')}`}
            isShowButton={canActionInviteClient.create}
            buttonConfig={{
              label: `${t('button:edit')}`,
              size: 'md',
              iconMenus: 'Edit3',
              underline: false,
              isDisabled: jobDetail?.jobsShow?.status === JOB_STATUS_ENUM.archived,
              onClick: () =>
                jobDetail?.jobsShow &&
                companyId &&
                openContactModal({
                  jobId: parseInt(jobDetail.jobsShow.id.toString()),
                  companyId: parseInt(companyId.toString()),
                  onContactUpdated: () => {
                    callback?.()
                    return Promise.resolve()
                  }
                })
            }}
          >
            <div className="pt-4">
              {(jobDetail?.jobsShow?.clientMembers?.length || 0) > 0 ? (
                jobDetail?.jobsShow?.clientMembers?.map(member => (
                  <div key={`member-${member?.id}`} className="mt-0.5 flex items-center py-1.5 first:mt-0">
                    <div className="mr-2 flex-none">
                      <Avatar src={member?.avatarVariants?.thumb?.url} size="sm" color={member?.defaultColour} alt={member?.fullName} />
                    </div>
                    <TypographyText className="text-sm font-medium text-gray-900">{member?.fullName}</TypographyText>
                  </div>
                ))
              ) : (
                <NoContactComponent />
              )}
            </div>
          </JobDetailSettingLayout>
          <Divider />
        </If>
        <If condition={!userIsAsClient()}>
          <JobDetailSettingLayout
            title={`${t('job:detail:interview_kits:title')}`}
            description={`${t('job:detail:interview_kits:description')}`}
            isShowButton={
              checkPermissionForActions({
                user,
                currentRole,
                jobDetail: {
                  owner: jobDetail?.jobsShow?.owner,
                  jobRecruiters: jobDetail?.jobsShow?.jobRecruiters
                },
                ignoreRecruiters: true,
                permission: actionJobIkit.create
              }) && adminAndMemberCanAction(currentRole?.code)
            }
            buttonConfig={{
              label: `${t('button:add')}`,
              size: 'md',
              iconMenus: 'Plus',
              underline: false,
              isDisabled: jobDetail?.jobsShow?.status === JOB_STATUS_ENUM.archived,
              onClick: () => setOpenJobInterviewKitsModal && setOpenJobInterviewKitsModal(true)
            }}
          >
            {jobDetail?.jobsShow?.id ? (
              <JobInterviewKits
                jobDetail={jobDetail}
                jobIkits={jobDetail?.jobsShow?.jobIkits}
                jobId={jobDetail?.jobsShow?.id}
                callback={callback}
                stageTypes={stageTypes}
                checkPermissionForActions={{
                  update: checkPermissionForActions({
                    user,
                    currentRole,
                    jobDetail: {
                      owner: jobDetail?.jobsShow?.owner,
                      jobRecruiters: jobDetail?.jobsShow?.jobRecruiters
                    },
                    ignoreRecruiters: true,
                    permission: actionJobIkit.update
                  }),
                  delete: checkPermissionForActions({
                    user,
                    currentRole,
                    jobDetail: {
                      owner: jobDetail?.jobsShow?.owner,
                      jobRecruiters: jobDetail?.jobsShow?.jobRecruiters
                    },
                    ignoreRecruiters: true,
                    permission: actionJobIkit.delete
                  })
                }}
              />
            ) : null}
          </JobDetailSettingLayout>
        </If>
        {isFeatureEnabled(PLAN_FEATURE_KEYS.application_form) && isUnLockFeature(PLAN_FEATURE_KEYS.application_form) && (
          <>
            <Divider />
            <JobDetailSettingLayout
              showSkeleton={!jobDetail?.jobsShow}
              title={`${t('job:detail:application_form:title')}`}
              description={`${t('job:detail:application_form:description')}`}
              isShowButton={adminAndMemberCanAction(currentRole?.code)}
              buttonConfig={{
                label: `${t('button:edit')}`,
                size: 'md',
                iconMenus: 'Edit3',
                underline: false,
                isDisabled: jobDetail?.jobsShow?.status === JOB_STATUS_ENUM.archived,
                onClick: () => setOpenApplicationFormSetting?.(true)
              }}
            />
          </>
        )}
        {!!jobDetail?.jobsShow ? (
          <div>
            {(jobDetail?.jobsShow?.tenant?.tenantReferralSettings.enabling ||
              jobDetail?.jobsShow?.tenant?.tenantReferralSettings?.values?.enabling) &&
              isFeatureEnabled(PLAN_FEATURE_KEYS.referral) &&
              isUnLockFeature(PLAN_FEATURE_KEYS.referral) &&
              accessReferralFeature(featureSetting) && (
                <>
                  <Divider />
                  <ReferralReward jobDetail={jobDetail} updateReferralReward={updateReferralReward} />
                </>
              )}
          </div>
        ) : (
          <div className="px-6 py-4">
            <JobDetailSkeleton />
          </div>
        )}
      </div>
    </div>
  )
}

export default JobDetailTab
