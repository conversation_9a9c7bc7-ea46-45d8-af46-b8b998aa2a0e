'use client'

import type { FC } from 'react'
import { use<PERSON>allback, useEffect, useMemo, useState } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import configuration from '~/configuration'
import type { IFormAction, ISelectOption } from '~/core/@types/global'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import { ROLE_CODE } from '~/core/constants/role'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Button } from '~/core/ui/Button'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { Input } from '~/core/ui/Input'
import { NativeSelect } from '~/core/ui/NativeSelect'
import { TextButton } from '~/core/ui/TextButton'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { memberCanAction } from '~/core/utilities/permission'

import QueryUserPermissionsList from '~/lib/features/permissions/graphql/query-user-permissions-list'
import QueryInviteMemberMutation from '~/lib/features/settings/members/graphql/submit-invite-member-mutation'
import useRoles from '~/lib/features/settings/members/hooks/use-roles-logic'
import schemaInviteForm from '~/lib/features/settings/members/schema/invite-member-form'
import type { IinviteMemberForm } from '~/lib/features/settings/members/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

const InviteMember4HiringModalForm: FC<{
  onOpenChange: (open: boolean) => void
  callback: () => void
  jobId?: number
}> = ({ onOpenChange, callback, jobId }) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const { currentRole } = useBoundStore()
  const { roles } = useRoles()

  const [roleId, setRoleId] = useState<number>()

  const { trigger, isLoading } = useSubmitCommon(QueryInviteMemberMutation)
  const { data, trigger: fetchPermissionsList } = useQueryGraphQL({
    query: QueryUserPermissionsList,
    variables: { ...(roleId ? { roleId } : undefined) },
    shouldPause: true
  })

  const permissionsList = data?.userPermissionsList?.collection
  const optionsRoles = useMemo(() => {
    if (memberCanAction(currentRole?.code)) {
      return roles.filter(role => [ROLE_CODE.LimitedMember, ROLE_CODE.Member].includes(String(role?.id)))
    }
    return roles
  }, [roles, currentRole])

  useEffect(() => {
    if (roleId) {
      fetchPermissionsList()
    }
  }, [roleId])

  const onFinishCallback = useCallback(
    async (data: IinviteMemberForm, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      trigger({
        email: data.email,
        roleId: Number(data.roleId),
        jobId,
        userPermissions: data?.userPermissions?.length ? data.userPermissions.filter(item => item.moduleName) : permissionsList
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.members,
            formAction,
            setToast
          })
        }

        const { tenantInvitationSend } = result.data
        if (tenantInvitationSend.success) {
          callback && callback()
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:settings:teamMembers:success_sent_title')}`,
            description: `${t('notification:settings:teamMembers:success_sent_content_new_invitation', {
              domain: PUBLIC_APP_NAME
            })}`
          })
          onOpenChange(false)
        }

        return
      })
    },
    [isLoading, trigger, setToast, t, onOpenChange, callback]
  )

  return (
    <DynamicImportForm id="invite-member-form" className="w-full" schema={schemaInviteForm(t)} onSubmit={onFinishCallback} noUseSubmit>
      {({ formState, control, setValue }) => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          setValue('userPermissions', permissionsList)
        }, [permissionsList])

        return (
          <>
            <div className="flex-1">
              <Controller
                control={control}
                name="email"
                defaultValue=""
                render={({ field: { onChange, value } }) => (
                  <FormControlItem
                    labelRequired
                    destructive={formState.errors && !!formState.errors.email}
                    destructiveText={formState.errors && formState.errors.email?.message}
                    label={`${t('form:email_invite_member_field_label')}`}
                  >
                    <Input
                      size="sm"
                      autoFocus
                      placeholder={`${t('form:email_invite_member_field_placeholder')}`}
                      onChange={onChange}
                      value={value}
                      destructive={formState.errors && !!formState.errors.email}
                    />
                  </FormControlItem>
                )}
              />
            </div>
            <div className="mt-4 flex-1">
              <Controller
                control={control}
                name="roleId"
                defaultValue=""
                render={({ field: { onChange, value } }) => {
                  const filter = roles.filter(item => item.value === value)
                  return (
                    <FormControlItem
                      labelRequired
                      destructive={formState.errors && !!formState.errors.roleId}
                      destructiveText={formState.errors && formState.errors.roleId?.message}
                      label={`${t('form:role_invite_member_field_label')}`}
                    >
                      <NativeSelect
                        size="sm"
                        isSearchable={false}
                        placeholder={`${t('form:role_invite_member_field_placeholder')}`}
                        onChange={newValue => {
                          const getValue = newValue as ISelectOption
                          onChange(getValue?.value)
                          if (getValue?.value) {
                            setRoleId(Number(getValue.value))
                          } else {
                            setValue('userPermissions', [])
                            setRoleId(undefined)
                          }
                        }}
                        value={filter}
                        options={optionsRoles}
                        configSelectOption={{
                          supportingText: ['name', 'description']
                        }}
                        destructive={formState.errors && !!formState.errors.roleId}
                        classNameOverride={{
                          loadingMessage: `${t('label:loading')}`,
                          noOptionsMessage: `${t('label:noOptions')}`
                        }}
                      />
                    </FormControlItem>
                  )
                }}
              />
            </div>

            <div className="flex items-center justify-between space-x-3 pt-6">
              <TextButton
                type="secondary"
                label={`${t('button:learnMore')}`}
                icon="trailing"
                iconMenus="ExternalLink"
                underline={false}
                onClick={() => {
                  window.open(pathConfiguration.helpCenter.inviteMember, 'blank')
                }}
              />

              <div className="flex items-center space-x-3">
                <Button
                  type="secondary"
                  size="sm"
                  onClick={() => onOpenChange(false)}
                  label={`${t('settings:teamMembers:inviteMemberModal:actions:cancel')}`}
                />
                <Button
                  size="sm"
                  isDisabled={isLoading}
                  isLoading={isLoading}
                  htmlType="submit"
                  label={`${t('settings:teamMembers:inviteMemberModal:actions:sendInvites')}`}
                />
              </div>
            </div>
          </>
        )
      }}
    </DynamicImportForm>
  )
}

export default InviteMember4HiringModalForm
