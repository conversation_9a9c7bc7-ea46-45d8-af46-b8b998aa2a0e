'use client'

import type { BuiltInProviderType } from 'next-auth/providers'
import type { ClientSafeProvider, LiteralUnion } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { destroyCookie, parseCookies } from 'nookies'
import { useCallback, useEffect, useMemo } from 'react'

import configuration from '~/configuration'
import type { IFormAction } from '~/core/@types/global'
import { COOKIE_PATH, SESSION_COOKIE_SSO } from '~/core/constants/cookies'
import { useRecaptcha, verifyRecaptcha } from '~/core/hooks/use-verify-captcha'
import { setSessionCookieClient } from '~/core/middleware/save-session-cookie'
import useMiddlewareRequest from '~/core/middleware/use-middleware-request'
import AppRouteLoading from '~/core/ui/AppRouteLoading'
import { catchErrorFromRequest } from '~/core/utilities/catch-api-error'

import type { ILoginForm } from '~/lib/features/login/types'
import { useTrackingUTM } from '~/lib/hooks/use-tracking-utm'
import { useRouterContext } from '~/lib/next/use-router-context'
import useToastStore from '~/lib/store/toast'

import LayoutHybrid from '~/components/Layout/LayoutHybrid'
import LayoutLoginRegister from '~/components/Layout/LayoutLoginRegister'
import LoginView from '~/components/Login'

const LoginContainer: React.FC<{
  providers?: Record<LiteralUnion<BuiltInProviderType, string>, ClientSafeProvider> | null
}> = ({ providers }) => {
  useRecaptcha()
  const router = useRouter()
  const { searchParams } = useRouterContext()
  const error = searchParams?.get('error')
  const { utm_campaign, utm_source, utm_medium, utm_content, isHasParam } = useTrackingUTM()
  const cookies = parseCookies()
  const { setToast } = useToastStore()

  const { trigger: loginWithEmail, isMutating: isLoading } = useMiddlewareRequest({
    endpoint: configuration.api.login,
    method: 'POST'
  })

  const ssoCookie = cookies[SESSION_COOKIE_SSO]
  const ssoObj = useMemo(() => (ssoCookie ? JSON.parse(ssoCookie) : {}), [ssoCookie])

  useEffect(() => {
    const options = { path: COOKIE_PATH }
    destroyCookie(null, SESSION_COOKIE_SSO, options)
  }, [])

  const { trigger: loginWithSSO, isMutating: isLoadingSSO } = useMiddlewareRequest({
    endpoint: configuration.api.loginSSO,
    method: 'POST'
  })

  const navigateToVerifyEmailPage = useCallback((data: ILoginForm) => {
    router.push(`${configuration.path.verifyEmail}?email=${data.email}`)
  }, [])

  const loginWithEmailCallback = useCallback(
    async (data: ILoginForm, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      verifyRecaptcha(async (isNotGoogleBOT: boolean) => {
        if (isNotGoogleBOT) {
          try {
            await loginWithEmail({
              ...data,
              ...(isHasParam ? { utm_campaign, utm_source, utm_medium, utm_content } : {}),
              provider: 'web'
            })
            navigateToVerifyEmailPage(data)
          } catch (error) {
            catchErrorFromRequest({
              error,
              formAction,
              callbackHandleStatusError422: keys => {
                keys.forEach((session: { field: string; message: string }) => {
                  formAction.setError('email', {
                    type: 'custom',
                    message: [session.message]
                  })
                })
              }
            })
          }
        }
      })
    },
    [isLoading, loginWithEmail, isHasParam, utm_campaign, utm_source, utm_medium, utm_content, navigateToVerifyEmailPage]
  )

  const loginSSOCallback = useCallback(async () => {
    if (isLoadingSSO) {
      return
    }

    verifyRecaptcha(async (isNotGoogleBOT: boolean) => {
      if (isNotGoogleBOT) {
        try {
          const result = await loginWithSSO({})

          const obj = {
            ...ssoObj,
            ssoToken: result.ssoToken
          }
          await setSessionCookieClient(SESSION_COOKIE_SSO, JSON.stringify(obj).toString(), 30 * 24 * 60 * 60)

          router.push(configuration.path.loginSSO)
        } catch (error) {
          catchErrorFromRequest({ error, setToast })
        }
      }
    })
  }, [isLoadingSSO, loginWithSSO, ssoObj, setToast])

  return (
    <LayoutHybrid>
      <AppRouteLoading>
        <LayoutLoginRegister>
          <LoginView
            queryErrorMessage={error as string}
            isLoadingEmail={isLoading}
            isLoadingSSO={isLoadingSSO}
            providers={providers}
            onFinish={loginWithEmailCallback}
            loginSSO={loginSSOCallback}
          />
        </LayoutLoginRegister>
      </AppRouteLoading>
    </LayoutHybrid>
  )
}

export default LoginContainer
