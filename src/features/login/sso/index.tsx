'use client'

import { useRouter } from 'next/navigation'
import { destroyCookie, parseCookies } from 'nookies'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IFormAction } from '~/core/@types/global'
import { COOKIE_PATH, SESSION_COOKIE_IP, SESSION_COOKIE_SSO } from '~/core/constants/cookies'
import { useRecaptcha, verifyRecaptcha } from '~/core/hooks/use-verify-captcha'
import { setSessionCookieClient } from '~/core/middleware/save-session-cookie'
import useMiddlewareRequest from '~/core/middleware/use-middleware-request'
import AppRouteLoading from '~/core/ui/AppRouteLoading'
import type { ISocialProps } from '~/core/ui/SocialButton'
import { catchErrorFromRequest } from '~/core/utilities/catch-api-error'

import type { ILoginForm } from '~/lib/features/login/types'

import LayoutHybrid from '~/components/Layout/LayoutHybrid'
import LoginSSOView from '~/components/LoginSSO'

const LoginSSOContainer = () => {
  useRecaptcha()
  const { t } = useTranslation()
  const router = useRouter()
  const cookies = parseCookies()

  const [provider, setProvider] = useState<ISocialProps>()
  const [email, setEmail] = useState<string>('')

  const ssoCookie = cookies[SESSION_COOKIE_SSO]
  const ssoObj = useMemo(() => (ssoCookie ? JSON.parse(ssoCookie) : undefined), [ssoCookie])
  const ip = cookies[SESSION_COOKIE_IP]

  useEffect(() => {
    if (!ssoObj?.ssoToken) {
      router.push(configuration.path.login)
    }
    if (ssoObj && Object.keys(ssoObj).length) {
      setProvider(ssoObj.provider)
      setEmail(ssoObj.email)
    }
  }, [ssoObj])

  const { trigger: loginWithSSO, isMutating: isLoadingSSO } = useMiddlewareRequest({
    endpoint: configuration.api.loginSSO,
    method: 'POST',
    headers: {
      accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${ssoObj?.ssoToken}`,
      IP: String(ip)
    }
  })

  const loginSSOCallback = useCallback(
    async (data: ILoginForm, formAction: IFormAction) => {
      if (isLoadingSSO) {
        return
      }

      verifyRecaptcha(async (isNotGoogleBOT: boolean) => {
        if (isNotGoogleBOT) {
          try {
            const result = await loginWithSSO({
              ...data
            })

            if (result.provider) {
              const obj = {
                ...ssoObj,
                email: String(data.email),
                provider: result.provider,
                tenantId: String(result.tenantId)
              }

              await setSessionCookieClient(SESSION_COOKIE_SSO, JSON.stringify(obj).toString(), 30 * 24 * 60 * 60)
              setProvider(result.provider)
              setEmail(String(data.email))
            } else {
              catchErrorFromRequest({
                error: {
                  errors: [
                    {
                      extensions: {
                        errors: [
                          {
                            field: 'email',
                            message: `${t('form:emailDoesNotExist')}`
                          }
                        ]
                      }
                    }
                  ]
                },
                formAction
              })
            }
          } catch (error) {
            catchErrorFromRequest({
              error: {
                errors: [
                  {
                    extensions: {
                      errors: [
                        {
                          field: 'email',
                          message: `${t('form:emailDoesNotExist')}`
                        }
                      ]
                    }
                  }
                ]
              },
              formAction
            })
          }
        }
      })
    },
    [isLoadingSSO, loginWithSSO, ssoObj, t]
  )

  const clearEmailSSOCallback = useCallback(async () => {
    const obj = {
      ssoToken: ssoObj?.ssoToken,
      email: undefined,
      provider: undefined,
      tenantId: undefined
    }

    await setSessionCookieClient(SESSION_COOKIE_SSO, JSON.stringify(obj).toString(), 30 * 24 * 60 * 60)
    setProvider(undefined)
    setEmail('')
  }, [ssoObj])

  const clearTokenSSOCallback = useCallback(async () => {
    const options = { path: COOKIE_PATH }
    destroyCookie(null, SESSION_COOKIE_SSO, options)
  }, [])

  return (
    <LayoutHybrid>
      <AppRouteLoading>
        <LoginSSOView
          email={email}
          provider={provider}
          isLoadingSSO={isLoadingSSO}
          loginSSO={loginSSOCallback}
          clearEmailSSO={clearEmailSSOCallback}
          clearTokenSSO={clearTokenSSOCallback}
        />
      </AppRouteLoading>
    </LayoutHybrid>
  )
}

export default LoginSSOContainer
