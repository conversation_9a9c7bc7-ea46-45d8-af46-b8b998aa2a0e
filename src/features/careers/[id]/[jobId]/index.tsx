import type { JobType } from '~/lib/features/apply/jobId/types'
import type { CareerPageSettingType } from '~/lib/features/careers/[id]/types'
import useJobVisit from '~/lib/features/jobs/hooks/use-job-visit'

import JobDetailView from '~/components/Apply/[jobId]'
import { LANG_ENG_OPTION, LANG_JA_OPTION } from '~/components/DropdownLanguage'
import LayoutFooterPowerBy from '~/components/Layout/LayoutFooterPowerBy'
import type { SettingEditorFormType } from '~/components/Settings/Careers/Editor/CustomizeSettingTab'

const JobDetailContainer = ({
  job,
  careerPageSetting,
  templateConfig
}: {
  job: JobType
  careerPageSetting: CareerPageSettingType
  templateConfig?: SettingEditorFormType
}) => {
  useJobVisit(parseInt(job?.id), String(job?.tenant?.slug || ''))

  return (
    <LayoutFooterPowerBy
      tenantSlug={job?.tenant?.slug || ''}
      showSwitchLanguages={Object.values(careerPageSetting?.languages || {}).filter(lang => !!lang.enable).length > 1}
      languagesOptions={[LANG_ENG_OPTION, LANG_JA_OPTION]}
    >
      <JobDetailView job={job} careerPageSetting={careerPageSetting} templateConfig={templateConfig} />
    </LayoutFooterPowerBy>
  )
}

export default JobDetailContainer
