'use client'

import { useCallback, useEffect, useState } from 'react'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { Skeleton } from '~/core/ui/Skeleton'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryTenant from '~/lib/features/careers/[id]/graphql/query-tenant'
import type { CareerPageSettingType, ICareerForm, TenantType } from '~/lib/features/careers/[id]/types'
import type { TemplateResponseType } from '~/lib/features/settings/careers/types/editor'
import { useRouterContext } from '~/lib/next/use-router-context'
import useToastStore from '~/lib/store/toast'

import SimpleCareersView from '~/components/Careers/[id]'
import CareerPageTemplateView from '~/components/Careers/[id]/CareerPageTemplateView'
import { LANG_ENG_OPTION, LANG_JA_OPTION } from '~/components/DropdownLanguage'
import LayoutFooterPowerBy from '~/components/Layout/LayoutFooterPowerBy'

const SimpleCareersContainer = ({
  careerPageSetting,
  careerTemplate,
  queryStringParams
}: {
  careerPageSetting: CareerPageSettingType
  queryStringParams: ICareerForm
  careerTemplate?: {
    publicCareerTemplatesShow: TemplateResponseType
  }
}) => {
  const { clientGraphQL } = useContextGraphQL()
  const { params } = useRouterContext()
  const id = params?.id
  const [tenant, setTenant] = useState<TenantType>()
  const { setToast } = useToastStore()
  const tenantSlug = id ? decodeURIComponent(String(id)) : careerPageSetting?.tenant_slug
  const fetchTenant = useCallback(() => {
    return clientGraphQL
      .query(QueryTenant, {
        tenantSlug
      })
      .toPromise()
      .then((result: { error: any; data: { publicTenantsShow: TenantType } }) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            error404ShouldForceToNotFoundPage: true
          })
        }

        const { publicTenantsShow } = result.data
        setTenant(publicTenantsShow)

        return publicTenantsShow
      })
  }, [clientGraphQL, tenantSlug])

  useEffect(() => {
    fetchTenant()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (!!tenant && !tenant?.careerSiteSettings?.enablingCareerPageBuilder) {
      let link: HTMLLinkElement | null = document.querySelector("link[rel~='icon']")
      if (!link) {
        link = document.createElement('link')
        link.rel = 'icon'
        document.head.appendChild(link)
      }
      link.href = '/favicon.ico'
    }
  }, [tenant])
  return !tenant ? (
    <div className="">
      <Skeleton className="mx-auto my-[11px] h-10 w-full max-w-[1440px] rounded-xs" />
      <Skeleton className="mx-auto my-[11px] h-[480px] w-full rounded-xs" />
    </div>
  ) : tenant?.careerSiteSettings?.enablingCareerPageBuilder ? (
    <CareerPageTemplateView
      queryStringParams={queryStringParams}
      showSwitchLanguages={Object.values(careerPageSetting?.languages || {}).filter(lang => !!lang.enable).length > 1}
      tenantSlug={String(tenantSlug)}
      tenant={tenant}
      careerTemplate={careerTemplate}
    />
  ) : (
    <LayoutFooterPowerBy
      tenantSlug={tenantSlug as string}
      showSwitchLanguages={Object.values(careerPageSetting?.languages || {}).filter(lang => !!lang.enable).length > 1}
      languagesOptions={[LANG_ENG_OPTION, LANG_JA_OPTION]}
    >
      <SimpleCareersView
        queryStringParams={queryStringParams}
        tenantSlug={tenantSlug as string}
        careerPageSetting={careerPageSetting}
        tenant={tenant}
      />
    </LayoutFooterPowerBy>
  )
}

export default withQueryClientProvider(SimpleCareersContainer)
