'use client'

import { useRouter } from 'next/navigation'
import { createContext, useEffect, useMemo } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import configuration from '~/configuration'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'
import { adminCanAction } from '~/core/utilities/permission'

import QueryUserPermissionsList from '~/lib/features/permissions/graphql/query-user-permissions-list'
import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useBoundStore from '~/lib/store'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import ReferralSettingManagementView from '~/components/Settings/Referrals'

export const ReferralSettingManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const ReferralManagementContainer = () => {
  const router = useRouter()
  const { user, currentRole } = useBoundStore()
  const { actionAccessReferral } = usePermissionSetting()
  const { data } = useQueryGraphQL({
    query: QueryUserPermissionsList,
    variables: {
      ...(user?.id ? { userId: Number(user?.id) } : undefined)
    },
    shouldPause: !user?.id
  })

  const actionManageReferral = useMemo(() => {
    return data?.userPermissionsList?.collection.find((permission: any) => permission.moduleName === PERMISSIONS_LIST?.manage_career_hub?.keyModule)
      ?.enabled
  }, [data])

  useEffect(() => {
    if (!!data && !adminCanAction(currentRole?.code) && !actionManageReferral) router.push(configuration.path.errorAccessDenied)
  }, [data, actionManageReferral])

  return (
    <LayoutGridSettings>
      <ReferralSettingManagementPermissionContext.Provider
        value={{
          create: !!actionAccessReferral,
          update: !!actionAccessReferral,
          delete: !!actionAccessReferral
        }}
      >
        <ReferralSettingManagementView />
      </ReferralSettingManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule, PERMISSIONS_LIST.manage_career_hub.keyModule]
  },
  withQueryClientProvider(ReferralManagementContainer)
)
