import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useBoundStore from '~/lib/store'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import TeamsManagementView from '~/components/Settings/Teams'

export const TeamsManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const TeamsManagementContainer = () => {
  const { user } = useBoundStore()
  const { actionTeam } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <TeamsManagementPermissionContext.Provider
        value={{
          create: !!actionTeam,
          update: !!actionTeam,
          delete: !!actionTeam
        }}
      >
        <TeamsManagementView user={user} />
      </TeamsManagementPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  TeamsManagementContainer
)
