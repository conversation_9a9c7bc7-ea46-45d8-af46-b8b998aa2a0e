'use client'

import { createContext } from 'react'

import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { canAccessSetting, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import TermsAndConditionsView from '~/components/Settings/TermsAndConditions'

export const CareersPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const TermsAndConditionsSettingContainer = () => {
  const { actionCareer } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <CareersPermissionContext.Provider
        value={{
          create: !!actionCareer,
          update: !!actionCareer,
          delete: !!actionCareer
        }}
      >
        <TermsAndConditionsView />
      </CareersPermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.career_page.keyModule]
  },
  TermsAndConditionsSettingContainer
)
