import { createContext } from 'react'

import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { DEFAULT_PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'

import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import WorkspaceView from '~/components/Settings/Workspace'

export const WorkspacePermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const WorkspaceContainer = () => {
  const { actionWorkSpace } = usePermissionSetting()

  return (
    <LayoutGridSettings>
      <WorkspacePermissionContext.Provider
        value={{
          create: !!actionWorkSpace,
          update: !!actionWorkSpace,
          delete: !!actionWorkSpace
        }}
      >
        <WorkspaceView />
      </WorkspacePermissionContext.Provider>
    </LayoutGridSettings>
  )
}

export default WorkspaceContainer
