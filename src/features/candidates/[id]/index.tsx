'use client'

import { useRouter } from 'next/navigation'
import { FC, type JSX, useEffect } from 'react'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import configuration from '~/configuration'
import type { IRouterWith<PERSON> } from '~/core/@types/global'
import { ACTIONS_PERMISSIONS, canAccessFeature, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import usePermissionCandidate from '~/lib/features/permissions/hooks/use-permission-candidate'
import useBoundStore from '~/lib/store'

import CandidateProfileView from '~/components/Candidates/Profile/CandidateProfileView'
import LayoutGrid from '~/components/Layout/LayoutGrid'
import type { ISwitchLayoutView } from '~/components/SwitchLayout/SwitchLayoutView'

const CandidateProfileContainer = ({
  returnUrl,
  id,
  applicantId,
  isDrawer = false,
  setSwitchView,
  renderedActions,
  talentPoolId
}: {
  returnUrl?: string
  id?: IRouterWithID
  applicantId?: IRouterWithID
  isDrawer: boolean
  setSwitchView?: (value: ISwitchLayoutView) => void
  renderedActions?: () => JSX.Element
  talentPoolId?: string
}) => {
  const router = useRouter()
  const { permissionSetting } = useBoundStore()
  const { canAccessPage } = usePermissionCandidate({ applicantId })

  useEffect(() => {
    if (isDrawer === false && canAccessPage === false && permissionSetting.length) {
      router.push(configuration.path.errorAccessDenied)
    }
  }, [canAccessPage])

  if (isDrawer) {
    if (canAccessPage === false) {
      return null
    }

    return (
      <CandidateProfileView
        returnUrl={returnUrl}
        isDrawer={isDrawer}
        renderedActions={renderedActions}
        id={id}
        applicantId={applicantId}
        setSwitchView={setSwitchView}
        talentPoolId={talentPoolId}
      />
    )
  }

  return (
    <LayoutGrid>
      {canAccessPage ? (
        <CandidateProfileView
          returnUrl={returnUrl}
          isDrawer={isDrawer}
          renderedActions={undefined}
          id={id}
          applicantId={applicantId}
          setSwitchView={setSwitchView}
        />
      ) : null}
    </LayoutGrid>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.job_management.keyModule],
    keyModuleObject: [
      PERMISSIONS_LIST.job_management.objects.profile.keyModuleObject,
      PERMISSIONS_LIST.job_management.objects.applicant.keyModuleObject
    ],
    action: ACTIONS_PERMISSIONS.show
  },
  CandidateProfileContainer
)
