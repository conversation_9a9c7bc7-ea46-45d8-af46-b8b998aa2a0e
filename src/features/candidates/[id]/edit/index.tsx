'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import configuration from '~/configuration'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>ith<PERSON> } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { ScrollArea } from '~/core/ui/ScrollArea'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { ACTIONS_PERMISSIONS, canAccessFeature, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import QueryAgencyCandidateDetail from '~/lib/features/candidates/graphql/query-agency-candidate-detail'
import QueryCandidateDetail from '~/lib/features/candidates/graphql/query-candidate-detail'
import { getsBase64FromImageURL } from '~/lib/features/candidates/utilities/export'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import { mappingPrepareFromQuery } from '~/lib/features/settings/profiles/edit/mapping'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'
import type { IResume } from '~/lib/features/settings/profiles/edit/types'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import ResumesEditorContentView from '~/components/Resumes/Editor/Content/ResumesEditorContentView'
import ResumesPreviewTemplatesWrapper from '~/components/Resumes/PreviewTemplates/ResumesPreviewTemplatesWrapper'
import TemplatePDFInitialize from '~/components/Resumes/PreviewTemplates/Templates/TemplatePDFInitialize'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

const CandidateProfileEditCVContainer = ({ id }: { id?: IRouterWithID }) => {
  const router = useRouter()
  const { pathname, params: paramsRouter } = useRouterContext()
  const { t, i18n } = useTranslation()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { resumeData, setResume } = useResumeStore()
  const { clientGraphQL } = useContextGraphQL()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const employeeId = Number(id)

  const { data: customFieldViewData } = useCustomFieldSettingByUser({
    objectKind: 'profile',
    shouldPause: false,
    forceVisibleToEmployeeProfile: true
  })

  const [isLoadingProfileTemplate, setLoadingProfileTemplate] = useState(false)
  const [isLoadingSaveFiles, setLoadingSaveFile] = useState(false)
  const scrollRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    triggerGetResume()

    return () => {
      setResume(undefined)
    }
  }, [])

  const triggerGetResume = async () => {
    type paramsType = {
      id: number
      talentPoolId?: number
    }
    const params: paramsType = { id: employeeId }

    return clientGraphQL
      .query(isCompanyKind ? QueryAgencyCandidateDetail : QueryCandidateDetail, { ...params })
      .toPromise()
      .then(
        (result: {
          error: { graphQLErrors: Array<object> }
          data: {
            profilesShow: IResume
          }
        }) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              setToast,
              router: {
                push: router.push,
                pathname,
                params: paramsRouter
              },
              error404ShouldForceToNotFoundPage: true
            })

            return {}
          }

          const { profilesShow } = result.data
          if (profilesShow === null) {
            router.push(configuration.path.errorAccessDenied)
          } else {
            const mappingData = async () => {
              const mappingData = mappingPrepareFromQuery(profilesShow)
              const logoVariantsUrl = user.currentTenant?.logoVariants?.medium?.url || user.currentTenant?.logoVariants?.thumb?.url
              const avatarVariantsUrl =
                // 'https://raw.githubusercontent.com/parallax/jsPDF/refs/heads/master/examples/images/Octonyan.jpg'
                mappingData?.permittedFields?.avatar?.value?.medium?.url || mappingData?.permittedFields?.avatar?.value?.thumb?.url

              const endpointsUrl = []
              if (logoVariantsUrl) {
                endpointsUrl.push({
                  logoBase64: true,
                  url: logoVariantsUrl
                })
              }

              if (avatarVariantsUrl) {
                endpointsUrl.push({
                  avatarBase64: true,
                  url: avatarVariantsUrl
                })
              }

              const imageBase64 = await getsBase64FromImageURL(endpointsUrl)

              setResume({
                ...mappingData,
                ...imageBase64
              })
            }

            mappingData()
          }

          return {}
        }
      )
  }

  // -------- FUNCTIONS --------

  return (
    <div className="flex">
      <div className="w-[45%]">
        <ScrollArea
          scrollRef={scrollRef}
          className="flex-1"
          viewportClassName="h-full w-full rounded-[inherit] [&>*:first-child]:block! h-[calc(100vh-56px)] flex-1 overflow-y-auto border-l border-l-gray-100 pb-5 px-8"
          scrollBarClassName="mx-1 py-1"
          rootStyle={{
            height: '100vh'
          }}
        >
          <ResumesEditorContentView
            scrollRef={scrollRef}
            id={Number(resumeData?.id)}
            customFieldViewData={customFieldViewData}
            isLoadingProfileTemplate={isLoadingProfileTemplate}
            isLoadingSaveFiles={isLoadingSaveFiles}
            customClickBackButton={() => router.back()}
          />
        </ScrollArea>
      </div>

      <div className="relative w-[55%] min-w-[636px] bg-gray-500">
        <TemplatePDFInitialize language={i18n.language}>
          {({ pdf }) => (
            <>
              {resumeData !== undefined ? (
                <ResumesPreviewTemplatesWrapper
                  pdf={pdf}
                  viewportClassName="h-full w-full rounded-[inherit] [&>*:first-child]:block! px-5 pt-[76px] pb-5 border-l border-l-gray-100"
                  isShowSaveToFile
                  isEmployee={false}
                  employeeId={employeeId}
                  resumeData={resumeData}
                  setResume={setResume}
                  customFieldViewData={customFieldViewData}
                  isLoadingProfileTemplate={isLoadingProfileTemplate}
                  setLoadingProfileTemplate={setLoadingProfileTemplate}
                  isLoadingSaveFiles={isLoadingSaveFiles}
                  setLoadingSaveFile={setLoadingSaveFile}
                  companyName={user?.currentTenant?.name}
                />
              ) : null}
            </>
          )}
        </TemplatePDFInitialize>
      </div>
    </div>
  )
}

export default withSubscriptionPlanLockFearture(
  withPermissionFeatureProvider(
    {
      checkAccessPermission: canAccessFeature,
      keyModule: [PERMISSIONS_LIST.job_management.keyModule],
      keyModuleObject: [
        PERMISSIONS_LIST.job_management.objects.profile.keyModuleObject,
        PERMISSIONS_LIST.job_management.objects.applicant.keyModuleObject
      ],
      action: ACTIONS_PERMISSIONS.show
    },
    CandidateProfileEditCVContainer
  ),
  PLAN_FEATURE_KEYS.resumee_builder as FeatureName
)
