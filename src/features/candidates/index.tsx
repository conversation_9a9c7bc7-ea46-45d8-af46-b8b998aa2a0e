'use client'

import type { ReactElement } from 'react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import configuration from '~/configuration'
import type { IParamsTableInfinity } from '~/core/@types/global'
import { AGENCY_TENANT, LIST_ACTION_FILTERS } from '~/core/constants/enum'
import { Badge } from '~/core/ui/Badge'
import { Drawer } from '~/core/ui/Drawer'
import If from '~/core/ui/If'
import { SplitButton } from '~/core/ui/SplitButton'
import type { IPagePagination } from '~/core/ui/TablePagination'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { ACTIONS_PERMISSIONS, canAccessFeature, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'
import { pushStateBrowser } from '~/core/utilities/is-browser'
import { adminAndMemberCanAction } from '~/core/utilities/permission'

import QueryAgencyProfilesList from '~/lib/features/candidates/graphql/query-agency-profiles-list'
import QueryProfilesList from '~/lib/features/candidates/graphql/query-profiles-list'
import useProfileViewDisplayManagement from '~/lib/features/candidates/hooks/use-profile-view-display-management'
import { mappingDisplayColumnsSetting, useUserSetting } from '~/lib/features/candidates/hooks/use-query-user-setting'
import { mappingAdvancedFilterCandidates } from '~/lib/features/candidates/mapping/candidate-filter-mapping'
import { mappingProfileViewFilter } from '~/lib/features/candidates/mapping/profile-view-display-mapping'
import useCandidateStore from '~/lib/features/candidates/store'
import type { ICandidatesFilter, IProfileViewDisplay } from '~/lib/features/candidates/types'
import type { InputFieldsSettingType } from '~/lib/features/candidates/types/user-setting'
import { DESC_SORTING, OPERATOR } from '~/lib/features/candidates/utilities/enum'
import usePermissionCandidate from '~/lib/features/permissions/hooks/use-permission-candidate'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import BulkActions from '~/components/Candidates/BulkActions/Candidate/BulkActions'
import CandidateEditingListingTable from '~/components/Candidates/CandidateEditingListingTable'
import CreateCandidateModal from '~/components/Candidates/CreateCandidateModal'
import type { FieldSettingType } from '~/components/DisplayConfig'
import DisplayConfigNew from '~/components/DisplayConfig/DisplayConfigNew'
import Filter from '~/components/Filter/Filter'
import FilterCandidateListingConfig from '~/components/FilterCandidateListingConfig'
import ProfileViewDisplayConfig from '~/components/ProfileViewDisplayConfig'
import { TopSpaceProvider, useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import SwitchLayoutView from '~/components/SwitchLayout/SwitchLayoutView'

import CandidateProfileContainer from './[id]'

const DEFAULT_FILTER = {
  operator: OPERATOR.and
}

export const CandidatesProfileViewDisplayConfig = (props: {
  currentViewId: number
  setProfileViewDisplay: (profileViewDisplay: IProfileViewDisplay | undefined) => void
  children: () => ReactElement
}) => {
  const { currentViewId, setProfileViewDisplay } = props
  const { profileViewDisplayShow } = useProfileViewDisplayManagement({
    viewId: Number(currentViewId)
  })
  const [isLoaded, setLoaded] = useState(false)

  useEffect(() => {
    profileViewDisplayShow.fetch()
  }, [])

  useEffect(() => {
    if (profileViewDisplayShow.data?.userSettingsProfileViewShow) {
      setLoaded(true)
      setProfileViewDisplay(profileViewDisplayShow.data?.userSettingsProfileViewShow)
    }
  }, [profileViewDisplayShow?.data])

  return isLoaded ? props.children() : null
}

const CandidatesManagementContainer = (props: {
  profileViewDisplay?: IProfileViewDisplay
  setProfileViewDisplay: (profileViewDisplay: IProfileViewDisplay | undefined) => void
}) => {
  const { profileViewDisplay, setProfileViewDisplay } = props
  const { searchParams } = useRouterContext()
  const view = searchParams?.get('view')
  const [openCreateCandidate, setOpenCreateCandidate] = useState<boolean>(false)
  const [filter, changeFilter] = useState<ICandidatesFilter | undefined>(
    !view && !profileViewDisplay
      ? DEFAULT_FILTER
      : mappingProfileViewFilter({
          profileView: profileViewDisplay,
          filter: DEFAULT_FILTER // Clear previous filter when change view
        })
  )
  const [count, setCount] = useState(0)
  const [sorting, setSorting] = useState<{
    [key: string]: string | null
  }>({
    lastActivity: DESC_SORTING
  })
  const { user, setRefetchMyList, refetchMyList, refetchMyDelete, setRefetchMyDelete, bulkSelectedAll, bulkValues, currentRole } = useBoundStore()
  const { setToast } = useToastStore()
  const { userSetting, resetAsDefault, updateFieldsSetting, isDefaultUserSetting } = useUserSetting({
    setToast
  })

  const [configUserDisplay, setConfigUserDisplay] = useState<FieldSettingType[]>()

  const { action } = useProfileViewDisplayManagement({
    viewId: Number(view)
  })
  const { t } = useTranslation()

  const [configSwitchLayout, setConfigSwitchLayout] = useState({
    path: [`${configuration.path.candidates.list}/`],
    redirectUrls: ['']
  })

  const [actionOnFilters, setActionOnFilters] = useState<string>('')

  const { setOpenUploadDrawer, setUploadDrawerJobId } = useCandidateStore()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const { isCompanyKind: isAgencyTenant } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const { actionProfile } = usePermissionCandidate({})
  const { data: customFieldProfileViewData } = useCustomFieldSettingByUser({
    objectKind: 'profile'
  })
  const mappingsFilterProfileField = (customFieldProfileViewData || [])?.filter(item => item.visibility)

  const drawerContainerRef = useRef<HTMLDivElement>(null)
  const tableRef = useRef<any>(null)

  const topSpace = useClassBasedTopSpace({
    34: 'h-full',
    default: 'h-screen'
  })

  const params = mappingAdvancedFilterCandidates({ ...filter }, user)
  const { data, refetch, fetchPagination, forceChangeCurrentPage, isFetching } = usePaginationGraphPage({
    queryDocumentNode: isAgencyTenant ? QueryAgencyProfilesList : QueryProfilesList,
    queryKey: 'my-candidates-management',
    filter: {
      ...(profileViewDisplay?.id ? { ...params, sorting, uSettingId: Number(profileViewDisplay.id) } : { ...params, sorting }),
      relatedObjects: undefined
    }
  })

  useEffect(() => {
    if (setCount !== undefined && data?.meta?.totalRowCount !== undefined) {
      setCount(data?.meta?.totalRowCount)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.meta?.totalRowCount])

  const onReOrderUserDisplay = useCallback(
    (data: FieldSettingType[]) => {
      setConfigUserDisplay(data)
      if (profileViewDisplay?.id && Number(user?.id) !== Number(profileViewDisplay?.createdById)) return null
      return updateFieldsSetting(data)
    },
    [updateFieldsSetting]
  )

  const onUpdateView = (pageParam = {} as IParamsTableInfinity) => {
    const params = mappingAdvancedFilterCandidates(pageParam, user)

    if (action.update.isLoading) {
      return
    }

    action.update
      .profileView({
        id: Number(profileViewDisplay?.id),
        name: profileViewDisplay?.name,
        group: 'profile_view',
        state: profileViewDisplay?.state,
        profileFilters: {
          search: params?.search || undefined,
          fieldsFilter: params?.fieldsFilter || undefined,
          operator: params?.operator || OPERATOR.and,
          relatedObjects: params?.relatedObjects || undefined
        }
      })
      .then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }

        const { userSettingsProfileViewUpdate } = result.data
        if (userSettingsProfileViewUpdate?.userSetting) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:changesSaved')}`
          })
          setProfileViewDisplay(userSettingsProfileViewUpdate.userSetting)
        }

        return
      })
  }

  const onClearFilter = () => {
    changeFilter(DEFAULT_FILTER)
    setProfileViewDisplay({
      ...profileViewDisplay,
      profileFilters: {
        fieldsFilter: undefined,
        search: undefined,
        operator: profileViewDisplay?.profileFilters?.operator || OPERATOR.and
      }
    })
  }

  useEffect(() => {
    if (refetchMyList) {
      refetch()
      setRefetchMyList(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchMyList])

  useEffect(() => {
    if (refetchMyDelete) {
      refetch()
      setRefetchMyDelete(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchMyDelete])

  useEffect(() => {
    if (profileViewDisplay?.profileDisplay) {
      setConfigUserDisplay(mappingDisplayColumnsSetting(profileViewDisplay?.profileDisplay as unknown as InputFieldsSettingType[], t))
    } else {
      setConfigUserDisplay(userSetting?.profileDisplay)
    }
  }, [userSetting?.profileDisplay, profileViewDisplay?.profileDisplay])

  useEffect(() => {
    if (!view && !profileViewDisplay) {
      changeFilter(DEFAULT_FILTER)
    } else if (profileViewDisplay) {
      const newFilter = mappingProfileViewFilter({
        profileView: profileViewDisplay,
        filter: DEFAULT_FILTER // Clear previous filter when change view
      })
      changeFilter(newFilter)
    }
  }, [profileViewDisplay])

  useEffect(() => {
    switch (actionOnFilters) {
      case LIST_ACTION_FILTERS.updateView:
        onUpdateView(filter)
        break
      case LIST_ACTION_FILTERS.clearFilter:
        onClearFilter()
        break
      default:
        break
    }
    setActionOnFilters('')
  }, [actionOnFilters])

  useEffect(() => {
    return () => {
      setProfileViewDisplay(undefined)
    }
  }, [])

  const calcHeightScroll = useClassBasedTopSpace({
    34: 'h-[calc(100vh-90px)]',
    default: 'h-[calc(100vh-56px)]'
  })

  return (
    <SwitchLayoutView
      configurations="ghost"
      returnUrl={configuration.path.candidates.list}
      paths={configSwitchLayout.path}
      redirectUrls={configSwitchLayout.redirectUrls}
    >
      {({ switchView, setSwitchView, renderedActions }) => {
        return (
          <div className={cn('flex flex-col', topSpace)}>
            <div className="flex h-[56px] flex-none items-center justify-between px-6 py-4">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <p className="text-lg font-medium text-gray-900 dark:text-gray-200">{t('candidates:candidateTable:heading')}</p>
                  {count > 0 ? (
                    <Badge radius="circular" size="md">
                      {count}
                    </Badge>
                  ) : null}
                </div>

                <ProfileViewDisplayConfig viewType="candidate" />
              </div>
              <Filter value={filter} onChange={changeFilter}>
                <div className="flex">
                  <div className="mr-2 w-[260px]">
                    <Filter.SearchText
                      typingDebounceSubmit={500}
                      maxLength={50}
                      size="xs"
                      placeholder={`${t('label:placeholder:search_by_name_phone_email_link')}`}
                      name="search"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <FilterCandidateListingConfig
                      user={user}
                      filters={filter}
                      configUserDisplay={configUserDisplay}
                      mappingsProfileField={mappingsFilterProfileField}
                      setActionOnFilters={setActionOnFilters}
                    />

                    <DisplayConfigNew
                      fields={configUserDisplay}
                      isDefaultFields={isDefaultUserSetting}
                      onReOrder={onReOrderUserDisplay}
                      resetAsDefault={resetAsDefault}
                      refetchFilterList={refetch}
                    />

                    <If condition={actionProfile.create}>
                      <SplitButton
                        label={`${t('button:addCandidate')}`}
                        size="xs"
                        menu={[
                          {
                            label: `${t('button:uploadCvsResume')}`,
                            icon: 'Upload',
                            onClick: () => {
                              setUploadDrawerJobId([
                                {
                                  defaultValue: true,
                                  profileId: undefined,
                                  jobId: undefined,
                                  jobTitle: undefined
                                }
                              ])
                              setOpenUploadDrawer(true)
                            }
                          }
                        ]}
                        onClick={() => setOpenCreateCandidate(true)}
                        htmlType="button"
                        iconMenus="Plus"
                      />
                    </If>
                  </div>
                </div>
              </Filter>
            </div>

            {configUserDisplay?.length && !refetchMyList ? (
              <div className="flex-1 pl-6">
                <CandidateEditingListingTable
                  tableRef={(tableEditor: any) => {
                    return (tableRef.current = tableEditor)
                  }}
                  calcHeightScroll={calcHeightScroll}
                  hiddenColumns={['matchRank']}
                  configUserDisplay={configUserDisplay}
                  classNameEmpty="-ml-6"
                  setOpenCreateCandidate={setOpenCreateCandidate}
                  data={data}
                  fetcher={{
                    fetchPagination,
                    forceChangeCurrentPage
                  }}
                  isFetching={isFetching}
                  filter={filter}
                  clearFilter={onClearFilter}
                  sorting={sorting}
                  setSorting={setSorting}
                  actions={{
                    configSwitchLayout,
                    setConfigSwitchLayout,
                    switchView,
                    setSwitchView
                  }}
                  enableRowSelection={adminAndMemberCanAction(currentRole?.code)}
                  mappingsFilterProfileField={mappingsFilterProfileField}
                  classNameTable={bulkSelectedAll || bulkValues?.length ? 'pb-[55px]' : ''}
                />
              </div>
            ) : null}

            <CreateCandidateModal open={openCreateCandidate} setOpen={setOpenCreateCandidate} reload={() => refetch()} />

            {switchView.view === 'candidates' ? (
              <Drawer
                position="right"
                size="full"
                drawerClassName="max-w-[calc(100vw-60px)]"
                contentRef={drawerContainerRef}
                open
                onEscapeKeyDown={() => {
                  setRefetchMyList(true)

                  pushStateBrowser({
                    state: {},
                    unused: '',
                    url: configuration.path.candidates.list
                  })
                  setSwitchView({
                    id: undefined,
                    view: '',
                    applicantId: undefined
                  })
                }}
                customCloseButton={<div className="absolute top-4 -left-10 z-50 flex flex-col space-y-4">{renderedActions && renderedActions()}</div>}
              >
                <TopSpaceProvider value={{ top: undefined }}>
                  <CandidateProfileContainer isDrawer={true} {...switchView} setSwitchView={setSwitchView} />
                </TopSpaceProvider>
              </Drawer>
            ) : null}

            <BulkActions
              tableRef={tableRef}
              candidates={data as IPagePagination}
              queryOptions={{ filter: filter || {}, sorting }}
              displayColumns={configUserDisplay || []}
            />
          </div>
        )
      }}
    </SwitchLayoutView>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.job_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.job_management.objects.profile.keyModuleObject],
    action: ACTIONS_PERMISSIONS.show
  },
  CandidatesManagementContainer
)
