import { parseCookies } from 'nookies'
import { useCallback } from 'react'

import configuration from '~/configuration'
import { mappingUserCookie } from '~/cookies/user'
import type { IFormAction } from '~/core/@types/global'
import { SESSION_COOKIE_USER, SESSION_EXPIRES_AT_COOKIE_NAME } from '~/core/constants/cookies'
import { setSessionCookieClient } from '~/core/middleware/save-session-cookie'
import AppRouteLoading from '~/core/ui/AppRouteLoading'
import { uuidV4 } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryUpdateAccountProfileMutation from '~/lib/features/settings/account/graphql/submit-update-account-profile-mutation'
import type { IWelcomeForm } from '~/lib/features/welcome/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import LayoutHybrid from '~/components/Layout/LayoutHybrid'
import WelcomeView from '~/components/Welcome'

const DEFAULT_LANGUAGE = 'en'

const WelcomeContainer = () => {
  const { trigger, isLoading } = useSubmitCommon(QueryUpdateAccountProfileMutation)
  const user = useBoundStore(state => state.user)
  const setUser = useBoundStore(state => state.setUser)
  const { setToast } = useToastStore()
  const cookies = parseCookies()
  const navigateToMainPage = useCallback(() => {
    window.location.href = configuration.path.default
  }, [])
  const submitDataCallback = useCallback(
    async (data: IWelcomeForm, formAction: IFormAction) => {
      if (isLoading) {
        return
      }
      trigger({
        id: Number(user?.id),
        fullName: data.fullName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        language: data.language || DEFAULT_LANGUAGE,
        timezone: data.timezone,
        ...(data.avatar && typeof data.avatar === 'object'
          ? {
              avatar: new File([new Blob([data.avatar || ''])], `avatar-${uuidV4()}.jpeg`)
            }
          : {})
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.account,
            formAction,
            setToast
          })
        }
        const { tenantMemberUpdate } = result.data
        if (tenantMemberUpdate.user) {
          const userCookie = mappingUserCookie({
            ...tenantMemberUpdate.user,
            signInCount: tenantMemberUpdate.user.signInCount + 1
          })
          setSessionCookieClient(SESSION_COOKIE_USER, JSON.stringify(userCookie).toString(), Number(cookies[SESSION_EXPIRES_AT_COOKIE_NAME]))
          setUser({ ...user, ...tenantMemberUpdate.user })

          navigateToMainPage()
        }
        return
      })
    },
    [isLoading, trigger, cookies, setToast, navigateToMainPage]
  )

  return (
    <LayoutHybrid>
      <AppRouteLoading>
        <WelcomeView user={user} setUser={setUser} isLoading={isLoading} onFinish={submitDataCallback} callBack={navigateToMainPage} />
      </AppRouteLoading>
    </LayoutHybrid>
  )
}

export default WelcomeContainer
