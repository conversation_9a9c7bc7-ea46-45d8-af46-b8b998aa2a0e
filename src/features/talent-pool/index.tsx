'use client'

import { createContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import { Divider } from '~/core/ui/Divider'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsTriggerView } from '~/core/ui/Tabs'
import { cn } from '~/core/ui/utils'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { ACTIONS_PERMISSIONS, canAccessFeature, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import { DESC_SORTING } from '~/lib/features/candidates/utilities/enum'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import QueryTalentPoolList from '~/lib/features/talent-pool/graphql/query-talent-pool-list'
import type { IFilterTalentPool } from '~/lib/features/talent-pool/types/talent-pool-type'
import { ACTIVE_TAB, ARCHIVED_TAB } from '~/lib/features/talent-pool/utilities/enum'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'

import { withLayoutGrid } from '~/components/Layout/LayoutGrid'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import AddTalentPoolModal from '~/components/TalentPool/AddTalentPoolModal'
import FilterTalentPoolManagement from '~/components/TalentPool/FilterTalentPoolManagement'
import TalentPoolTable from '~/components/TalentPool/TalentPoolTable'

export const TalentPoolManagementPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const TalentPoolContainer = () => {
  const reportTabControl = useBrowserTab({
    defaultValue: ACTIVE_TAB,
    queryKeyName: 'talent_pool_tabs'
  })
  const { t } = useTranslation()
  const { actionTalentPool } = usePermissionJob()
  const [filter, changeFilter] = useState<IFilterTalentPool | undefined>({})
  const [sorting, setSorting] = useState<{
    createdAt?: string
  }>({
    createdAt: DESC_SORTING
  })
  const [openTalentPoolModal, setOpenTalentPoolModal] = useState(false)

  const { data, refetch, fetchPagination, forceChangeCurrentPage, isFetching } = usePaginationGraphPage({
    queryDocumentNode: QueryTalentPoolList,
    queryKey: 'my-/talent-pools-management',
    filter: {
      ...(filter || {}),
      sorting,
      talentPoolMembersIds: (filter?.talentPoolMembersIds || []).map(item => Number(item.value)),
      status: reportTabControl.value
    }
  })

  useEffect(() => {
    refetch()
  }, [reportTabControl.value])

  const calcHeightWrapper = useClassBasedTopSpace({
    34: 'h-[calc(100vh-34px)]',
    default: 'h-screen'
  })

  return (
    <TalentPoolManagementPermissionContext.Provider value={actionTalentPool}>
      <Tabs {...reportTabControl} className={cn(calcHeightWrapper, 'flex flex-col')}>
        <div className="flex flex-none items-center justify-between pl-7">
          <div className="pt-5">
            <TabsList size="sm">
              <TabsTrigger value={ACTIVE_TAB} size="sm">
                <TabsTriggerView
                  size="sm"
                  session={{
                    value: ACTIVE_TAB,
                    label: `${t('talent_pool:tab:active')}`,
                    count: data?.meta?.totalRowCount
                  }}
                />
              </TabsTrigger>
              <TabsTrigger value={ARCHIVED_TAB} size="sm">
                <TabsTriggerView
                  size="sm"
                  session={{
                    value: ARCHIVED_TAB,
                    label: `${t('talent_pool:tab:archived')}`,
                    count: data?.meta?.totalRowCount
                  }}
                />
              </TabsTrigger>
            </TabsList>
          </div>
          <FilterTalentPoolManagement filter={filter} changeFilter={changeFilter} setOpenTalentPoolModal={setOpenTalentPoolModal} />
        </div>
        <Divider className="flex-none" />
        <TabsContent value={ACTIVE_TAB} className="flex-1 pl-6">
          <TalentPoolTable
            filter={filter}
            data={data}
            fetcher={{
              fetchPagination,
              forceChangeCurrentPage
            }}
            isFetching={isFetching}
            refetch={refetch}
            sorting={sorting}
            setSorting={setSorting}
            settingAction={['view', 'edit', 'archive', 'delete']}
            setOpenTalentPoolModal={setOpenTalentPoolModal}
          />
        </TabsContent>
        <TabsContent value={ARCHIVED_TAB} className="pl-6">
          <TalentPoolTable
            filter={filter}
            data={data}
            fetcher={{
              fetchPagination,
              forceChangeCurrentPage
            }}
            isFetching={isFetching}
            refetch={refetch}
            sorting={sorting}
            setSorting={setSorting}
            settingAction={['view', 'retrieve', 'delete']}
          />
        </TabsContent>
      </Tabs>

      <AddTalentPoolModal open={openTalentPoolModal} setOpen={setOpenTalentPoolModal} callback={() => refetch()} />
    </TalentPoolManagementPermissionContext.Provider>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.job_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.job_management.objects.talent_pool.keyModuleObject],
    action: ACTIONS_PERMISSIONS.show
  },
  withLayoutGrid(TalentPoolContainer)
)
