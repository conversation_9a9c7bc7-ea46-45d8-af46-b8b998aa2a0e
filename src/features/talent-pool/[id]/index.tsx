import { useRouter } from 'next/navigation'
import { createContext, useCallback, useMemo, useRef, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import type { MultiValue } from 'react-select'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import configuration from '~/configuration'
import { openAlert } from '~/core/ui/AlertDialog'
import { AvatarGroup } from '~/core/ui/AvatarGroup'
import { Button } from '~/core/ui/Button'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { DashedButton } from '~/core/ui/DashedButton'
import { DropdownOptionAsync } from '~/core/ui/DropdownOptionAsync'
import type { ISelectOption } from '~/core/ui/Select'
import { Tabs, TabsContent, TabsList, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import { ACTIONS_PERMISSIONS, canAccessFeature, DEFAULT_PERMISSIONS_LIST, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'
import { adminCanAction } from '~/core/utilities/permission'

import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import usePromiseOwnerOptions from '~/lib/features/settings/members/hooks/use-promise-owner-options'
import QueryDeleteTalentPoolMutation from '~/lib/features/talent-pool/graphql/submit-delete-talent-pool'
import QueryUpdateTalentPoolMutation from '~/lib/features/talent-pool/graphql/submit-update-talent-pool'
import useTalentPoolDetailHook from '~/lib/features/talent-pool/hooks/use-talent-pool-detail-hook'
import {
  ACTIVE_TAB,
  ACTIVITIES_TALENT_POOL_DETAIL,
  ANALYTICS_TALENT_POOL_DETAIL,
  ARCHIVED_TAB,
  CANDIDATES_TALENT_POOL_DETAIL,
  NOTES_TALENT_POOL_DETAIL,
  TALENT_POOL_STATES
} from '~/lib/features/talent-pool/utilities/enum'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import { withLayoutGrid } from '~/components/Layout/LayoutGrid'
import LongContentDisplay from '~/components/LongContentDisplay'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import ActivitiesTab from '~/components/TalentPool/ActivitiesTab'
import AnalyticsTab from '~/components/TalentPool/Analytics/AnalyticsTab'
import CandidatesTab from '~/components/TalentPool/CandidatesTab'
import NotesTab from '~/components/TalentPool/NotesTab'
import RetrieveTalentPool from '~/components/TalentPool/RetrieveTalentPool'
import TalentPoolDetailHeadingSkeleton from '~/components/TalentPool/TalentPoolDetailSkeleton'
import UpdateTalentPoolModal from '~/components/TalentPool/UpdateTalentPoolModal'

export const TalentPoolDetailPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const TalentPoolDetailContainer = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { params } = useRouterContext()
  const query = {
    id: params?.id
  }
  const { setRefetchMyList, user, currentRole } = useBoundStore()
  const { setToast } = useToastStore()
  const { canAccessTalentPoolNote, actionTalentPool } = usePermissionJob()
  const { trigger } = useSubmitCommon(QueryUpdateTalentPoolMutation)

  const divRef = useRef<HTMLDivElement>(null)
  const descriptionContentRef = useRef<HTMLDivElement>(null)
  const [openUpdateTalentPool, setOpenUpdateTalentPool] = useState<boolean>(false)

  const tabControl = useBrowserTab({
    defaultValue: 'candidates',
    queryKeyName: 'tabs',
    pushState: false,
    excludeQueryKeysName: ['id']
  })

  const { talentPoolData, fetchTalentPool, updateLocalTalentPool, filterControl } = useTalentPoolDetailHook({
    id: Number(query.id),
    user
  })
  const { promiseMembersOwnerOptions } = usePromiseOwnerOptions({
    avatarSize: 'md',
    disabledOption: [talentPoolData?.createdBy?.id ? String(talentPoolData?.createdBy?.id) : ''],
    jobOwnable: true,
    tooltipDisableOption: `${t('talent_pool:tooltip:can_not_remove_creator')}`
  })
  const { trigger: triggerUpdateTalentPool } = useSubmitCommon(QueryUpdateTalentPoolMutation)
  const { trigger: triggerDeleteTalentPool, isLoading: isLoadingDeleteTalentPool } = useSubmitCommon(QueryDeleteTalentPoolMutation)

  const actionButton = useMemo(() => {
    // Pool members = creator + other members
    const isPoolMember = !!talentPoolData?.members?.find(member => String(member.id) === String(user.id))

    const poolMemberOrAdminRole = adminCanAction(currentRole?.code) || isPoolMember

    return {
      isDisable: !poolMemberOrAdminRole,
      tooltip: poolMemberOrAdminRole ? '' : `${t('tooltip:not_have_permission')}`
    }
  }, [talentPoolData])

  const onRetrieveTalentPool = useCallback(
    () =>
      triggerUpdateTalentPool({
        id: Number(query.id),
        status: ACTIVE_TAB
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error
          })
        }
        setToast({
          open: true,
          type: 'success',
          title: `${t('talent_pool:management:retrieved_success')}`
        })

        return fetchTalentPool()
      }),
    [query]
  )

  const onChangeState = useCallback(
    async (data: ISelectOption | ISelectOption[] | undefined) => {
      if (!data || Array.isArray(data)) return
      trigger({
        id: Number(talentPoolData?.id),
        state: data.value
      }).then(result => {
        if (result.error) {
          return false
        }

        const { talentPoolsUpdate } = result.data
        if (talentPoolsUpdate.talentPool) {
          updateLocalTalentPool({
            ...talentPoolData,
            state: talentPoolsUpdate.talentPool.state
          })
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:talent_pool:update_success')}`
          })
        }

        return true
      })
    },
    [talentPoolData]
  )

  return (
    <TalentPoolDetailPermissionContext.Provider value={actionTalentPool}>
      <div ref={divRef} className="[&>div]:z-10!">
        <Tabs
          className={cn(
            useClassBasedTopSpace({
              34: 'h-[calc(100vh-34px)]',
              default: 'h-screen'
            }),
            'flex flex-col'
          )}
          {...tabControl}
        >
          {!talentPoolData ? (
            <TalentPoolDetailHeadingSkeleton />
          ) : (
            <div className="flex flex-none justify-between space-x-10 border-b border-b-gray-100 px-6">
              <div className="flex-1 pt-4">
                <div className="mb-3">
                  <TypographyText className="text-lg font-semibold text-gray-900">{talentPoolData?.name}</TypographyText>
                  {talentPoolData?.description ? (
                    <div className="mt-1" ref={descriptionContentRef}>
                      <LongContentDisplay
                        isHTML
                        limitLines={2}
                        content={talentPoolData.description}
                        className="max-w-full flex-1 text-sm text-gray-900"
                        textButtonProps={{ size: 'md' }}
                      />
                    </div>
                  ) : null}
                </div>
                <TabsList size="sm">
                  <TabsTrigger gapSize="sm" size="sm" value={CANDIDATES_TALENT_POOL_DETAIL}>
                    <TabsTriggerView
                      size="sm"
                      session={{
                        value: CANDIDATES_TALENT_POOL_DETAIL,
                        label: `${t('label:tab:candidates')}`
                      }}
                    />
                  </TabsTrigger>
                  <TabsTrigger gapSize="sm" size="sm" value={ANALYTICS_TALENT_POOL_DETAIL}>
                    <TabsTriggerView
                      size="sm"
                      session={{
                        value: ANALYTICS_TALENT_POOL_DETAIL,
                        label: `${t('label:tab:analytics')}`
                      }}
                    />
                  </TabsTrigger>
                  {canAccessTalentPoolNote ? (
                    <TabsTrigger gapSize="sm" size="sm" value={NOTES_TALENT_POOL_DETAIL}>
                      <TabsTriggerView
                        size="sm"
                        session={{
                          value: NOTES_TALENT_POOL_DETAIL,
                          label: `${t('label:tab:notes')}`
                        }}
                      />
                    </TabsTrigger>
                  ) : null}
                  <TabsTrigger gapSize="sm" size="sm" value={ACTIVITIES_TALENT_POOL_DETAIL}>
                    <TabsTriggerView
                      size="sm"
                      session={{
                        value: ACTIVITIES_TALENT_POOL_DETAIL,
                        label: `${t('label:tab:activities')}`
                      }}
                    />
                  </TabsTrigger>
                </TabsList>
              </div>
              <div className="py-3">
                {actionTalentPool.update ? (
                  <div className="mb-3.5 flex space-x-2">
                    {talentPoolData.status === ACTIVE_TAB ? (
                      <>
                        <Tooltip
                          content={actionButton.tooltip}
                          classNameConfig={{
                            content: actionButton.tooltip ? '' : 'hidden'
                          }}
                        >
                          <ComboboxSelect
                            closeOnSelect
                            dropdownMenuClassName="right-0"
                            buttonClassName={'min-w-[88px]'}
                            menuOptionAlign="end"
                            isDisabled={actionButton.isDisable}
                            value={
                              talentPoolData?.state
                                ? ({
                                    value: talentPoolData.state,
                                    supportingObj: {
                                      name: t(`talent_pool:state:${talentPoolData.state}`)
                                    }
                                  } as ISelectOption)
                                : undefined
                            }
                            size="sm"
                            isClearable={false}
                            isSearchable={false}
                            onChange={option => {
                              onChangeState(option)
                            }}
                            configSelectOption={{
                              supportingText: ['description']
                            }}
                            options={TALENT_POOL_STATES.map(item => ({
                              value: item.value,
                              supportingObj: {
                                name: t(`talent_pool:state:${item.supportingObj.name}`)
                              }
                            }))}
                          />
                        </Tooltip>

                        <Tooltip
                          content={actionButton.tooltip}
                          classNameConfig={{
                            content: actionButton.tooltip ? '' : 'hidden'
                          }}
                        >
                          <Button
                            type="tertiary"
                            size="xs"
                            isDisabled={actionButton.isDisable}
                            iconMenus="Edit3"
                            onClick={() => setOpenUpdateTalentPool(true)}
                            label={`${t('button:edit')}`}
                          />
                        </Tooltip>
                        <Tooltip
                          content={actionButton.tooltip}
                          classNameConfig={{
                            content: actionButton.tooltip ? '' : 'hidden'
                          }}
                        >
                          <Button
                            type="tertiary"
                            size="xs"
                            isDisabled={actionButton.isDisable}
                            iconMenus="Archive"
                            label={`${t('talent_pool:detail:archive')}`}
                            onClick={() =>
                              triggerUpdateTalentPool({
                                id: Number(query.id),
                                status: ARCHIVED_TAB
                              }).then(result => {
                                if (result.error) {
                                  return catchErrorFromGraphQL({
                                    error: result.error
                                  })
                                }
                                setToast({
                                  open: true,
                                  type: 'success',
                                  title: `${t('talent_pool:management:archived_success')}`
                                })

                                return fetchTalentPool()
                              })
                            }
                          />
                        </Tooltip>
                      </>
                    ) : (
                      <Tooltip
                        content={actionButton.tooltip}
                        classNameConfig={{
                          content: actionButton.tooltip ? '' : 'hidden'
                        }}
                      >
                        <Button
                          type="tertiary"
                          size="xs"
                          isDisabled={actionButton.isDisable}
                          iconMenus="Undo2"
                          label={`${t('talent_pool:detail:retrieve')}`}
                          onClick={onRetrieveTalentPool}
                        />
                      </Tooltip>
                    )}

                    {adminCanAction(currentRole?.code) ? (
                      <Tooltip
                        content={actionButton.tooltip}
                        classNameConfig={{
                          content: actionButton.tooltip ? '' : 'hidden'
                        }}
                      >
                        <Button
                          type="destructive"
                          size="xs"
                          isDisabled={actionButton.isDisable}
                          iconMenus="Trash2"
                          label={`${t('talent_pool:detail:delete')}`}
                          onClick={() => {
                            openAlert({
                              isPreventAutoFocusDialog: false,
                              className: 'w-[480px]',
                              title: `${t('talent_pool:modal:delete_modal:title')}`,
                              description: (
                                <Trans i18nKey="talent_pool:modal:delete_modal:description" values={{ name: talentPoolData?.name }}>
                                  <span className="font-medium" />
                                </Trans>
                              ),
                              actions: [
                                {
                                  label: `${t('button:cancel')}`,
                                  type: 'secondary',
                                  size: 'sm'
                                },
                                {
                                  label: `${t('talent_pool:modal:delete_modal:btn_delete')}`,
                                  type: 'destructive',
                                  size: 'sm',
                                  onClick: async () => {
                                    await triggerDeleteTalentPool({
                                      id: Number(query.id)
                                    }).then(result => {
                                      if (result.error) {
                                        return catchErrorFromGraphQL({
                                          error: result.error
                                        })
                                      }
                                      setToast({
                                        open: true,
                                        type: 'success',
                                        title: `${t('talent_pool:management:deleted_success')}`
                                      })

                                      router.push(configuration.path.talentPool.list)
                                      return
                                    })
                                  }
                                }
                              ]
                            })
                          }}
                        />
                      </Tooltip>
                    ) : null}
                  </div>
                ) : null}
                <div>
                  <AvatarGroup
                    orderPlacement="rightToLeft"
                    size="sm"
                    className="flex-row-reverse space-x-reverse"
                    maxUser={3}
                    toolTipPosition="left"
                    toolTipPositionAvatarCount="left"
                    source={(talentPoolData?.members || []).map(item => ({
                      id: Number(item?.id),
                      alt: item?.fullName || item?.email,
                      src: item.avatarVariants?.thumb?.url,
                      defaultColour: item.defaultColour,
                      tooltip: `${item.fullName || item.email}`
                    }))}
                    extraControl={
                      actionTalentPool.update ? (
                        <DropdownOptionAsync
                          className="w-[320px]"
                          portalContainer={divRef?.current}
                          {...(divRef?.current
                            ? {
                                dropdownMenuContainer: divRef?.current
                              }
                            : {})}
                          configSelect={{
                            loadAsyncWhenRender: true,
                            placeholder: `${t('label:placeholder:search')}`,
                            configSelectOption: {
                              option: 'checkbox',
                              avatar: true,
                              supportingText: ['name', 'description']
                            },
                            classNameOverride: {
                              loadingMessage: `${t('label:loading')}`,
                              noOptionsMessage: `${t('label:noOptions')}`
                            },
                            onChange: newValue => {
                              triggerUpdateTalentPool({
                                id: Number(query.id),
                                talentPoolMembersIds: (newValue as MultiValue<ISelectOption>).map(item => Number(item.value))
                              }).then(() => {
                                const membersTalentPool = (newValue as MultiValue<ISelectOption>).map(item => ({
                                  id: Number(item.value),
                                  fullName: item?.supportingObj?.name || '',
                                  avatarVariants: item.avatarVariants,
                                  defaultColour: item.supportingObj?.defaultColour,
                                  email: item.supportingObj?.description,
                                  roles: [
                                    {
                                      name: item.supportingObj?.helpName || ''
                                    }
                                  ]
                                }))
                                updateLocalTalentPool({
                                  ...talentPoolData,
                                  members: membersTalentPool
                                })
                              })
                            },
                            promiseOptions: promiseMembersOwnerOptions,
                            size: 'sm',
                            value: (talentPoolData.members || []).map(item => ({
                              value: String(item.id),
                              avatar: item.avatarVariants?.thumb?.url,
                              avatarVariants: item.avatarVariants,
                              supportingObj: {
                                name: item.fullName || '',
                                description: item.email,
                                defaultColour: item.defaultColour,
                                helpName: item.roles?.[0]?.name
                              }
                            })),
                            isMulti: true
                          }}
                          side="bottom"
                          isDisabled={actionButton.isDisable}
                          align="end"
                          sideOffset={5}
                          trigger={
                            <Tooltip content={actionButton.isDisable ? actionButton.tooltip : t('tooltip:add_member')}>
                              <div className="relative">
                                <DashedButton iconMenus="Plus" isDisabled={actionButton.isDisable} size="md" className="bg-white" />
                              </div>
                            </Tooltip>
                          }
                        />
                      ) : undefined
                    }
                  />
                </div>
              </div>
            </div>
          )}

          {!!talentPoolData &&
            (talentPoolData?.status === ACTIVE_TAB ? (
              <>
                <TabsContent className="mt-0 pt-3" value={CANDIDATES_TALENT_POOL_DETAIL}>
                  <CandidatesTab talentPoolData={talentPoolData} descriptionContentRef={descriptionContentRef.current} />
                </TabsContent>
                <TabsContent className="mt-0 overflow-x-hidden pt-4" value={ANALYTICS_TALENT_POOL_DETAIL}>
                  <AnalyticsTab user={user} id={Number(query.id)} filterControl={filterControl} name={String(talentPoolData.name)} />
                </TabsContent>
                {canAccessTalentPoolNote ? (
                  <TabsContent className="mt-0 pt-4" value={NOTES_TALENT_POOL_DETAIL}>
                    <NotesTab talentPoolId={Number(query.id)} />
                  </TabsContent>
                ) : null}
                <TabsContent className="mt-0 pt-4" value={ACTIVITIES_TALENT_POOL_DETAIL}>
                  <ActivitiesTab talentPoolId={Number(query.id)} />
                </TabsContent>
                <UpdateTalentPoolModal
                  open={openUpdateTalentPool}
                  setOpen={setOpenUpdateTalentPool}
                  data={talentPoolData}
                  callback={() => {
                    setRefetchMyList(true)
                    fetchTalentPool()
                  }}
                />
              </>
            ) : (
              <RetrieveTalentPool
                retrieveButton={{
                  isDisabled: actionButton.isDisable
                }}
                retrieveButtonTooltip={{
                  content: actionButton.tooltip
                }}
                onClickButton={onRetrieveTalentPool}
              />
            ))}
        </Tabs>
      </div>
    </TalentPoolDetailPermissionContext.Provider>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.job_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.job_management.objects.talent_pool.keyModuleObject],
    action: ACTIONS_PERMISSIONS.show
  },
  withLayoutGrid(TalentPoolDetailContainer)
)
