'use client'

import type { BuiltInProviderType } from 'next-auth/providers'
import type { ClientSafeProvider, LiteralUnion } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { destroyCookie, parseCookies } from 'nookies'
import { useCallback, useEffect, useState } from 'react'

import pathConfiguration from 'src/configuration/path'
import configuration from '~/configuration'
import type { IFormAction } from '~/core/@types/global'
import { COOKIE_PATH, SESSION_ERROR_MESSAGE } from '~/core/constants/cookies'
import { useRecaptcha, verifyRecaptcha } from '~/core/hooks/use-verify-captcha'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useMiddlewareRequest from '~/core/middleware/use-middleware-request'
import AppRouteLoading from '~/core/ui/AppRouteLoading'
import { catchErrorFromGraphQL, catchErrorFromRequest } from '~/core/utilities/catch-api-error'

import QueryTenant from '~/lib/features/careers/[id]/graphql/query-tenant'
import type { TenantType } from '~/lib/features/careers/[id]/types'
import type { IRegisterForm } from '~/lib/features/register/types'
import { useTrackingUTM } from '~/lib/hooks/use-tracking-utm'
import { useRouterContext } from '~/lib/next/use-router-context'
import useToastStore from '~/lib/store/toast'

import CareerHubRegisterView from '~/components/CareerHub/Register'
import LayoutHybrid from '~/components/Layout/LayoutHybrid'

const CareerHubRegisterContainer: React.FC<{
  providers?: Record<LiteralUnion<BuiltInProviderType, string>, ClientSafeProvider> | null
}> = ({ providers }) => {
  useRecaptcha()
  const { utm_campaign, utm_source, utm_medium, utm_content, isHasParam } = useTrackingUTM()
  const { clientGraphQL } = useContextGraphQL()
  const router = useRouter()
  const { params } = useRouterContext()
  const query = {
    slug: params?.slug ? decodeURIComponent(String(params.slug)) : undefined
  }
  const [tenant, setTenant] = useState<TenantType>()
  const { setToast } = useToastStore()
  const fetchTenant = useCallback(() => {
    return clientGraphQL
      .query(QueryTenant, {
        tenantSlug: query.slug
      })
      .toPromise()
      .then((result: { error: any; data: { publicTenantsShow: TenantType } }) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            error404ShouldForceToNotFoundPage: true
          })
        }

        const { publicTenantsShow } = result.data
        setTenant(publicTenantsShow)

        return publicTenantsShow
      })
  }, [clientGraphQL, query.slug])

  useEffect(() => {
    fetchTenant()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  //check show error message when sign up with google and microsoft account
  useEffect(() => {
    const checkShowSocialRegisterErrMessage = () => {
      const cookies = parseCookies()
      const message = cookies[SESSION_ERROR_MESSAGE]
        ? (
            JSON.parse(cookies[SESSION_ERROR_MESSAGE]) as {
              errors?: Array<{
                message: string
                extensions: {
                  error_code: string
                  http_code: number
                  errors: Array<{
                    field: string
                    message: string
                  }>
                }
              }>
            }
          )?.errors?.[0]?.extensions?.errors?.[0]?.message
        : ''

      if (message) {
        setToast({
          open: true,
          type: 'error',
          title: message
        })

        destroyCookie(null, SESSION_ERROR_MESSAGE, {
          path: COOKIE_PATH
        })
      }
    }

    checkShowSocialRegisterErrMessage()
    addEventListener('pageshow', checkShowSocialRegisterErrMessage)

    return () => {
      removeEventListener('pageshow', checkShowSocialRegisterErrMessage)
    }
  }, [])

  const { trigger, isMutating: isLoading } = useMiddlewareRequest({
    endpoint: configuration.api.CHUBlogin,
    method: 'POST'
  })

  const navigateToVerifyEmailPage = useCallback(
    (data: IRegisterForm) => {
      router.push(`${pathConfiguration.careerHub.verify_email(query.slug as string)}?email=${data.email}`)
    },
    [query]
  )

  const registerCallback = useCallback(
    async (data: IRegisterForm, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      verifyRecaptcha(async (isNotGoogleBOT: boolean) => {
        if (isNotGoogleBOT) {
          try {
            await trigger({
              ...data,
              ...(isHasParam ? { utm_campaign, utm_source, utm_medium, utm_content } : {}),
              is_register: true,
              tenant_slug: query.slug,
              provider: 'web'
            })
            navigateToVerifyEmailPage(data)
          } catch (error) {
            catchErrorFromRequest({
              error,
              formAction,
              callbackHandleStatusError422: keys => {
                keys.forEach((session: { field: string; message: string }) => {
                  formAction.setError('email', {
                    type: 'custom',
                    message: [session.message]
                  })
                })
              }
            })
          }
        }
      })
    },
    [isLoading, trigger, isHasParam, utm_campaign, utm_source, utm_medium, utm_content, navigateToVerifyEmailPage]
  )

  return (
    <LayoutHybrid>
      <AppRouteLoading>
        <CareerHubRegisterView tenant={tenant} isLoadingEmail={isLoading} providers={providers} onFinish={registerCallback} />
      </AppRouteLoading>
    </LayoutHybrid>
  )
}

export default CareerHubRegisterContainer
