'use client'

import { useEffect } from 'react'

import pathConfiguration from 'src/configuration/path'
import { ASC_SORTING, DESC_SORTING } from '~/core/constants/enum'
import type { ISelectOption } from '~/core/ui/Select'
import { buildURL } from '~/core/utilities/common'
import { pushStateBrowser } from '~/core/utilities/is-browser'

import useLearningLibrary from '~/lib/features/career-hub/hook/use-learning-library'
import type { CourseFilter } from '~/lib/features/career-hub/types'
import useBoundStore from '~/lib/store'

import FilterCourses from '~/components/CareerHub/[id]/FilterCourses'
import LearningLibrary from '~/components/CareerHub/LearningLibrary'
import LayoutGridCareerHub from '~/components/Layout/LayoutGridCareerHub'

const CareerHubLearningLibraryContainer = (props: CourseFilter & { sort: string }) => {
  const {
    filterControl,
    refetch,
    getCoursesWithPaging,
    providerOptions,
    levelOptions,
    typeOptions,
    languagesOptions,
    priceOptions,
    isResetting,
    setSorting,
    sorting
  } = useLearningLibrary({
    ...props
  })

  const { user } = useBoundStore()

  useEffect(() => {
    const keys = Object.keys(filterControl?.value ?? {})

    let params: { [x: string]: any }[] = []

    if (keys.length > 0) {
      params = keys
        .filter((key: string) => key !== 'isFilterTouched')
        .map((key: string) => {
          const filterItem = (filterControl?.value as Record<string, any>)?.[key]

          if (Array.isArray(filterItem) && filterItem?.length) {
            return { [key]: filterItem.map((fi: ISelectOption) => fi.value) }
          }
          return {
            [key]: (filterControl?.value as Record<string, any>)?.[key]
          }
        })
    }

    const sort = sorting?.createdAt === DESC_SORTING ? 'newest' : sorting?.createdAt === ASC_SORTING ? 'oldest' : 'relevant'

    const url = buildURL(pathConfiguration.careerHub.library(user?.currentTenant?.slug || ''), Object.assign({}, ...params, { sort }))

    pushStateBrowser({
      state: {},
      unused: '',
      url: url
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterControl?.value, sorting])

  return (
    <LayoutGridCareerHub>
      <div className="mx-auto h-full min-h-[calc(100vh-114px)] max-w-[1216px] py-4">
        <FilterCourses
          extras={getCoursesWithPaging?.data?.pages[0]?.tenantCoursesList?.metadata?.extras}
          filterControl={filterControl}
          filtersOptions={{
            providerOptions,
            levelOptions,
            typeOptions,
            languagesOptions
          }}
          viewList={
            <LearningLibrary
              filterControl={filterControl}
              getCourse={getCoursesWithPaging}
              refetch={refetch}
              filtersOptions={{
                providerOptions,
                levelOptions,
                typeOptions,
                languagesOptions,
                priceOptions
              }}
              isResetting={isResetting}
              setSorting={setSorting}
              sorting={sorting}
            />
          }
        />
      </div>
    </LayoutGridCareerHub>
  )
}

export default CareerHubLearningLibraryContainer
