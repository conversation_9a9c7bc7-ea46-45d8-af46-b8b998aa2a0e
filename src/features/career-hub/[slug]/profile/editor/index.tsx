'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IUserInformation } from '~/core/@types/global'
import { ScrollArea } from '~/core/ui/ScrollArea'

import { getsBase64FromImageURL } from '~/lib/features/candidates/utilities/export'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import useResumes from '~/lib/features/settings/profiles/edit/hooks/use-resumes'
import { mappingPrepareFromQuery } from '~/lib/features/settings/profiles/edit/mapping'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'

import ResumesEditorContentView from '~/components/Resumes/Editor/Content/ResumesEditorContentView'
import ResumesPreviewTemplatesWrapper from '~/components/Resumes/PreviewTemplates/ResumesPreviewTemplatesWrapper'
import TemplatePDFInitialize from '~/components/Resumes/PreviewTemplates/Templates/TemplatePDFInitialize'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'

const CareerHubResumesEditCVContainer = ({ user }: { user: IUserInformation }) => {
  const { t, i18n } = useTranslation()
  const router = useRouter()
  const { resumeData, setResume } = useResumeStore()
  const employeeId = Number(user.id)

  const { data: customFieldViewData } = useCustomFieldSettingByUser({
    objectKind: 'profile',
    employeeId
  })

  const [isLoadingProfileTemplate, setLoadingProfileTemplate] = useState(false)
  const [isLoadingSaveFiles, setLoadingSaveFile] = useState(false)
  const { resumeData: resumeDataAPI, triggerGetResume } = useResumes({
    employeeId,
    t
  })
  const scrollRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    triggerGetResume()

    return () => {
      setResume(undefined)
    }
  }, [])

  useEffect(() => {
    if (resumeDataAPI !== undefined) {
      if (resumeDataAPI === null) {
        router.push(configuration.path.errorAccessDenied)
      } else {
        const mappingData = async () => {
          const mappingData = mappingPrepareFromQuery(resumeDataAPI)
          const logoVariantsUrl = user.currentTenant?.logoVariants?.medium?.url || user.currentTenant?.logoVariants?.thumb?.url
          const avatarVariantsUrl =
            mappingData?.permittedFields?.avatar?.value?.medium?.url || mappingData?.permittedFields?.avatar?.value?.thumb?.url

          const endpointsUrl = []
          if (logoVariantsUrl) {
            endpointsUrl.push({
              logoBase64: true,
              url: logoVariantsUrl
            })
          }

          if (avatarVariantsUrl) {
            endpointsUrl.push({
              avatarBase64: true,
              url: avatarVariantsUrl
            })
          }

          const imageBase64 = await getsBase64FromImageURL(endpointsUrl)

          setResume({
            ...mappingData,
            ...imageBase64
          })
        }

        mappingData()
      }
    }

    return () => {
      setResume(undefined)
    }
  }, [resumeDataAPI, setResume])

  // -------- FUNCTIONS --------

  return (
    <div className="flex">
      <div className="w-[45%]">
        <ScrollArea
          scrollRef={scrollRef}
          className="flex-1"
          viewportClassName="h-full w-full rounded-[inherit] [&>*:first-child]:block! h-[calc(100vh-56px)] flex-1 overflow-y-auto border-l border-l-gray-100 pb-5 px-8"
          scrollBarClassName="mx-1 py-1"
          rootStyle={{
            height: '100vh'
          }}
        >
          <ResumesEditorContentView
            scrollRef={scrollRef}
            id={Number(resumeData?.id)}
            currentUserId={employeeId}
            customFieldViewData={customFieldViewData}
            isLoadingProfileTemplate={isLoadingProfileTemplate}
            isLoadingSaveFiles={isLoadingSaveFiles}
          />
        </ScrollArea>
      </div>

      <div className="relative w-[55%] min-w-[636px] bg-gray-500">
        <TemplatePDFInitialize language={i18n.language}>
          {({ pdf }) => (
            <>
              {resumeData !== undefined ? (
                <ResumesPreviewTemplatesWrapper
                  pdf={pdf}
                  viewportClassName="h-full w-full rounded-[inherit] [&>*:first-child]:block! px-5 pb-5 pt-[85px] border-l border-l-gray-100"
                  isShowSaveToFile
                  isEmployee
                  employeeId={employeeId}
                  resumeData={resumeData}
                  setResume={setResume}
                  customFieldViewData={customFieldViewData}
                  isLoadingProfileTemplate={isLoadingProfileTemplate}
                  setLoadingProfileTemplate={setLoadingProfileTemplate}
                  isLoadingSaveFiles={isLoadingSaveFiles}
                  setLoadingSaveFile={setLoadingSaveFile}
                  companyName={user?.currentTenant?.name}
                />
              ) : null}
            </>
          )}
        </TemplatePDFInitialize>
      </div>
    </div>
  )
}

export default withSubscriptionPlanLockFearture(CareerHubResumesEditCVContainer, PLAN_FEATURE_KEYS.employee_profile as FeatureName)
