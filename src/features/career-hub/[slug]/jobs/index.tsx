import useOpenJobsManagement from '~/lib/features/referrals/hooks/use-open-jobs-management'
import type { IOpenJobsManagementFilter } from '~/lib/features/referrals/types'
import useReferralSetting from '~/lib/features/settings/referrals/hooks/useReferralSetting'

import FilterJobCareerHub from '~/components/CareerHub/[id]/FilterJobCareerHub'
import OpenJobs from '~/components/CareerHub/[id]/OpenJobs'
import LayoutGridCareerHub from '~/components/Layout/LayoutGridCareerHub'

const CareerHubContainer = (props: IOpenJobsManagementFilter) => {
  const { openJobsPaging, filterControl, refetch } = useOpenJobsManagement(props)
  const { dataReferral } = useReferralSetting()
  const referralPortal = dataReferral?.values?.referral_portal

  return (
    <LayoutGridCareerHub>
      <div className="mx-auto max-w-[1216px] pt-6 pb-10">
        <FilterJobCareerHub
          filterControl={filterControl}
          referral_only={referralPortal?.referral_only}
          extras={openJobsPaging.data?.pages?.[0]?.jobsReferableList?.metadata?.extras}
          viewList={<OpenJobs filterControl={filterControl} openJobsPaging={openJobsPaging} refetch={refetch} />}
        />
      </div>
    </LayoutGridCareerHub>
  )
}

export default CareerHubContainer
