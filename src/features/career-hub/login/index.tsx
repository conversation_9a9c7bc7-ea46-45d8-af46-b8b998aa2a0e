'use client'

import type { BuiltInProviderType } from 'next-auth/providers'
import type { ClientSafeProvider, LiteralUnion } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { destroyCookie } from 'nookies'
import { useCallback, useEffect, useState } from 'react'

import pathConfiguration from 'src/configuration/path'
import configuration from '~/configuration'
import type { IFormAction } from '~/core/@types/global'
import { COOKIE_PATH, SESSION_COOKIE_SSO } from '~/core/constants/cookies'
import { useRecaptcha, verifyRecaptcha } from '~/core/hooks/use-verify-captcha'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import useMiddlewareRequest from '~/core/middleware/use-middleware-request'
import AppRouteLoading from '~/core/ui/AppRouteLoading'
import { catchErrorFromGraphQL, catchErrorFromRequest } from '~/core/utilities/catch-api-error'

import QueryTenant from '~/lib/features/careers/[id]/graphql/query-tenant'
import type { TenantType } from '~/lib/features/careers/[id]/types'
import type { ILoginForm } from '~/lib/features/login/types'
import { useTrackingUTM } from '~/lib/hooks/use-tracking-utm'
import { useRouterContext } from '~/lib/next/use-router-context'
import useToastStore from '~/lib/store/toast'

import CareerHubLoginView from '~/components/CareerHub/Login'
import LayoutHybrid from '~/components/Layout/LayoutHybrid'

const CareerHubLoginContainer: React.FC<{
  providers?: Record<LiteralUnion<BuiltInProviderType, string>, ClientSafeProvider> | null
}> = ({ providers }) => {
  useRecaptcha()
  const { clientGraphQL } = useContextGraphQL()
  const router = useRouter()
  const { params } = useRouterContext()
  const query = {
    slug: params?.slug ? decodeURIComponent(String(params.slug)) : undefined
  }

  const [tenant, setTenant] = useState<TenantType>()
  const { setToast } = useToastStore()
  const fetchTenant = useCallback(() => {
    return clientGraphQL
      .query(QueryTenant, {
        tenantSlug: query.slug
      })
      .toPromise()
      .then((result: { error: any; data: { publicTenantsShow: TenantType } }) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            error404ShouldForceToNotFoundPage: true
          })
        }

        const { publicTenantsShow } = result.data
        setTenant(publicTenantsShow)

        return publicTenantsShow
      })
  }, [clientGraphQL, query.slug])

  useEffect(() => {
    fetchTenant()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const { utm_campaign, utm_source, utm_medium, utm_content, isHasParam } = useTrackingUTM()

  const { trigger: loginWithEmail, isMutating: isLoading } = useMiddlewareRequest({
    endpoint: configuration.api.CHUBlogin,
    method: 'POST'
  })

  useEffect(() => {
    const options = { path: COOKIE_PATH }
    destroyCookie(null, SESSION_COOKIE_SSO, options)
  }, [])

  const navigateToVerifyEmailPage = useCallback(
    (data: ILoginForm) => {
      router.push(`${pathConfiguration.careerHub.verify_email(query.slug as string)}?email=${data.email}`)
    },
    [query]
  )

  const loginWithEmailCallback = useCallback(
    async (data: ILoginForm, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      verifyRecaptcha(async (isNotGoogleBOT: boolean) => {
        if (isNotGoogleBOT) {
          try {
            await loginWithEmail({
              ...data,
              ...(isHasParam ? { utm_campaign, utm_source, utm_medium, utm_content } : {}),
              provider: 'web',
              is_register: false,
              tenant_slug: query.slug
            })
            navigateToVerifyEmailPage(data)
          } catch (error) {
            catchErrorFromRequest({
              error,
              formAction,
              callbackHandleStatusError422: keys => {
                keys.forEach((session: { field: string; message: string }) => {
                  formAction.setError('email', {
                    type: 'custom',
                    message: [session.message]
                  })
                })
              }
            })
          }
        }
      })
    },
    [isLoading, loginWithEmail, isHasParam, utm_campaign, utm_source, utm_medium, utm_content, navigateToVerifyEmailPage]
  )

  return (
    <LayoutHybrid>
      <AppRouteLoading>
        <CareerHubLoginView tenant={tenant} isLoadingEmail={isLoading} providers={providers} onFinish={loginWithEmailCallback} />
      </AppRouteLoading>
    </LayoutHybrid>
  )
}

export default CareerHubLoginContainer
