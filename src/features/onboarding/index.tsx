import Gleap from 'gleap'
import { useRouter } from 'next/navigation'
import { parseCookies } from 'nookies'
import { useCallback } from 'react'

import configuration from '~/configuration'
import { mappingCurrentTenantCookie } from '~/cookies/currentTenant'
import { mappingTenantsCookie } from '~/cookies/tenants'
import { mappingUserCookie } from '~/cookies/user'
import type { IFormAction } from '~/core/@types/global'
import {
  SESSION_COOKIE_CURRENT_TENANT,
  SESSION_COOKIE_CURRENT_TENANT_EXT,
  SESSION_COOKIE_EXT,
  SESSION_COOKIE_NAME,
  SESSION_COOKIE_ROLE_EXT,
  SESSION_COOKIE_TENANTS,
  SESSION_COOKIE_USER,
  SESSION_EXPIRES_AT_COOKIE_NAME
} from '~/core/constants/cookies'
import { setSessionCookieClient, setSessionCookieExtClient } from '~/core/middleware/save-session-cookie'
import AppRouteLoading from '~/core/ui/AppRouteLoading'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { sendChromeRuntimeMessage } from '~/core/utilities/common'

import QueryOnboardingMutation from '~/lib/features/onboarding/graphql/submit-mutation'
import type { IOnboardingForm } from '~/lib/features/onboarding/types'
import { useSubmitSignOut } from '~/lib/features/sign-out/hooks/use-submit-sign-out'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import LayoutHybrid from '~/components/Layout/LayoutHybrid'
import OnboardingView from '~/components/Onboarding'

const OnboardingContainer = () => {
  const router = useRouter()
  const { trigger, isLoading } = useSubmitCommon(QueryOnboardingMutation, {
    notUpdateLoadingAfterMutation: true
  })
  const user = useBoundStore(state => state.user)
  const { setToast } = useToastStore()
  const cookies = parseCookies()

  const { handleSignOut } = useSubmitSignOut()
  const signOut = useCallback(async () => {
    await handleSignOut()
  }, [])

  const navigateToMainPage = useCallback(() => {
    router.push(configuration.path.default)
  }, [])

  const submitDataCallback = useCallback(
    async (data: IOnboardingForm, formAction: IFormAction) => {
      if (isLoading) {
        return
      }
      Gleap.clearIdentity()
      trigger({
        name: data.name,
        phoneNumber: data.phoneNumber,
        companySize: data.companySize,
        companyKind: 'employer'
      }).then(result => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            formAction,
            setToast
          })
        }

        const { tenantOnboard } = result.data
        if (tenantOnboard?.user) {
          Gleap.identify(tenantOnboard.user.id, {
            name: tenantOnboard.user.fullName,
            email: tenantOnboard.user.email,
            companyId: tenantOnboard.currentTenant?.id,
            // @ts-ignore - doesn't need to fix
            companyName: tenantOnboard.currentTenant?.name,
            plan: tenantOnboard.currentTenant?.tenantPlan?.name,
            customData: {
              type: tenantOnboard?.user?.ownTenant
                ? `${tenantOnboard.currentTenant?.companyKind}-owner`
                : `${tenantOnboard.currentTenant?.companyKind}`,
              companyId: tenantOnboard.currentTenant?.id,
              companyName: tenantOnboard.currentTenant?.name
            }
          })
          const userCookie = mappingUserCookie(tenantOnboard.user)

          setSessionCookieClient(SESSION_COOKIE_USER, JSON.stringify(userCookie).toString(), Number(cookies[SESSION_EXPIRES_AT_COOKIE_NAME]))
        }
        if (tenantOnboard.user.tenants.length) {
          const payload = mappingTenantsCookie(tenantOnboard.user.tenants)

          setSessionCookieClient(SESSION_COOKIE_TENANTS, JSON.stringify(payload).toString(), Number(cookies[SESSION_EXPIRES_AT_COOKIE_NAME]))
        }

        if (tenantOnboard.currentTenant) {
          const obj = {
            ...tenantOnboard.currentTenant,
            careerSiteSettings: tenantOnboard.currentTenant.careerSiteSettings
          }
          const currentTenantObject = mappingCurrentTenantCookie(obj)

          setSessionCookieClient(
            SESSION_COOKIE_CURRENT_TENANT,
            JSON.stringify(currentTenantObject).toString(),
            Number(cookies[SESSION_EXPIRES_AT_COOKIE_NAME])
          )
          setSessionCookieExtClient(
            SESSION_COOKIE_CURRENT_TENANT_EXT,
            JSON.stringify(currentTenantObject).toString(),
            Number(cookies[SESSION_EXPIRES_AT_COOKIE_NAME])
          )
        }

        const rolesObj = { data: tenantOnboard.user.roles || [] }
        const currentRoleFilter = rolesObj.data.filter((item: { id: string }) => item.id)
        const currentRoleName = currentRoleFilter.length ? currentRoleFilter?.[0]?.name : ''
        setSessionCookieExtClient(
          SESSION_COOKIE_ROLE_EXT,
          JSON.stringify(currentRoleName).toString(),
          Number(cookies[SESSION_EXPIRES_AT_COOKIE_NAME])
        )

        sendChromeRuntimeMessage({
          [SESSION_COOKIE_EXT]: cookies[SESSION_COOKIE_NAME],
          [SESSION_COOKIE_CURRENT_TENANT_EXT]: JSON.stringify(tenantOnboard.currentTenant).toString(),
          [SESSION_COOKIE_ROLE_EXT]: JSON.stringify(currentRoleName).toString()
        }).then(() => navigateToMainPage())

        return
      })
    },
    [isLoading, trigger, cookies, setToast, navigateToMainPage]
  )

  return (
    <LayoutHybrid>
      <AppRouteLoading>
        <OnboardingView user={user} isLoading={isLoading} onFinish={submitDataCallback} signOut={signOut} />
      </AppRouteLoading>
    </LayoutHybrid>
  )
}

export default OnboardingContainer
