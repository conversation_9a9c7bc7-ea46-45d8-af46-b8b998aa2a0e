'use client'

import { formatISO } from 'date-fns'
import type { ComponentProps } from 'react'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useMutation } from 'urql'

import { AGENCY_TENANT } from '~/core/constants/enum'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import IconWrapper from '~/core/ui/IconWrapper'
import type { ISelectOption } from '~/core/ui/Select'
import { Tooltip } from '~/core/ui/Tooltip'

import usePermissionPlacement from '~/lib/features/permissions/hooks/use-permission-placement'
import type { IPlacementCustomField } from '~/lib/features/placements/types/management-page-type'
import { removeEmptyField } from '~/lib/features/placements/utilities'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import QueryAgencyPlacementFormFieldsList from '~/lib/features/settings/profile-fields/graphql/query-agency-placement-form-fields'
import { formatInitialValueCustomField, formatSubmitCustomFieldData } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import createModalHook from '~/lib/hooks/create-modal-hooks'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useUserCheckKindOf } from '~/lib/hooks/use-user-check-kind'
import useToastStore from '~/lib/store/toast'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import usePlacementStatusWithDotColor from '../hooks/use-placement-status-with-dot-color'
import type { IPlacement } from '../placement'
import type { ICreateEditPlacement } from '../schema'
import { editAgencyPlacementMutation } from './graphql/mutation-agency-edit-placement'
import PlacementForm from './PlacementForm'
import ToolTipOnOverflow from './ToolTipOnOverflow'

const EditPlacementDialog = (props: {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit?: (data: ICreateEditPlacement) => Promise<IPlacement>
  openParams?: {
    onPlacementEdited: (placement: IPlacement) => void
    header?: { candidateName?: string; jobTitle?: string; companyName?: string }
    placement?: IPlacement
    profileId?: ComponentProps<typeof PlacementForm>['profileId']
  }
}) => {
  const { t } = useTranslation()
  const { open, setOpen, onSubmit, openParams } = props
  const typeOfStatus = usePlacementStatusWithDotColor()
  const [isLoading, setLoading] = useState(false)
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const { userIsAsClient } = useUserCheckKindOf()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const isShowPlacementFeature = isFeatureEnabled(PLAN_FEATURE_KEYS.placement) && isUnLockFeature(PLAN_FEATURE_KEYS.placement)
  const { canAccessPage: canAccessPlacement } = usePermissionPlacement()
  const isAccessPlacement = isShowPlacementFeature && canAccessPlacement

  /** handle customFields **/
  const { data: dataPlacementCustomFields, trigger: triggerPlacementFormFieldsList } = useQueryGraphQL({
    query: QueryAgencyPlacementFormFieldsList,
    variables: {},
    shouldPause: true
  })

  useEffect(() => {
    if ((isCompanyKind && !userIsAsClient()) || isAccessPlacement) {
      triggerPlacementFormFieldsList()
    }
  }, [])

  const placementCustomFields = useMemo(() => {
    return (dataPlacementCustomFields?.placementFieldsSettingList || []).filter((field: IPlacementCustomField) => field.field_level == 'custom')
  }, [dataPlacementCustomFields])

  const placementSystemFields = useMemo(() => {
    return (dataPlacementCustomFields?.placementFieldsSettingList || []).filter((field: IPlacementCustomField) => field.field_level == 'system')
  }, [dataPlacementCustomFields])

  const customFieldsFormatted = useMemo(() => {
    const customFieldsValue = openParams?.placement?.customFields || []

    return formatInitialValueCustomField(
      placementCustomFields.map((field: IPlacementCustomField) => {
        const findItem = customFieldsValue.filter(f => Number(f.customSettingId) === Number(field.custom_setting_id))?.[0]

        return {
          ...field,
          value: findItem?.value || '',
          selectedOptionKeys: findItem?.selectedOptionKeys || []
        }
      })
    )
  }, [placementCustomFields, openParams?.placement])

  /** END **/

  const defaultValue = useMemo<ICreateEditPlacement | undefined>(
    () =>
      openParams?.placement
        ? ({
            id: openParams?.placement?.id?.toString(),
            applicantId: openParams?.placement?.permittedFields?.applicant?.value?.id.toString(),
            status: typeOfStatus.find((item: ISelectOption) => item.value === openParams?.placement?.permittedFields?.status?.value),
            jobStageId: openParams?.placement?.permittedFields?.jobStage?.value?.id.toString(),
            hiredDate: openParams?.placement?.permittedFields?.hiredDate?.value && new Date(openParams?.placement?.permittedFields?.hiredDate?.value),
            ...removeEmptyField({
              onboardDate:
                openParams?.placement?.permittedFields?.onboardDate?.value && new Date(openParams?.placement?.permittedFields?.onboardDate?.value),
              endOfProbationDate:
                openParams?.placement?.permittedFields?.endOfProbationDate?.value &&
                new Date(openParams?.placement?.permittedFields?.endOfProbationDate?.value),
              salary: openParams?.placement?.permittedFields?.salary?.value
                ? openParams?.placement?.permittedFields.salary?.value.toString()
                : undefined
            }),
            typeOfSalary: openParams?.placement?.permittedFields?.typeOfSalary?.value,
            currencyOfSalary: openParams?.placement?.permittedFields?.currencyOfSalary?.value,
            fee: openParams?.placement?.permittedFields?.fee?.value || '',
            typeOfFee: openParams?.placement?.permittedFields?.typeOfFee?.value,
            revenue: openParams?.placement?.permittedFields?.revenue?.value || '',
            currencyOfRevenue: openParams?.placement?.permittedFields?.currencyOfRevenue?.value,
            profitSplits:
              (openParams?.placement?.permittedFields?.profitSplits?.value || []).map(item => ({
                id: +item.id,
                user_id: {
                  avatar: item.user?.avatar,
                  avatarVariants: item.user?.avatarVariants,
                  value: item.user?.id?.toString(),
                  supportingObj: { name: item.user?.fullName }
                },
                profit_percentage: item.profitPercentage.toString()
              })) || [],
            hiredBy: {
              value: openParams?.placement?.permittedFields?.hiredBy?.value?.id?.toString(),
              supportingObj: {
                name: openParams?.placement?.permittedFields?.hiredBy?.value?.fullName
              }
            },
            notes: openParams?.placement?.permittedFields?.comments?.value?.[0]?.content || undefined,
            noteId: openParams?.placement?.permittedFields?.comments?.value?.[0]?.id
              ? Number(openParams?.placement?.permittedFields?.comments?.value?.[0]?.id)
              : undefined,
            customFields: customFieldsFormatted
          } as ICreateEditPlacement)
        : undefined,
    [openParams?.placement]
  )

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
      isDivider={true}
      isPreventAutoFocusDialog={true}
      size="md"
      label={`${t('placements:editPlacement')}`}
      description={
        <div>
          <div className="flex items-center">
            <div className="mr-2">
              <IconWrapper size={16} name="ClipboardList" />
            </div>
            <Tooltip content={`${t('tooltip:placementId')}`}>
              <div className="line-clamp-1 text-sm text-gray-900">{openParams?.placement?.permittedFields?.publicId?.value || ''}</div>
            </Tooltip>
          </div>
          <div className="mt-1.5 flex items-center">
            <div className="mr-2">
              <IconWrapper size={16} name="User" />
            </div>
            <Tooltip content={openParams?.header?.candidateName}>
              <div className="line-clamp-1 text-sm text-gray-900">{openParams?.header?.candidateName}</div>
            </Tooltip>
          </div>
          <div className="mt-1.5 flex items-center">
            <div className="mr-2 shrink-0">
              <IconWrapper size={16} name="Briefcase" />
            </div>
            <ToolTipOnOverflow text={openParams?.header?.jobTitle} className="max-w-[200px] shrink-0 truncate text-sm text-gray-900" />
            <div className="mr-2 ml-2 h-0.5 w-0.5 shrink-0 rounded-full bg-gray-400"></div>
            <ToolTipOnOverflow className="shrink-1 truncate" text={openParams?.header?.companyName} />
          </div>
        </div>
      }
    >
      {defaultValue && (
        <PlacementForm
          placementSystemFields={placementSystemFields}
          placementCustomFields={placementCustomFields}
          onSubmit={data => {
            if (onSubmit) {
              setLoading(true)

              return onSubmit({
                ...data,
                noteId: openParams?.placement?.permittedFields?.comments?.value?.[0]?.id
                  ? Number(openParams?.placement?.permittedFields?.comments?.value?.[0]?.id)
                  : undefined
              }).then(placement => {
                setLoading(false)
                setOpen(false)
                openParams?.onPlacementEdited && openParams?.onPlacementEdited(placement)
              })
            }

            return Promise.reject('Handler not provided')
          }}
          defaultValue={defaultValue}
          profileId={openParams?.profileId}
          applicant={{
            ...openParams?.placement?.permittedFields?.applicant?.value,
            id: Number(openParams?.placement?.permittedFields?.applicant?.value?.id),
            hiredDate: openParams?.placement?.permittedFields?.hiredDate?.value
              ? new Date(openParams?.placement?.permittedFields?.hiredDate?.value)
              : undefined,
            createdBy: openParams?.placement?.permittedFields?.createdBy?.value
          }}
        >
          <div className="mt-6 flex items-center justify-end space-x-3">
            <Button label={`${t('button:cancel')}`} size="sm" type="secondary" onClick={() => setOpen(false)} />
            <Button isDisabled={isLoading} isLoading={isLoading} size="sm" label={`${t('interview:email_modal:save')}`} htmlType="submit" />
          </div>
        </PlacementForm>
      )}
    </Dialog>
  )
}
export const useEditPlacementModel = () => {
  const { t } = useTranslation()
  const [{ fetching: creatingRequisition }, editPlacement] = useMutation(editAgencyPlacementMutation)
  const { setToast } = useToastStore()
  const { ModalComponent: EditPlacementModalComponent, openModal: openEditPlacementModel } = createModalHook(EditPlacementDialog)(data => {
    return editPlacement({
      id: data.id ? +data.id : undefined,
      applicantId: +data.applicantId,
      jobStageId: +data.jobStageId,
      status: data.status?.value,
      hiredDate: formatISO(data.hiredDate),
      onboardDate: data.onboardDate ? formatISO(data.onboardDate) : null,
      endOfProbationDate: data.endOfProbationDate ? formatISO(data.endOfProbationDate) : null,
      salary: data.salary ? +data.salary : null,
      typeOfSalary: data.typeOfSalary,
      currencyOfSalary: 'USD',
      fee: data.fee ? +data.fee : null,
      typeOfFee: data.typeOfFee,
      revenue: data.revenue ? +data.revenue : null,
      currencyOfRevenue: data.currencyOfRevenue && data.currencyOfRevenue,
      profitSplits: data.profitSplits.map(item => ({
        userId: +item.user_id?.value,
        profitPercentage: +item.profit_percentage,
        id: item.id ? +item.id : undefined,
        _destroy: item._destroy
      })),
      hiredById: Number(data?.hiredBy?.value),
      noteId: data?.noteId ? Number(data?.noteId) : undefined,
      noteContent: data?.notes?.trim() ? data?.notes?.trim() : undefined,
      //@ts-ignore
      customFields: formatSubmitCustomFieldData(data?.customFields || {})
    }).then(rs => {
      if (rs.error) {
        throw rs.error
      }
      setToast({
        open: true,
        type: 'success',
        title: `${t('notification:placementUpdated')}`,
        classNameConfig: {
          viewport: 'mb-[48px]'
        }
      })
      return rs.data?.placementsUpdate.placement
    })
  })

  return {
    EditPlacementModalComponent,
    openEditPlacementModel
  }
}
