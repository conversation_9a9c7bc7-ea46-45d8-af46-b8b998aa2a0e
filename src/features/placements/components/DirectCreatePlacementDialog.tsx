'use client'

import { formatISO } from 'date-fns'
import type { ComponentProps } from 'react'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useMutation } from 'urql'

import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import IconWrapper from '~/core/ui/IconWrapper'
import { Tooltip } from '~/core/ui/Tooltip'

import MutationMarkAsHired from '~/lib/features/jobs/graphql/submit-mark-as-hired'
import usePermissionPlacement from '~/lib/features/permissions/hooks/use-permission-placement'
import type { IPlacementCustomField } from '~/lib/features/placements/types/management-page-type'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import QueryAgencyPlacementFormFieldsList from '~/lib/features/settings/profile-fields/graphql/query-agency-placement-form-fields'
import { formatInitialValueCustomField, formatSubmitCustomFieldData } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import createModalHook from '~/lib/hooks/create-modal-hooks'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import type { IPlacement } from '../placement'
import type { ICreateEditPlacement } from '../schema'
import PlacementForm from './PlacementForm'
import ToolTipOnOverflow from './ToolTipOnOverflow'

interface OpenParamsProps {
  onPlacementCreated: (placement: IPlacement) => void
  header?: { candidateName?: string; jobTitle?: string; companyName?: string }
  applicant: ComponentProps<typeof PlacementForm>['applicant']
  profileId?: ComponentProps<typeof PlacementForm>['profileId']
  defaultValues?: { applicationId?: number; jobStageId?: number }
}

const DirectCreatePlacementDialog = ({
  open,
  setOpen,
  onSubmit,
  openParams
}: {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit?: (data: ICreateEditPlacement) => Promise<IPlacement>
  openParams?: OpenParamsProps
}) => {
  const { t } = useTranslation()
  const user = useBoundStore(state => state.user)
  const [isLoading, setLoading] = useState(false)
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const isShowPlacementFeature = isFeatureEnabled(PLAN_FEATURE_KEYS.placement) && isUnLockFeature(PLAN_FEATURE_KEYS.placement)
  const { canAccessPage: canAccessPlacement } = usePermissionPlacement()
  const isAccessPlacement = isShowPlacementFeature && canAccessPlacement

  const { data: dataPlacementCustomFields } = useQueryGraphQL({
    query: QueryAgencyPlacementFormFieldsList,
    variables: {},
    shouldPause: !isAccessPlacement || !open
  })

  const placementCustomFields = useMemo(() => {
    return (dataPlacementCustomFields?.placementFieldsSettingList || []).filter((field: IPlacementCustomField) => field.field_level == 'custom')
  }, [dataPlacementCustomFields])

  const placementSystemFields = useMemo(() => {
    return (dataPlacementCustomFields?.placementFieldsSettingList || []).filter((field: IPlacementCustomField) => field.field_level == 'system')
  }, [dataPlacementCustomFields])

  const customFieldsFormatted = useMemo(() => {
    return formatInitialValueCustomField(placementCustomFields)
  }, [placementCustomFields])

  const defaultValue = useMemo<ICreateEditPlacement>(
    () =>
      ({
        //@ts-ignore
        applicantId: openParams?.defaultValues?.applicationId?.toString(),
        //@ts-ignore
        jobStageId: openParams?.defaultValues?.jobStageId?.toString(),
        typeOfSalary: 'annually',
        typeOfFee: 'percentage',
        currencyOfRevenue: user?.currentTenant?.currency,
        profitSplits: [
          {
            user_id: {
              value: user.id?.toString() as string,
              avatar: user.avatar,
              avatarVariants: user.avatarVariants,
              supportingObj: { name: user.fullName }
            },
            profit_percentage: '100'
          }
        ],
        customFields: customFieldsFormatted
      }) as ICreateEditPlacement,
    [openParams?.defaultValues, customFieldsFormatted, user]
  )

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
      isDivider={true}
      isPreventAutoFocusDialog={true}
      size="md"
      label={`${t('placements:createPlacement')}`}
      description={
        <div className="mb-5">
          <div className="flex items-center">
            <div className="mr-2 shrink-0">
              <IconWrapper size={16} name="User" />
            </div>
            <Tooltip content={openParams?.header?.candidateName}>
              <div className="line-clamp-1 text-sm text-gray-900">{openParams?.header?.candidateName}</div>
            </Tooltip>
          </div>
          <div className="mt-1.5 flex items-center">
            <div className="mr-2 shrink-0">
              <IconWrapper size={16} name="Briefcase" />
            </div>
            <ToolTipOnOverflow text={openParams?.header?.jobTitle} className="max-w-[200px] shrink-0 truncate text-sm text-gray-900" />
            <div className="mr-2 ml-2 h-0.5 w-0.5 shrink-0 rounded-full bg-gray-400"></div>
            <ToolTipOnOverflow className="shrink-1 truncate" text={openParams?.header?.companyName} />
          </div>
        </div>
      }
      headingClassName="tablet:pb-0"
    >
      {openParams?.defaultValues && (
        <PlacementForm
          placementSystemFields={placementSystemFields}
          placementCustomFields={placementCustomFields}
          applicant={openParams?.applicant}
          profileId={openParams?.profileId}
          hiddenFields={['status']}
          onSubmit={data => {
            if (onSubmit) {
              setLoading(true)

              return onSubmit(data).then(placement => {
                setLoading(false)
                setOpen(false)
                openParams?.onPlacementCreated(placement)
              })
            }

            return Promise.reject('Handler not provided')
          }}
          defaultValue={defaultValue}
        >
          <div className="mt-6 flex items-center justify-end space-x-3">
            <Button label={`${t('button:cancel')}`} size="sm" type="secondary" onClick={() => setOpen(false)} />
            <Button isDisabled={isLoading} isLoading={isLoading} size="sm" label={`${t('interview:email_modal:save')}`} htmlType="submit" />
          </div>
        </PlacementForm>
      )}
    </Dialog>
  )
}

export const useDirectCreatePlacementModel = () => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const { actionPlacement } = usePermissionPlacement()

  const [{ fetching: creatingRequisition }, createPlacement] = useMutation(MutationMarkAsHired)
  const { ModalComponent: PlacementModalComponent, openModal } = createModalHook(DirectCreatePlacementDialog)(data => {
    return createPlacement({
      id: +data.applicantId,
      jobStageId: +data.jobStageId,
      hiredDate: formatISO(data.hiredDate),
      onboardDate: data.onboardDate ? formatISO(data.onboardDate) : undefined,
      endOfProbationDate: data.endOfProbationDate ? formatISO(data.endOfProbationDate) : undefined,
      salary: data.salary ? +data.salary : undefined,
      typeOfSalary: data.typeOfSalary,
      currencyOfSalary: 'USD',
      fee: data.fee ? +data.fee : undefined,
      typeOfFee: data.typeOfFee,
      revenue: data.revenue ? +data.revenue : undefined,
      currencyOfRevenue: data.currencyOfRevenue ? data.currencyOfRevenue : undefined,
      profitSplits: data.profitSplits.map(item => ({
        userId: +item.user_id?.value,
        profitPercentage: +item.profit_percentage
      })),
      hiredById: Number(data?.hiredBy?.value),
      noteContent: data?.notes?.trim() ? data?.notes?.trim() : undefined,
      //@ts-ignore
      customFields: formatSubmitCustomFieldData(data?.customFields || {})
    }).then(rs => {
      if (rs.error) {
        throw rs.error
      }
      const newPlacement = rs.data?.applicantsMarkAsHired?.applicant?.placement || {}
      return {
        ...newPlacement,
        editablePlacement: true
      }
    })
  })

  const openPlacementWithPermission = useCallback(
    (data: OpenParamsProps) => {
      if (!actionPlacement?.create) {
        setToast({
          open: true,
          type: 'error',
          title: `${t('placements:permission:createPlacement')}`
        })
        return
      }

      openModal(data)
    },
    [actionPlacement?.create]
  )

  return {
    PlacementModalComponent,
    openPlacementModel: openPlacementWithPermission
  }
}

export default DirectCreatePlacementDialog
