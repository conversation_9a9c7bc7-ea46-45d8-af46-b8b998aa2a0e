import DOMPurify from 'dompurify'
import type { FC, ReactNode } from 'react'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { Avatar } from '~/core/ui/Avatar'
import type { IColorBadgeType } from '~/core/ui/Badge'
import { Badge } from '~/core/ui/Badge'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import type { LucideIconName } from '~/core/ui/IconWrapper'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import type { ISelectOption } from '~/core/ui/Select'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { defaultFormatDate } from '~/core/utilities/format-date'

import usePermissionPlacement from '~/lib/features/permissions/hooks/use-permission-placement'
import { formatMoneyWithComma } from '~/lib/features/placements/utilities'
import { PLACEMENT_COLOR_STATUS } from '~/lib/features/placements/utilities/enum'
import createModalHook from '~/lib/hooks/create-modal-hooks'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useToastStore from '~/lib/store/toast'

import type { IPlacement } from '../placement'
import { useDirectEditPlacementModel } from './DirectEditPlacementDialog'
import { useEditPlacementModel } from './EditPlacementDialog'
import { useOpenPlacementNotesDialog } from './PlacementNotesDialog'
import ToolTipOnOverflow from './ToolTipOnOverflow'

const RowInfoWrapper: FC<{
  hide?: boolean
  nameIcon: LucideIconName
  labelName: string
  content: string | ReactNode
  classNameLeftView?: string
}> = ({ nameIcon, labelName, content, classNameLeftView, hide = false }) => {
  return !hide ? (
    <div className="tablet:grid-cols-[132px_1fr] tablet:gap-4 mt-3 grid w-full grid-cols-1 gap-[2px]">
      <div className={`flex flex-row items-center self-start ${classNameLeftView || ''}`}>
        <IconWrapper size={16} className="flex-none text-gray-600" name={nameIcon} />
        <TypographyText className="ml-2 text-sm font-normal text-gray-700">{labelName}</TypographyText>
      </div>
      <div className="tablet:w-full text-sm text-gray-900">{content}</div>
    </div>
  ) : (
    <></>
  )
}
type OpenParams = {
  placement: IPlacement
  header: { candidateName?: string; jobTitle?: string; companyName?: string }
  onPlacementEdited: (placement: IPlacement) => Promise<any>
}
const PlacementDetailDialog = ({
  open,
  setOpen,
  openParams
}: {
  open: boolean
  setOpen: (open: boolean, param?: OpenParams) => void
  openParams?: OpenParams
}) => {
  const { t, i18n } = useTranslation()
  const placement = openParams?.placement
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const { EditPlacementModalComponent, openEditPlacementModel } = useEditPlacementModel()
  const { EditDirectPlacementModalComponent, openDirectEditPlacementModel } = useDirectEditPlacementModel()
  const { PlacementNotesModalComponent, openPlacementNotesModel } = useOpenPlacementNotesDialog()
  const { fullPermission: fullPermissionPlacement } = usePermissionPlacement()

  const placementStatus = useEnumsData({
    enumType: 'PlacementStatus',
    locale: i18n.language
  })
  const typesOfSalary = useEnumsData({
    enumType: 'PlacementTypeOfSalary',
    locale: i18n.language
  })
  const { setToast } = useToastStore()

  const isShowEditButton = useMemo(() => {
    return fullPermissionPlacement || placement?.ownedPlacement || placement?.editablePlacement
  }, [fullPermissionPlacement, placement?.ownedPlacement, placement?.editablePlacement])

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
      isDivider={true}
      isPreventAutoFocusDialog={true}
      size="sm"
      label={`${t('placements:placementDetail')}`}
      description={
        <div>
          {isCompanyKind ? <EditPlacementModalComponent /> : <EditDirectPlacementModalComponent />}
          <div className="flex items-center">
            <div className="mr-2">
              <IconWrapper size={16} name="User" />
            </div>
            <Tooltip content={openParams?.header?.candidateName}>
              <div className="line-clamp-1 text-sm text-gray-900">{openParams?.header?.candidateName}</div>
            </Tooltip>
          </div>
          <div className="mt-1.5 flex items-center">
            <div className="mr-2 shrink-0">
              <IconWrapper size={16} name="Briefcase" />
            </div>
            <ToolTipOnOverflow text={openParams?.header?.jobTitle} className="max-w-[200px] shrink-0 truncate text-sm text-gray-900" />
            <div className="mr-2 ml-2 h-0.5 w-0.5 shrink-0 rounded-full bg-gray-400"></div>
            <ToolTipOnOverflow className="shrink-1 truncate" text={openParams?.header?.companyName} />
          </div>
        </div>
      }
    >
      {openParams?.placement && (
        <div>
          <RowInfoWrapper
            hide={!placement?.permittedFields?.status.value}
            nameIcon="Zap"
            labelName={`${t('placements:management:table:placement_status')}`}
            content={
              <div className="flex text-sm text-gray-900">
                <Badge type="dotLeading" color={PLACEMENT_COLOR_STATUS(placement?.permittedFields?.status.value as string).color as IColorBadgeType}>
                  {placementStatus.find((item: ISelectOption) => item.value === placement?.permittedFields?.status.value)?.supportingObj?.name}
                </Badge>
              </div>
            }
          />
          <RowInfoWrapper
            hide={!placement?.permittedFields?.hiredDate.value}
            nameIcon="CalendarCheck2"
            labelName={`${t('placements:management:table:hired_date')}`}
            content={
              <div className="text-sm text-gray-900">
                {placement?.permittedFields?.hiredDate.value && defaultFormatDate(new Date(placement?.permittedFields?.hiredDate.value))}
              </div>
            }
          />
          <RowInfoWrapper
            hide={!placement?.permittedFields?.hiredBy.value}
            nameIcon="UserCheck"
            labelName={`${t('placements:management:table:hiredBy')}`}
            content={
              <div className="flex items-center">
                <Avatar
                  size="xs"
                  color={placement?.permittedFields?.hiredBy?.value?.defaultColour}
                  src={placement?.permittedFields?.hiredBy?.value?.avatarVariants?.thumb?.url}
                  alt={placement?.permittedFields?.hiredBy?.value?.fullName}
                />
                <Tooltip content={placement?.permittedFields?.hiredBy?.value?.fullName}>
                  <div className="ml-2 line-clamp-1 text-sm font-medium text-gray-900">{placement?.permittedFields?.hiredBy?.value?.fullName}</div>
                </Tooltip>
              </div>
            }
          />
          <RowInfoWrapper
            hide={!placement?.permittedFields?.onboardDate?.value && !placement?.permittedFields?.endOfProbationDate?.value}
            nameIcon="Calendar"
            labelName={`${t('placements:management:table:start_end_date')}`}
            content={
              <div className="text-sm text-gray-900">
                {placement?.permittedFields?.onboardDate?.value && defaultFormatDate(new Date(placement?.permittedFields?.onboardDate?.value))}{' '}
                {placement?.permittedFields?.endOfProbationDate?.value &&
                  `- ${defaultFormatDate(new Date(placement?.permittedFields?.endOfProbationDate?.value))}`}
              </div>
            }
          />
          <RowInfoWrapper
            hide={!placement?.permittedFields?.salary?.value}
            nameIcon="DollarSign"
            labelName={`${t('placements:management:table:salary')}`}
            content={
              <div className="text-sm break-all text-gray-900">
                {formatMoneyWithComma(Number(placement?.permittedFields?.salary?.value))} {placement?.permittedFields?.currencyOfSalary?.value}{' '}
                <span className="text-sm text-gray-600">
                  (
                  {
                    typesOfSalary.find((typeSalary: ISelectOption) => typeSalary?.value === placement?.permittedFields?.typeOfSalary?.value)
                      ?.supportingObj.name
                  }
                  )
                </span>
              </div>
            }
          />
          <RowInfoWrapper
            hide={!placement?.permittedFields?.fee?.value}
            nameIcon="Scroll"
            labelName={`${t('placements:management:table:fee')}`}
            content={
              <div className="text-sm break-all text-gray-900">
                {{
                  percentage: `${formatMoneyWithComma(Number(placement?.permittedFields?.fee?.value))} %`,
                  months: `${t('placements:fee_months', {
                    num: `${formatMoneyWithComma(Number(placement?.permittedFields?.fee?.value))}`
                  })}`
                }[placement?.permittedFields?.typeOfFee?.value as string] ||
                  `${formatMoneyWithComma(Number(placement?.permittedFields?.fee?.value))} ${placement?.permittedFields?.currencyOfRevenue?.value}`}
              </div>
            }
          />
          <RowInfoWrapper
            hide={!placement?.permittedFields?.revenue?.value}
            nameIcon="DollarSign"
            labelName={`${t('placements:management:table:revenue')}`}
            content={
              <div className="text-sm break-all text-gray-900">
                {formatMoneyWithComma(Number(placement?.permittedFields?.revenue?.value))} {placement?.permittedFields?.currencyOfRevenue?.value}
              </div>
            }
          />
          <RowInfoWrapper
            hide={!placement?.permittedFields?.profitSplits?.value?.length}
            nameIcon="Users"
            labelName={`${t('placements:management:table:profit_split')}`}
            content={placement?.permittedFields?.profitSplits?.value?.map((split, index) => (
              <div key={index} className="mt-2 first:mt-0">
                <div className="flex items-center">
                  <Avatar size="xs" color={split?.user?.defaultColour} src={split?.user?.avatarVariants?.thumb?.url} alt={split?.user?.fullName} />
                  <Tooltip content={split?.user?.fullName}>
                    <div className="ml-2 line-clamp-1 text-sm font-medium text-gray-900">{split?.user?.fullName}</div>
                  </Tooltip>
                  <div className="ml-2 h-1 w-1 flex-none animate-bounce rounded-full bg-gray-400"></div>
                  <div className="ml-2 text-sm font-medium text-gray-900">{split?.profitPercentage}%</div>
                </div>
              </div>
            ))}
          />
          <RowInfoWrapper
            hide={placement?.permittedFields?.comments?.value?.length === 0}
            nameIcon="FileEdit"
            labelName={`${t('placements:management:table:notes')}`}
            content={
              placement?.permittedFields?.comments?.value?.[0] && (
                <div>
                  <PlacementNotesModalComponent />
                  <div
                    className="mb-1 line-clamp-3 text-sm break-all text-gray-900"
                    dangerouslySetInnerHTML={{
                      __html: DOMPurify.sanitize(String(placement?.permittedFields?.comments?.value?.[0]?.content))
                    }}
                  />
                  <TextButton
                    size="md"
                    label={`${t('button:viewMore')}`}
                    underline={false}
                    onClick={() =>
                      openPlacementNotesModel({
                        header: {
                          candidateName: openParams?.header?.candidateName,
                          jobTitle: openParams?.header?.jobTitle,
                          companyName: openParams?.header?.companyName
                        },
                        comments: placement?.permittedFields?.comments?.value
                      })
                    }
                  />
                </div>
              )
            }
          />
          {/* {placement?.customFields?.map((field, index) => {
            const formatDate = field?.value as {
              year?: number
              month?: number
              date?: number
            }
            let label = ''
            let findCustomField = placementCustomFields.filter(
              (f: IPlacementCustomField) =>
                String(f.custom_setting_id) === String(field.customSettingId)
            )?.[0]

            if (findCustomField) {
              const selectOptions = findCustomField?.select_options
              const fieldKind = String(findCustomField.field_kind)
              if (['string', 'number'].includes(fieldKind)) {
                label = field.value
              } else if (fieldKind === 'array') {
                label = (selectOptions || []).find(
                  ({ key }: { key: string }) =>
                    String(key) === String(field?.selectedOptionKeys?.[0])
                )?.value
              } else if (fieldKind === 'boolean') {
                label = !!field?.value ? 'Yes' : 'No'
              } else if (fieldKind === 'date') {
                label =
                  formatDate?.year && typeof formatDate === 'object'
                    ? !!formatDate?.year &&
                      !formatDate.month &&
                      !formatDate.date
                      ? formatDate.year.toString()
                      : defaultFormatDate(formatDatePickerToDate(formatDate))
                    : '-'
              }
            }

            return (
              <RowInfoWrapper
                key={index}
                nameIcon={mappingPlacementCustomFieldIcon(
                  findCustomField?.field_kind || ''
                )}
                labelName={findCustomField?.field_name || ''}
                content={
                  <div className="break-all text-sm text-gray-900">{label}</div>
                }
              />
            )
          })} */}
          <If condition={isShowEditButton}>
            <div className="flex justify-end pt-6">
              <Button
                icon="leading"
                iconMenus="Edit3"
                label={`${t('button:edit')}`}
                type="primary"
                size="sm"
                onClick={() => {
                  ;(isCompanyKind ? openEditPlacementModel : openDirectEditPlacementModel)({
                    header: {
                      candidateName: openParams?.header?.candidateName,
                      jobTitle: openParams?.header?.jobTitle,
                      companyName: openParams?.header?.companyName
                    },
                    placement: openParams?.placement,
                    onPlacementEdited: placement => {
                      setOpen(true, { ...openParams, placement })
                      openParams?.onPlacementEdited(placement)
                      setToast({
                        open: true,
                        type: 'success',
                        title: `${t('notification:placementUpdated')}`
                      })
                    }
                  })
                  // setOpen(false)
                }}
              />
            </div>
          </If>
        </div>
      )}
    </Dialog>
  )
}
export const useOpenPlacementDetailDialog = () => {
  const { ModalComponent: PlacementDetailModalComponent, openModal: openPlacementDetailModel } = createModalHook(PlacementDetailDialog)(data => {
    return Promise.resolve()
  })

  return {
    PlacementDetailModalComponent,
    openPlacementDetailModel
  }
}
