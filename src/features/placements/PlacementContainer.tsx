'use client'

import type { FC } from 'react'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import configuration from '~/configuration'
import { Badge } from '~/core/ui/Badge'
import Empty from '~/core/ui/Empty'
import { cn } from '~/core/ui/utils'
import { ACTIONS_PERMISSIONS, canAccessFeature, PERMISSIONS_LIST } from '~/core/utilities/feature-permission'

import { DESC_SORTING, OPERATOR } from '~/lib/features/candidates/utilities/enum'
import QueryDirectPlacementList from '~/lib/features/placements/graphql/query-placement-direct-list'
import { useUserPlacementColumnsSetting } from '~/lib/features/placements/hooks/use-query-user-placement-setting-cols'
import { mappingAdvancedFilterPlacement } from '~/lib/features/placements/mapping/placement-filter-mapping'
import type { IFilterPlacement } from '~/lib/features/placements/types/management-page-type'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import type { FieldSettingType } from '~/components/DisplayConfig'
import { withLayoutGrid } from '~/components/Layout/LayoutGrid'
import FilterPlacementManagement from '~/components/Placements/FilterPlacementManagement'
import PlacementTable from '~/components/Placements/PlacementTable'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import type { IPlacementFilterCondition } from '~/features/placements/PlacementAgencyContainer'

const DEFAULT_FILTER = {
  page: 1,
  operator: OPERATOR.and
  // limit: configuration.defaultPageSize
}

const PlacementContainer: FC = () => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const user = useBoundStore(state => state.user)

  const { data: customFieldProfileViewData } = useCustomFieldSettingByUser({
    objectKind: 'placement'
  })
  const mappingsFilterProfileField = (customFieldProfileViewData || [])?.filter(item => item.visibility)

  const [configUserDisplay, setConfigUserDisplay] = useState<FieldSettingType[]>()

  const { userSetting, resetAsDefault, updateFieldsSetting, isDefaultUserSetting } = useUserPlacementColumnsSetting({
    setToast
  })

  const [count, setCount] = useState<number>(0)
  const [filter, changeFilter] = useState<IFilterPlacement | undefined>(DEFAULT_FILTER)

  const onApplyFilter = () => {
    if (filter) {
      const params = mappingAdvancedFilterPlacement(filter, user)
    }
  }

  const onClearFilter = () => {
    changeFilter(DEFAULT_FILTER)
  }

  const [filterConditions, setFilterConditions] = useState<IPlacementFilterCondition[]>([])

  const [sorting, setSorting] = useState<{
    onboardDate?: string
  }>({
    onboardDate: DESC_SORTING
  })

  useEffect(() => {
    setConfigUserDisplay(userSetting?.placementDisplay)
  }, [userSetting?.placementDisplay])

  const onReOrderUserDisplay = useCallback(
    (data: FieldSettingType[]) => {
      setConfigUserDisplay(data)
      return updateFieldsSetting(data)
    },
    [updateFieldsSetting]
  )

  const { data, refetch, fetchPagination, forceChangeCurrentPage, isFetching } = usePaginationGraphPage({
    queryDocumentNode: QueryDirectPlacementList,
    queryKey: 'my-placements-management',
    filter: (() => {
      const params = mappingAdvancedFilterPlacement(filter || {}, user)
      delete params.relatedObjects
      return {
        ...params,
        limit: configuration.defaultPageSize,
        sorting
      }
    })()
  })

  const topSpace = useClassBasedTopSpace({
    34: 'h-full',
    default: 'h-screen'
  })

  return (
    <div className={cn('flex flex-col', topSpace)}>
      <div className="flex-none px-6">
        <div className="flex h-[56px] items-center justify-between border-b border-b-gray-100">
          <div className="flex items-center">
            <p className="mr-2 text-lg font-medium text-gray-900 dark:text-gray-200">{t('placements:management:title')}</p>
            {data?.meta?.totalRowCount && data.meta.totalRowCount > 0 ? (
              <Badge radius="circular" size="md">
                {data.meta.totalRowCount}
              </Badge>
            ) : null}
          </div>
          <FilterPlacementManagement
            filter={filter}
            onApplyFilter={onApplyFilter}
            onClearFilter={onClearFilter}
            changeFilter={changeFilter}
            fields={configUserDisplay}
            isDefaultFields={isDefaultUserSetting}
            onReOrder={onReOrderUserDisplay}
            resetAsDefault={resetAsDefault}
            refetchFilterList={refetch}
            filterConditions={filterConditions}
            setFilterConditions={setFilterConditions}
          />
        </div>
      </div>
      <div className="flex-1">
        {data?.meta?.totalRowCount === 0 && filter?.isFilterTouched ? (
          <div style={{ minHeight: 'calc(100vh - 56px)' }} className="flex items-center">
            <Empty
              type="empty-search"
              title={t('placements:management:table:emptySearch:title') || ''}
              description={t('placements:management:table:emptySearch:description') || ''}
              buttonTitle={t('button:clearFilter') || ''}
              onClick={() => changeFilter(DEFAULT_FILTER)}
            />
          </div>
        ) : (
          <div className="h-full pl-6">
            <PlacementTable
              configUserDisplay={configUserDisplay}
              classNameTable="border-t-0"
              filter={filter}
              data={data}
              fetcher={{
                fetchPagination,
                forceChangeCurrentPage
              }}
              isFetching={isFetching}
              refetch={refetch}
              sorting={sorting}
              setSorting={setSorting}
              mappingsFilterProfileField={mappingsFilterProfileField}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default withSubscriptionPlanLockFearture(
  withPermissionFeatureProvider(
    {
      checkAccessPermission: canAccessFeature,
      keyModule: [PERMISSIONS_LIST.placement.keyModule],
      keyModuleObject: [PERMISSIONS_LIST.placement.objects.placement.keyModuleObject],
      action: ACTIONS_PERMISSIONS.show
    },
    withLayoutGrid(PlacementContainer)
  ),
  PLAN_FEATURE_KEYS.placement as FeatureName
)
